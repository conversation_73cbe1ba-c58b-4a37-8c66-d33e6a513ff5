#!/bin/bash

# Simple Migration Folder Scanner
# Jose <PERSON> (JoseTusabe)

echo "🔍 Searching for 'for-migration' folder..."

# Search in common locations
SEARCH_PATHS=(
    "$HOME/Desktop"
    "$HOME/Documents" 
    "$HOME/Downloads"
    "$HOME/Projects"
    "$HOME"
)

FOUND_FOLDERS=()

for path in "${SEARCH_PATHS[@]}"; do
    if [ -d "$path" ]; then
        echo "Searching in: $path"
        while IFS= read -r -d '' folder; do
            FOUND_FOLDERS+=("$folder")
        done < <(find "$path" -name "*migration*" -type d -print0 2>/dev/null)
    fi
done

echo ""
echo "📁 Found folders containing 'migration':"
for folder in "${FOUND_FOLDERS[@]}"; do
    echo "  - $folder"
done

echo ""
echo "📊 Let's check what's in your Desktop:"
ls -la "$HOME/Desktop/" | head -20

echo ""
echo "🔍 Looking for any folders with themes or projects:"
find "$HOME/Desktop" -type d -name "*theme*" -o -name "*project*" -o -name "*web*" -o -name "*site*" 2>/dev/null | head -10

echo ""
echo "📋 Looking for common project files:"
find "$HOME/Desktop" -name "package.json" -o -name "composer.json" -o -name "index.php" -o -name "*.css" 2>/dev/null | head -20
