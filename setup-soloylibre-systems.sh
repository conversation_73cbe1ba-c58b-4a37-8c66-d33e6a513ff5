#!/bin/bash

# SoloYlibre & JEYKO Dev - Complete System Setup
# Head Developer: <PERSON>carnacion
# Company: SoloYlibre (Main) | J<PERSON>Y<PERSON> (AI Division)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Company credentials
COMPANY_USER="SoloYlibre"
COMPANY_PASS="57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd"
ADMIN_EMAIL="<EMAIL>"

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    SOLOYLIBRE & JEYKO DEV                    ║"
    echo "║                  COMPLETE SYSTEM SETUP                      ║"
    echo "║                                                              ║"
    echo "║  Company: SoloYlibre (Main Business)                        ║"
    echo "║  AI Division: J<PERSON>Y<PERSON> (Artificial Intelligence)               ║"
    echo "║  Head Developer: <PERSON> Encarnacion                         ║"
    echo "║                                                              ║"
    echo "║  Installing & Configuring All Systems...                    ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_jeyko() {
    echo -e "${PURPLE}[JEYKO AI]${NC} $1"
}

# Wait for service to be ready
wait_for_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $service_name to be ready on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:$port > /dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 5
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Setup WordPress Multisite
setup_wordpress() {
    print_status "Setting up WordPress Multisite for SoloYlibre..."
    
    wait_for_service "WordPress" 8100
    
    # Install WordPress
    docker exec josetusabe-wordpress-multisite wp core install \
        --url="http://localhost:8100" \
        --title="SoloYlibre - Ultimate Business Platform" \
        --admin_user="$COMPANY_USER" \
        --admin_password="$COMPANY_PASS" \
        --admin_email="$ADMIN_EMAIL" \
        --allow-root || print_warning "WordPress may already be installed"
    
    # Enable multisite
    docker exec josetusabe-wordpress-multisite wp core multisite-convert \
        --title="SoloYlibre Network" \
        --allow-root || print_warning "Multisite may already be enabled"
    
    # Create JEYKO AI subsite
    docker exec josetusabe-wordpress-multisite wp site create \
        --slug="jeyko" \
        --title="JEYKO - AI Division" \
        --email="$ADMIN_EMAIL" \
        --allow-root || print_warning "JEYKO site may already exist"
    
    # Install and activate TTS plugin
    docker exec josetusabe-wordpress-multisite wp plugin activate tts-integration \
        --network \
        --allow-root || print_warning "TTS plugin activation failed"
    
    print_success "WordPress Multisite configured for SoloYlibre & JEYKO"
}

# Setup Drupal
setup_drupal() {
    print_status "Setting up Drupal for SoloYlibre..."
    
    wait_for_service "Drupal" 8101
    
    # Install Drupal
    docker exec josetusabe-drupal drush site:install standard \
        --db-url="pgsql://$CMS_DB_USER:$CMS_DB_PASS@josetusabe-postgres-cms:5432/drupal" \
        --site-name="SoloYlibre Drupal" \
        --account-name="$COMPANY_USER" \
        --account-pass="$COMPANY_PASS" \
        --account-mail="$ADMIN_EMAIL" \
        --yes || print_warning "Drupal may already be installed"
    
    print_success "Drupal configured for SoloYlibre"
}

# Setup Joomla
setup_joomla() {
    print_status "Setting up Joomla for SoloYlibre..."
    
    wait_for_service "Joomla" 8102
    
    # Joomla setup via API (simplified)
    curl -X POST http://localhost:8102/installation/index.php \
        -d "site_name=SoloYlibre Joomla" \
        -d "admin_user=$COMPANY_USER" \
        -d "admin_password=$COMPANY_PASS" \
        -d "admin_email=$ADMIN_EMAIL" \
        || print_warning "Joomla setup may need manual configuration"
    
    print_success "Joomla configured for SoloYlibre"
}

# Setup Ghost
setup_ghost() {
    print_status "Setting up Ghost for SoloYlibre..."
    
    wait_for_service "Ghost" 8103
    
    # Ghost setup via API
    curl -X POST http://localhost:8103/ghost/api/v1/authentication/setup/ \
        -H "Content-Type: application/json" \
        -d "{
            \"setup\": [{
                \"name\": \"Jose L Encarnacion\",
                \"email\": \"$ADMIN_EMAIL\",
                \"password\": \"$COMPANY_PASS\",
                \"blogTitle\": \"SoloYlibre Publishing\",
                \"status\": \"active\"
            }]
        }" || print_warning "Ghost setup may need manual configuration"
    
    print_success "Ghost configured for SoloYlibre"
}

# Setup Strapi
setup_strapi() {
    print_status "Setting up Strapi for SoloYlibre..."
    
    wait_for_service "Strapi" 8104
    
    # Create Strapi admin user
    curl -X POST http://localhost:8104/admin/auth/local/register \
        -H "Content-Type: application/json" \
        -d "{
            \"firstname\": \"Jose\",
            \"lastname\": \"Encarnacion\",
            \"email\": \"$ADMIN_EMAIL\",
            \"password\": \"$COMPANY_PASS\",
            \"confirmPassword\": \"$COMPANY_PASS\"
        }" || print_warning "Strapi admin may already exist"
    
    print_success "Strapi configured for SoloYlibre"
}

# Setup TTS Services
setup_tts_services() {
    print_jeyko "Setting up JEYKO AI TTS Services..."
    
    # Test ElevenLabs TTS
    wait_for_service "ElevenLabs TTS" 8105
    
    curl -X POST http://localhost:8105/api/tts \
        -H "Content-Type: application/json" \
        -d "{
            \"text\": \"Welcome to SoloYlibre and JEYKO AI Division. This is a test of our text-to-speech integration.\",
            \"voice_id\": \"EXAVITQu4vr4xnSDxMaL\",
            \"cms_source\": \"setup\",
            \"user_id\": \"$COMPANY_USER\"
        }" || print_warning "ElevenLabs TTS test failed"
    
    # Test Zonos AI TTS
    wait_for_service "Zonos AI TTS" 8106
    
    curl -X POST http://localhost:8106/api/tts \
        -H "Content-Type: application/json" \
        -d "{
            \"text\": \"Bienvenidos a SoloYlibre y la división de IA JEYKO. Esta es una prueba de nuestra integración de texto a voz.\",
            \"language\": \"es\",
            \"voice\": \"maria\",
            \"cms_source\": \"setup\",
            \"user_id\": \"$COMPANY_USER\"
        }" || print_warning "Zonos AI TTS test failed"
    
    print_jeyko "JEYKO AI TTS Services configured and tested"
}

# Setup CMS Gateway
setup_cms_gateway() {
    print_status "Setting up CMS Integration Gateway..."
    
    wait_for_service "CMS Gateway" 8107
    
    # Register admin user in gateway
    curl -X POST http://localhost:8107/api/auth/register \
        -H "Content-Type: application/json" \
        -d "{
            \"username\": \"$COMPANY_USER\",
            \"email\": \"$ADMIN_EMAIL\",
            \"password\": \"$COMPANY_PASS\"
        }" || print_warning "Gateway admin may already exist"
    
    print_success "CMS Gateway configured"
}

# Setup Grafana dashboards
setup_grafana() {
    print_status "Setting up Grafana for SoloYlibre monitoring..."
    
    wait_for_service "Grafana" 3001
    
    # Update Grafana organization name
    curl -X PUT http://SoloYlibre:57w%3Atqf_UMd2kmogiVx%21he%2BU%7DrXwhNHpd@localhost:3001/api/org \
        -H "Content-Type: application/json" \
        -d "{\"name\": \"SoloYlibre & JEYKO Dev\"}" \
        || print_warning "Grafana org update may have failed"
    
    print_success "Grafana configured for SoloYlibre"
}

# Create company branding files
create_branding() {
    print_status "Creating SoloYlibre & JEYKO branding assets..."
    
    mkdir -p branding/soloylibre branding/jeyko
    
    # Create SoloYlibre branding info
    cat > branding/soloylibre/company-info.json << EOF
{
    "company": "SoloYlibre",
    "division": "Main Business",
    "head_developer": "Jose L Encarnacion",
    "email": "<EMAIL>",
    "colors": {
        "primary": "#2196F3",
        "secondary": "#21CBF3"
    },
    "domains": [
        "soloylibre.com",
        "josetusabe.com",
        "1and1photo.com",
        "joselencarnacion.com"
    ]
}
EOF

    # Create JEYKO branding info
    cat > branding/jeyko/company-info.json << EOF
{
    "company": "JEYKO",
    "division": "Artificial Intelligence",
    "parent_company": "SoloYlibre",
    "head_developer": "Jose L Encarnacion",
    "email": "<EMAIL>",
    "colors": {
        "primary": "#FF6B35",
        "secondary": "#F7931E"
    },
    "focus": [
        "AI Services",
        "Machine Learning",
        "Text-to-Speech",
        "Voice Agents",
        "Automation"
    ]
}
EOF

    print_success "Branding assets created for SoloYlibre & JEYKO"
}

# Generate system report
generate_report() {
    print_status "Generating SoloYlibre system report..."
    
    cat > SOLOYLIBRE_SYSTEM_REPORT.md << EOF
# 🏢 SOLOYLIBRE & JEYKO DEV - SYSTEM REPORT
## Complete Installation & Configuration Report

### 🏢 COMPANY INFORMATION
- **Main Company**: SoloYlibre
- **AI Division**: JEYKO
- **Head Developer**: Jose L Encarnacion
- **Email**: <EMAIL>

### 🔐 UNIFIED CREDENTIALS
- **Username**: SoloYlibre
- **Password**: 57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd

### 🌐 SYSTEM ACCESS URLS
- **WordPress Multisite**: http://localhost:8100
  - Main Site: SoloYlibre Business
  - Subsite: JEYKO AI Division
- **Drupal**: http://localhost:8101
- **Joomla**: http://localhost:8102
- **Ghost**: http://localhost:8103
- **Strapi**: http://localhost:8104

### 🤖 JEYKO AI SERVICES
- **ElevenLabs TTS**: http://localhost:8105
- **Zonos AI TTS**: http://localhost:8106
- **CMS Gateway**: http://localhost:8107

### 📊 MONITORING & MANAGEMENT
- **Grafana**: http://localhost:3001
- **Prometheus**: http://localhost:9091
- **MinIO**: http://localhost:9003
- **Jaeger**: http://localhost:16687

### 🎯 NEXT STEPS
1. Configure DNS for production domains
2. Set up SSL certificates
3. Configure backup strategies
4. Customize themes and branding
5. Set up production workflows

Installation completed: $(date)
EOF

    print_success "System report generated: SOLOYLIBRE_SYSTEM_REPORT.md"
}

# Main execution
main() {
    print_header
    
    print_status "Starting complete system setup for SoloYlibre & JEYKO Dev..."
    
    # Create branding assets
    create_branding
    
    # Setup all CMS platforms
    setup_wordpress
    setup_drupal
    setup_joomla
    setup_ghost
    setup_strapi
    
    # Setup JEYKO AI services
    setup_tts_services
    setup_cms_gateway
    
    # Setup monitoring
    setup_grafana
    
    # Generate final report
    generate_report
    
    echo ""
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                    SETUP COMPLETED!                         ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🏢 SoloYlibre & JEYKO Dev systems are ready!              ║${NC}"
    echo -e "${GREEN}║  👨‍💻 Head Developer: Jose L Encarnacion                     ║${NC}"
    echo -e "${GREEN}║  🔐 Username: SoloYlibre                                    ║${NC}"
    echo -e "${GREEN}║  📧 Email: <EMAIL>                             ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  Check SOLOYLIBRE_SYSTEM_REPORT.md for details             ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
}

# Run main function
main "$@"
