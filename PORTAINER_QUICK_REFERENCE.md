# 🚀 Portainer Quick Reference Card

## 🌐 Access URLs
- **HTTP**: http://localhost:9000
- **HTTPS**: https://localhost:9443

## ⚡ Quick Commands
```bash
# Status check
./portainer-manager.sh status

# Start/Stop/Restart
./portainer-manager.sh start
./portainer-manager.sh stop
./portainer-manager.sh restart

# Logs and maintenance
./portainer-manager.sh logs
./portainer-manager.sh update
```

## 🐳 Your Current Environment
| Service | Port | Status |
|---------|------|--------|
| Portainer | 9000/9443 | ✅ Running |
| API Gateway | 8000 | ✅ Running |
| Redis | 6379 | ✅ Running |
| Jaeger | 16686 | ✅ Running |
| PostgreSQL | 5432 | ✅ Running |
| Web Server | 80/443 | ✅ Running |

## 🎯 First Steps
1. Open http://localhost:9000
2. Create admin account
3. Explore your containers
4. Try deploying a new app

## 🆘 Emergency Commands
```bash
# Force restart
docker restart portainer

# Check logs
docker logs portainer

# Remove and recreate
docker rm -f portainer && ./portainer-manager.sh start
```

---
*Professional 2000% IQ Setup Complete! 🎉*
