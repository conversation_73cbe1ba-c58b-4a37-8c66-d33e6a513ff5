#!/bin/bash

# DOCKER SERVICES SETUP - SoloYlibre & JEYKO Ultimate Environment
# Focus on Docker services installation and setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              DOCKER SERVICES SETUP                          ║"
    echo "║              SOLOYLIBRE & JEYKO                             ║"
    echo "║                                                              ║"
    echo "║  🐳 Setting up all Docker services and containers           ║"
    echo "║  🚀 Complete environment deployment                         ║"
    echo "║  👨‍💻 Head Developer: Jose L Encarnacion                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${CYAN}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check Docker status
check_docker() {
    print_status "Checking Docker status..."
    
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "Docker is running"
}

# Setup Docker networks
setup_docker_networks() {
    print_status "Setting up Docker networks..."
    
    # Create networks if they don't exist
    docker network create soloylibre-core 2>/dev/null || true
    docker network create soloylibre-cms 2>/dev/null || true
    docker network create soloylibre-ai 2>/dev/null || true
    docker network create soloylibre-app 2>/dev/null || true
    
    print_success "Docker networks configured"
}

# Start all compose files
start_all_services() {
    print_status "Starting all Docker Compose services..."
    
    # List of compose files to start
    compose_files=(
        "docker-compose.core.yml"
        "docker-compose.cms-suite.yml"
        "docker-compose.ai-services.yml"
        "docker-compose.ai-simple.yml"
        "docker-compose.business-saas.yml"
        "docker-compose.premium-saas.yml"
        "docker-compose.portainer.yml"
        "docker-compose.master.yml"
        "docker-compose.project1.yml"
        "docker-compose.project2.yml"
    )
    
    for compose_file in "${compose_files[@]}"; do
        if [ -f "$compose_file" ]; then
            print_status "Starting services from $compose_file..."
            docker-compose -f "$compose_file" up -d
            print_success "$compose_file services started"
            sleep 5  # Brief pause between services
        else
            print_warning "$compose_file not found, skipping"
        fi
    done
}

# Run existing setup scripts
run_setup_scripts() {
    print_status "Running existing setup scripts..."
    
    # List of setup scripts to run
    setup_scripts=(
        "install-portainer.sh"
        "install-postgresql-and-audit.sh"
        "auto-connect-postgresql.sh"
        "complete-auto-setup.sh"
        "fix-all-services.sh"
        "fix-database-connections.sh"
        "auto-complete-joomla-install.sh"
        "complete-joomla-fix.sh"
    )
    
    for script in "${setup_scripts[@]}"; do
        if [ -f "$script" ]; then
            print_status "Running $script..."
            chmod +x "$script"
            ./"$script" || print_warning "$script failed, continuing..."
            print_success "$script completed"
        else
            print_warning "$script not found, skipping"
        fi
    done
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to initialize..."
    
    # Wait for databases
    print_status "Waiting for databases to be ready..."
    sleep 30
    
    # Wait for web services
    print_status "Waiting for web services to be ready..."
    sleep 20
    
    print_success "Services initialization wait completed"
}

# Test service connectivity
test_services() {
    print_status "Testing service connectivity..."
    
    # Define services to test
    declare -A services=(
        ["pgAdmin"]="http://localhost:5050"
        ["WordPress"]="http://localhost:8100"
        ["Drupal"]="http://localhost:8101"
        ["Joomla"]="http://localhost:8102"
        ["Ghost"]="http://localhost:8103"
        ["Strapi"]="http://localhost:8104"
        ["AI Chat"]="http://localhost:3002"
        ["Themer"]="http://localhost:3004"
        ["Docmost"]="http://localhost:3003"
        ["NocoDB"]="http://localhost:8080"
        ["Grafana"]="http://localhost:3001"
        ["Prometheus"]="http://localhost:9091"
        ["MinIO"]="http://localhost:9003"
        ["Portainer"]="http://localhost:9000"
        ["Jaeger"]="http://localhost:16687"
        ["Traefik"]="http://localhost:8081"
        ["CMS Gateway"]="http://localhost:8107"
        ["ElevenLabs TTS"]="http://localhost:8105"
        ["Zonos AI TTS"]="http://localhost:8106"
    )
    
    echo ""
    echo "🔍 Service Health Check Results:"
    echo "================================"
    
    working_services=0
    total_services=${#services[@]}
    
    for service in "${!services[@]}"; do
        url="${services[$service]}"
        if curl -s --max-time 5 "$url" > /dev/null 2>&1; then
            echo -e "✅ $service: ${GREEN}WORKING${NC} ($url)"
            ((working_services++))
        else
            echo -e "❌ $service: ${RED}NOT RESPONDING${NC} ($url)"
        fi
    done
    
    echo ""
    echo "📊 Service Status Summary:"
    echo "Working Services: $working_services/$total_services"
    echo "Success Rate: $(( working_services * 100 / total_services ))%"
}

# Show container status
show_container_status() {
    print_status "Current container status:"
    
    echo ""
    echo "🐳 Running Containers:"
    echo "====================="
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | head -20
    
    echo ""
    echo "📊 Container Statistics:"
    total_containers=$(docker ps -q | wc -l)
    echo "Total Running Containers: $total_containers"
    
    # Show resource usage
    echo ""
    echo "💾 Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | head -10
}

# Generate service documentation
generate_service_docs() {
    print_status "Generating service documentation..."
    
    # Create a comprehensive service list
    cat > COMPLETE_SERVICES_STATUS.md << 'EOF'
# 🚀 SoloYlibre & JEYKO Complete Services Status
## All Services Installation and Setup Results

### 📊 Service Status Summary
Generated: $(date)
Head Developer: Jose L Encarnacion
Company: SoloYlibre & JEYKO

### 🗄️ Database Services
- **PostgreSQL Master**: localhost:5433 | <EMAIL> / Encarnacion12@amd12
- **PostgreSQL CMS**: localhost:5434 | cms_user / CMS_JoseTusabe_2024!
- **PostgreSQL Project2**: localhost:5432 | project2_user / project2_password
- **MySQL Joomla**: localhost:3307 | joomla_user / SoloYlibre_Joomla_2024!
- **Redis Master**: localhost:6380
- **Redis CMS**: localhost:6381

### 🌐 Web Services & CMS
- **WordPress Multisite**: http://localhost:8100 | SoloYlibre / 57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd
- **Drupal**: http://localhost:8101 | SoloYlibre / 57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd
- **Joomla**: http://localhost:8102 | SoloYlibre / 57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd
- **Ghost**: http://localhost:8103
- **Strapi**: http://localhost:8104

### 🤖 AI & Business Services
- **SoloYlibre AI Chat**: http://localhost:3002
- **Docmost**: http://localhost:3003
- **Themer**: http://localhost:3004
- **NocoDB**: http://localhost:8080
- **CMS Gateway**: http://localhost:8107

### 🔧 Infrastructure & Monitoring
- **pgAdmin**: http://localhost:5050 | <EMAIL> / Encarnacion12@amd12
- **Grafana**: http://localhost:3001 | admin / admin
- **Prometheus**: http://localhost:9091
- **MinIO**: http://localhost:9003 | minioadmin / minioadmin
- **Portainer**: http://localhost:9000
- **Jaeger**: http://localhost:16687
- **Traefik**: http://localhost:8081

### 🎤 TTS & Voice Services
- **ElevenLabs TTS**: http://localhost:8105
- **Zonos AI TTS**: http://localhost:8106

### 🚀 Quick Access Commands
```bash
# Check all containers
docker ps

# Check service logs
docker logs [container-name]

# Restart all services
docker-compose -f docker-compose.cms-suite.yml restart

# Access pgAdmin
open http://localhost:5050
```

### 📞 Support Information
- **PostgreSQL Admin**: <EMAIL>
- **Head Developer**: Jose L Encarnacion
- **Company**: SoloYlibre & JEYKO
- **Platform**: Ultimate Development Environment
EOF
    
    print_success "Service documentation generated: COMPLETE_SERVICES_STATUS.md"
}

# Show final results
show_final_results() {
    echo ""
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║              🎉 DOCKER SETUP COMPLETE! 🎉                  ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🚀 SoloYlibre & JEYKO Services Deployed!                  ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    
    # Count running containers
    total_containers=$(docker ps -q | wc -l)
    echo -e "${GREEN}║  📦 Total Containers Running: $total_containers                        ║${NC}"
    
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🌐 Key Service URLs:                                       ║${NC}"
    echo -e "${GREEN}║  • pgAdmin: http://localhost:5050                          ║${NC}"
    echo -e "${GREEN}║  • WordPress: http://localhost:8100                        ║${NC}"
    echo -e "${GREEN}║  • AI Chat: http://localhost:3002                          ║${NC}"
    echo -e "${GREEN}║  • Grafana: http://localhost:3001                          ║${NC}"
    echo -e "${GREEN}║  • Portainer: http://localhost:9000                        ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  📋 Documentation: COMPLETE_SERVICES_STATUS.md             ║${NC}"
    echo -e "${GREEN}║  🏢 Company: SoloYlibre & JEYKO                            ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    
    echo ""
    print_success "Docker services setup completed!"
    print_status "Check COMPLETE_SERVICES_STATUS.md for detailed service information"
}

# Main execution
main() {
    print_header
    
    check_docker
    setup_docker_networks
    start_all_services
    run_setup_scripts
    wait_for_services
    test_services
    show_container_status
    generate_service_docs
    show_final_results
}

# Run the Docker setup
main "$@"
