<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoloYlibre & JEYKO Ultimate Development Environment - Complete Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            padding: 30px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .company-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #667eea;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
        }
        
        .section h3 {
            color: #764ba2;
            font-size: 1.4em;
            margin-bottom: 15px;
            margin-top: 25px;
        }
        
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .service-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .service-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }
        
        .service-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }
        
        .service-status {
            margin-left: auto;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-working {
            background: #d4edda;
            color: #155724;
        }
        
        .status-ready {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-config {
            background: #f8d7da;
            color: #721c24;
        }
        
        .service-details {
            margin-top: 15px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .detail-label {
            font-weight: bold;
            color: #666;
        }
        
        .detail-value {
            color: #333;
            font-family: 'Courier New', monospace;
        }
        
        .url-link {
            color: #667eea;
            text-decoration: none;
        }
        
        .url-link:hover {
            text-decoration: underline;
        }
        
        .credentials-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .credentials-table th,
        .credentials-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .credentials-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        
        .credentials-table tr:hover {
            background: #f8f9fa;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-top: 40px;
            color: #666;
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .print-button:hover {
            background: #764ba2;
            transform: translateY(-2px);
        }
        
        @media print {
            body {
                background: white;
            }
            .container {
                box-shadow: none;
                margin: 0;
                padding: 0;
            }
            .print-button {
                display: none;
            }
        }
        
        .database-icon { background: #0066cc; }
        .web-icon { background: #009900; }
        .ai-icon { background: #ff6600; }
        .infra-icon { background: #cc0000; }
        .tts-icon { background: #9900cc; }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">🖨️ Print/Save PDF</button>
    
    <div class="container">
        <div class="header">
            <h1>🚀 SoloYlibre & JEYKO Ultimate Development Environment</h1>
            <div class="subtitle">Complete Infrastructure Documentation & Service Inventory</div>
            <div style="margin-top: 15px; font-size: 0.9em;">
                Generated: <span id="current-date"></span> | Head Developer: Jose L Encarnacion
            </div>
        </div>
        
        <div class="company-info">
            <h3>🏢 Company Information</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div>
                    <strong>Company:</strong> SoloYlibre<br>
                    <strong>AI Division:</strong> JEYKO<br>
                    <strong>Head Developer:</strong> Jose L Encarnacion
                </div>
                <div>
                    <strong>Server:</strong> Synology RS3618xs<br>
                    <strong>Memory:</strong> 56GB RAM<br>
                    <strong>Architecture:</strong> Containerized Microservices
                </div>
                <div>
                    <strong>PostgreSQL Admin:</strong> <EMAIL><br>
                    <strong>Platform:</strong> Ultimate Business Development Environment<br>
                    <strong>Status:</strong> Production Ready
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📊 Environment Summary</h2>
            <div class="summary-stats">
                <div class="stat-card">
                    <div class="stat-number">25+</div>
                    <div class="stat-label">Running Containers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">6</div>
                    <div class="stat-label">Database Instances</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">20+</div>
                    <div class="stat-label">Available Databases</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">Web Services</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>🗄️ Database Services</h2>
            <div class="service-grid">
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon database-icon">🗄️</div>
                        <div class="service-title">PostgreSQL Master</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-postgres-master</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Port:</span>
                            <span class="detail-value">5433 (external)</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Username:</span>
                            <span class="detail-value">admin</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Password:</span>
                            <span class="detail-value">Encarnacion12@amd12</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Databases:</span>
                            <span class="detail-value">master_db, jeyko_ai, analytics, monitoring</span>
                        </div>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon database-icon">🗄️</div>
                        <div class="service-title">PostgreSQL Project2</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">project2-postgres</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Port:</span>
                            <span class="detail-value">5432 (external)</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Username:</span>
                            <span class="detail-value">project2_user</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Password:</span>
                            <span class="detail-value">project2_password</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Databases:</span>
                            <span class="detail-value">project2, cms_db</span>
                        </div>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon database-icon">🗄️</div>
                        <div class="service-title">MySQL Joomla</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">soloylibre-mysql-joomla</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Port:</span>
                            <span class="detail-value">3307</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Username:</span>
                            <span class="detail-value">joomla_user</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Password:</span>
                            <span class="detail-value">SoloYlibre_Joomla_2024!</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Database:</span>
                            <span class="detail-value">joomla_db</span>
                        </div>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon database-icon">🗄️</div>
                        <div class="service-title">pgAdmin</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-pgadmin</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:5050" class="url-link">http://localhost:5050</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Email:</span>
                            <span class="detail-value"><EMAIL></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Password:</span>
                            <span class="detail-value">Encarnacion12@amd12</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Features:</span>
                            <span class="detail-value">Auto-connected PostgreSQL servers</span>
                        </div>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon database-icon">🗄️</div>
                        <div class="service-title">Redis Master</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-redis-master</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Port:</span>
                            <span class="detail-value">6380</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Purpose:</span>
                            <span class="detail-value">Main caching</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Authentication:</span>
                            <span class="detail-value">No auth required</span>
                        </div>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon database-icon">🗄️</div>
                        <div class="service-title">Redis CMS</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-redis-cms</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Port:</span>
                            <span class="detail-value">6381</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Purpose:</span>
                            <span class="detail-value">CMS caching</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Authentication:</span>
                            <span class="detail-value">No auth required</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>🌐 Web Services & CMS Platforms</h2>
            <div class="service-grid">
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon web-icon">🌐</div>
                        <div class="service-title">WordPress Multisite</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-wordpress-multisite</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:8100" class="url-link">http://localhost:8100</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Username:</span>
                            <span class="detail-value">SoloYlibre</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Password:</span>
                            <span class="detail-value">57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd</span>
                        </div>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon web-icon">🌐</div>
                        <div class="service-title">Drupal</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-drupal</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:8101" class="url-link">http://localhost:8101</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Username:</span>
                            <span class="detail-value">SoloYlibre</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Password:</span>
                            <span class="detail-value">57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd</span>
                        </div>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon web-icon">🌐</div>
                        <div class="service-title">Joomla</div>
                        <div class="service-status status-ready">🔄 Install Ready</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-joomla</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:8102" class="url-link">http://localhost:8102</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Username:</span>
                            <span class="detail-value">SoloYlibre</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Password:</span>
                            <span class="detail-value">57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd</span>
                        </div>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon web-icon">🌐</div>
                        <div class="service-title">Ghost Blog</div>
                        <div class="service-status status-config">❌ Config Needed</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-ghost</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:8103" class="url-link">http://localhost:8103</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Status:</span>
                            <span class="detail-value">Setup required</span>
                        </div>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon web-icon">🌐</div>
                        <div class="service-title">Strapi Headless CMS</div>
                        <div class="service-status status-config">❌ Config Needed</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-strapi</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:8104" class="url-link">http://localhost:8104</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Status:</span>
                            <span class="detail-value">Admin setup required</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🤖 AI & Business Services</h2>
            <div class="service-grid">
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon ai-icon">🤖</div>
                        <div class="service-title">SoloYlibre AI Chat</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">soloylibre-ai-chat</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:3002" class="url-link">http://localhost:3002</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Purpose:</span>
                            <span class="detail-value">SoloYlibre & JEYKO AI Interface</span>
                        </div>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon ai-icon">📄</div>
                        <div class="service-title">Docmost</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-docmost</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:3003" class="url-link">http://localhost:3003</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Purpose:</span>
                            <span class="detail-value">Document Management System</span>
                        </div>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon ai-icon">🎨</div>
                        <div class="service-title">Themer</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">soloylibre-themer</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:3004" class="url-link">http://localhost:3004</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Purpose:</span>
                            <span class="detail-value">Design System Manager</span>
                        </div>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon ai-icon">🗃️</div>
                        <div class="service-title">NocoDB</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">soloylibre-nocodb</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:8080" class="url-link">http://localhost:8080</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Purpose:</span>
                            <span class="detail-value">Visual Database Interface</span>
                        </div>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon ai-icon">🔗</div>
                        <div class="service-title">CMS Gateway</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-cms-gateway</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:8107" class="url-link">http://localhost:8107</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Purpose:</span>
                            <span class="detail-value">Unified API Gateway</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎤 TTS & Voice Services</h2>
            <div class="service-grid">
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon tts-icon">🎤</div>
                        <div class="service-title">ElevenLabs TTS</div>
                        <div class="service-status status-config">❌ Config Needed</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-elevenlabs-tts</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:8105" class="url-link">http://localhost:8105</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Status:</span>
                            <span class="detail-value">API keys required</span>
                        </div>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon tts-icon">🎤</div>
                        <div class="service-title">Zonos AI TTS</div>
                        <div class="service-status status-config">❌ Config Needed</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-zonos-ai-tts</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:8106" class="url-link">http://localhost:8106</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Status:</span>
                            <span class="detail-value">Service setup required</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 Infrastructure & Monitoring</h2>
            <div class="service-grid">
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon infra-icon">📊</div>
                        <div class="service-title">Grafana</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-grafana</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:3001" class="url-link">http://localhost:3001</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Username:</span>
                            <span class="detail-value">admin</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Password:</span>
                            <span class="detail-value">admin</span>
                        </div>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon infra-icon">📈</div>
                        <div class="service-title">Prometheus</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-prometheus</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:9091" class="url-link">http://localhost:9091</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Purpose:</span>
                            <span class="detail-value">Metrics collection</span>
                        </div>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon infra-icon">💾</div>
                        <div class="service-title">MinIO</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-minio</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:9003" class="url-link">http://localhost:9003</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Username:</span>
                            <span class="detail-value">minioadmin</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Password:</span>
                            <span class="detail-value">minioadmin</span>
                        </div>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon infra-icon">🔍</div>
                        <div class="service-title">Jaeger</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-jaeger</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:16687" class="url-link">http://localhost:16687</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Purpose:</span>
                            <span class="detail-value">Distributed tracing</span>
                        </div>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon infra-icon">⚖️</div>
                        <div class="service-title">Traefik</div>
                        <div class="service-status status-working">✅ Working</div>
                    </div>
                    <div class="service-details">
                        <div class="detail-row">
                            <span class="detail-label">Container:</span>
                            <span class="detail-value">josetusabe-traefik</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">URL:</span>
                            <span class="detail-value"><a href="http://localhost:8081" class="url-link">http://localhost:8081</a></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Purpose:</span>
                            <span class="detail-value">Load balancer</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔐 Complete Credentials Reference</h2>
            <table class="credentials-table">
                <thead>
                    <tr>
                        <th>Service</th>
                        <th>URL/Host</th>
                        <th>Username</th>
                        <th>Password</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>pgAdmin</strong></td>
                        <td>http://localhost:5050</td>
                        <td><EMAIL></td>
                        <td>Encarnacion12@amd12</td>
                        <td>PostgreSQL management interface</td>
                    </tr>
                    <tr>
                        <td><strong>PostgreSQL Master</strong></td>
                        <td>localhost:5433</td>
                        <td>admin</td>
                        <td>Encarnacion12@amd12</td>
                        <td>Main business database</td>
                    </tr>
                    <tr>
                        <td><strong>PostgreSQL Project2</strong></td>
                        <td>localhost:5432</td>
                        <td>project2_user</td>
                        <td>project2_password</td>
                        <td>CMS database</td>
                    </tr>
                    <tr>
                        <td><strong>MySQL Joomla</strong></td>
                        <td>localhost:3307</td>
                        <td>joomla_user</td>
                        <td>SoloYlibre_Joomla_2024!</td>
                        <td>Joomla database</td>
                    </tr>
                    <tr>
                        <td><strong>WordPress</strong></td>
                        <td>http://localhost:8100</td>
                        <td>SoloYlibre</td>
                        <td>57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd</td>
                        <td>Multisite installation</td>
                    </tr>
                    <tr>
                        <td><strong>Drupal</strong></td>
                        <td>http://localhost:8101</td>
                        <td>SoloYlibre</td>
                        <td>57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd</td>
                        <td>Content management</td>
                    </tr>
                    <tr>
                        <td><strong>Joomla</strong></td>
                        <td>http://localhost:8102</td>
                        <td>SoloYlibre</td>
                        <td>57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd</td>
                        <td>Installation ready</td>
                    </tr>
                    <tr>
                        <td><strong>Grafana</strong></td>
                        <td>http://localhost:3001</td>
                        <td>admin</td>
                        <td>admin</td>
                        <td>Monitoring dashboards</td>
                    </tr>
                    <tr>
                        <td><strong>MinIO</strong></td>
                        <td>http://localhost:9003</td>
                        <td>minioadmin</td>
                        <td>minioadmin</td>
                        <td>Object storage</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer">
            <h3>🏢 SoloYlibre & JEYKO Ultimate Development Environment</h3>
            <p><strong>Head Developer:</strong> Jose L Encarnacion</p>
            <p><strong>Company:</strong> SoloYlibre | <strong>AI Division:</strong> JEYKO</p>
            <p><strong>Server:</strong> Synology RS3618xs (56GB RAM) | <strong>Architecture:</strong> Containerized Microservices</p>
            <p><strong>Status:</strong> Production Ready Enterprise Platform</p>
            <p style="margin-top: 20px; font-size: 0.9em; color: #888;">
                This documentation was automatically generated for the Ultimate Business Development Environment.<br>
                All services are containerized and optimized for enterprise use.
            </p>
        </div>
    </div>

    <script>
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    </script>
</body>
</html>
