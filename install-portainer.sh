#!/bin/bash

# Portainer Installation and Configuration Script
# Professional 2000% IQ Setup for Ultimate Dev Environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    print_status "Checking Docker installation and status..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "Docker is installed and running"
}

# Check if Docker Compose is available
check_docker_compose() {
    print_status "Checking Docker Compose..."
    
    if docker compose version &> /dev/null; then
        print_success "Docker Compose (v2) is available"
        COMPOSE_CMD="docker compose"
    elif command -v docker-compose &> /dev/null; then
        print_success "Docker Compose (v1) is available"
        COMPOSE_CMD="docker-compose"
    else
        print_error "Docker Compose is not available"
        exit 1
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p secrets
    mkdir -p portainer-data
    mkdir -p logs
    
    print_success "Directories created"
}

# Set proper permissions
set_permissions() {
    print_status "Setting proper permissions..."
    
    chmod 600 secrets/portainer_admin_password.txt
    chmod +x install-portainer.sh
    
    print_success "Permissions set"
}

# Stop existing Portainer containers if any
stop_existing_portainer() {
    print_status "Checking for existing Portainer containers..."
    
    if docker ps -a --format "table {{.Names}}" | grep -q "portainer"; then
        print_warning "Found existing Portainer containers. Stopping and removing..."
        docker stop portainer portainer-agent 2>/dev/null || true
        docker rm portainer portainer-agent 2>/dev/null || true
        print_success "Existing containers removed"
    else
        print_status "No existing Portainer containers found"
    fi
}

# Pull latest Portainer images
pull_images() {
    print_status "Pulling latest Portainer images..."
    
    docker pull portainer/portainer-ce:latest
    docker pull portainer/agent:latest
    
    print_success "Images pulled successfully"
}

# Start Portainer services
start_portainer() {
    print_status "Starting Portainer services..."
    
    $COMPOSE_CMD -f docker-compose.portainer.yml up -d
    
    print_success "Portainer services started"
}

# Wait for Portainer to be ready
wait_for_portainer() {
    print_status "Waiting for Portainer to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:9000 > /dev/null 2>&1; then
            print_success "Portainer is ready!"
            break
        fi
        
        print_status "Attempt $attempt/$max_attempts - Waiting for Portainer..."
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_error "Portainer failed to start within expected time"
        exit 1
    fi
}

# Display connection information
show_connection_info() {
    print_success "=== PORTAINER INSTALLATION COMPLETE ==="
    echo ""
    echo -e "${GREEN}🚀 Portainer is now running!${NC}"
    echo ""
    echo -e "${BLUE}📱 Web Interface:${NC}"
    echo "   • HTTP:  http://localhost:9000"
    echo "   • HTTPS: https://localhost:9443"
    echo ""
    echo -e "${BLUE}🔐 Default Credentials:${NC}"
    echo "   • Username: admin"
    echo "   • Password: admin123!@#"
    echo ""
    echo -e "${BLUE}🐳 Docker Integration:${NC}"
    echo "   • Local Docker: Connected via socket"
    echo "   • Agent Port: 9001 (for remote environments)"
    echo ""
    echo -e "${BLUE}📊 Management Features:${NC}"
    echo "   • Container Management"
    echo "   • Image Management"
    echo "   • Volume Management"
    echo "   • Network Management"
    echo "   • Stack Deployment"
    echo "   • Registry Management"
    echo ""
    echo -e "${YELLOW}⚠️  Security Note:${NC}"
    echo "   Please change the default password after first login!"
    echo ""
    echo -e "${GREEN}🎉 Happy Docker Management!${NC}"
}

# Main installation process
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    PORTAINER INSTALLER                      ║"
    echo "║              Professional 2000% IQ Setup                    ║"
    echo "║                Ultimate Dev Environment                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    check_docker
    check_docker_compose
    create_directories
    set_permissions
    stop_existing_portainer
    pull_images
    start_portainer
    wait_for_portainer
    show_connection_info
}

# Run main function
main "$@"
