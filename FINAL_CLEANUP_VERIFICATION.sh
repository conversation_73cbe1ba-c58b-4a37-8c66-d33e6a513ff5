#!/bin/bash

# FINAL CLEANUP VERIFICATION - SoloYlibre & JEYKO
# Verify all remaining services are working properly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              ✅ CLEANUP VERIFICATION ✅                     ║"
    echo "║              SOLOYLIBRE & JEYKO                             ║"
    echo "║                                                              ║"
    echo "║  🔍 Verifying all remaining services are working            ║"
    echo "║  👨‍💻 Head Developer: Jose L Encarnacion                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${CYAN}[VERIFY]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Test all remaining services
test_all_services() {
    print_status "Testing all remaining services..."
    
    echo ""
    echo "🔍 Service Health Check Results:"
    echo "================================"
    
    # Test each service
    if curl -s --max-time 3 http://localhost:9000 > /dev/null 2>&1; then
        echo -e "✅ Portainer: ${GREEN}WORKING${NC} (http://localhost:9000)"
    else
        echo -e "❌ Portainer: ${RED}NOT RESPONDING${NC} (http://localhost:9000)"
    fi
    
    if curl -s --max-time 3 http://localhost:3000 > /dev/null 2>&1; then
        echo -e "✅ Grafana Ultimate: ${GREEN}WORKING${NC} (http://localhost:3000)"
    else
        echo -e "❌ Grafana Ultimate: ${RED}NOT RESPONDING${NC} (http://localhost:3000)"
    fi
    
    if curl -s --max-time 3 http://localhost:3001 > /dev/null 2>&1; then
        echo -e "✅ Grafana New: ${GREEN}WORKING${NC} (http://localhost:3001)"
    else
        echo -e "❌ Grafana New: ${RED}NOT RESPONDING${NC} (http://localhost:3001)"
    fi
    
    if curl -s --max-time 3 http://localhost:1052 > /dev/null 2>&1; then
        echo -e "✅ WordPress: ${GREEN}WORKING${NC} (http://localhost:1052)"
    else
        echo -e "❌ WordPress: ${RED}NOT RESPONDING${NC} (http://localhost:1052)"
    fi
    
    if curl -s --max-time 3 http://localhost:2051 > /dev/null 2>&1; then
        echo -e "✅ phpMyAdmin: ${GREEN}WORKING${NC} (http://localhost:2051)"
    else
        echo -e "❌ phpMyAdmin: ${RED}NOT RESPONDING${NC} (http://localhost:2051)"
    fi
    
    if curl -s --max-time 3 http://localhost:5050 > /dev/null 2>&1; then
        echo -e "✅ pgAdmin: ${GREEN}WORKING${NC} (http://localhost:5050)"
    else
        echo -e "❌ pgAdmin: ${RED}NOT RESPONDING${NC} (http://localhost:5050)"
    fi
    
    echo ""
}

# Show final status
show_final_status() {
    echo ""
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║              🎉 CLEANUP VERIFICATION COMPLETE! 🎉          ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🧹 SoloYlibre & JEYKO Environment Optimized!             ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    
    # Count current resources
    running_containers=$(docker ps -q | wc -l)
    total_images=$(docker images -q | wc -l)
    total_volumes=$(docker volume ls -q | wc -l)
    
    echo -e "${GREEN}║  📦 Running Containers: $running_containers                              ║${NC}"
    echo -e "${GREEN}║  🖼️  Docker Images: $total_images                                   ║${NC}"
    echo -e "${GREEN}║  💾 Docker Volumes: $total_volumes                                   ║${NC}"
    
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🌐 Active Services:                                        ║${NC}"
    echo -e "${GREEN}║  • Portainer: http://localhost:9000                        ║${NC}"
    echo -e "${GREEN}║  • Grafana: http://localhost:3000                          ║${NC}"
    echo -e "${GREEN}║  • WordPress: http://localhost:1052                        ║${NC}"
    echo -e "${GREEN}║  • phpMyAdmin: http://localhost:2051                       ║${NC}"
    echo -e "${GREEN}║  • pgAdmin: http://localhost:5050                          ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  ✅ All unused resources removed                           ║${NC}"
    echo -e "${GREEN}║  ✅ All remaining services verified working               ║${NC}"
    echo -e "${GREEN}║  ✅ Environment optimized for production                   ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🏢 Company: SoloYlibre & JEYKO                            ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    
    echo ""
    print_success "Environment cleanup and verification completed!"
    print_status "Your SoloYlibre & JEYKO Ultimate Development Environment is optimized!"
}

# Main execution
main() {
    print_header
    test_all_services
    show_final_status
}

# Run the verification
main "$@"
