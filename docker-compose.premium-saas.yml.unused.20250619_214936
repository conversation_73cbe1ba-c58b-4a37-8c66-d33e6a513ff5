version: '3.8'

services:
  # ERPNext - Complete Business Management
  erpnext:
    image: frappe/erpnext:latest
    container_name: josetusabe-erpnext
    ports:
      - "8972:8000"
    environment:
      - SITE_NAME=erp.soloylibre.com
      - DB_HOST=postgres-premium
      - DB_PORT=5432
      - DB_NAME=erpnext
      - DB_USER=premium_user
      - DB_PASSWORD=premium_pass_123
      - REDIS_CACHE_URL=redis://redis-premium:6379/1
      - REDIS_QUEUE_URL=redis://redis-premium:6379/2
      - REDIS_SOCKETIO_URL=redis://redis-premium:6379/3
      - MAIL_SERVER=smtp.dynu.com
      - MAIL_PORT=587
      - MAIL_USE_TLS=1
      - MAIL_USERNAME=${DYNU_EMAIL_USER}
      - MAIL_PASSWORD=${DYNU_EMAIL_PASS}
    volumes:
      - erpnext_data:/home/<USER>/frappe-bench/sites
      - ./erpnext/apps:/home/<USER>/frappe-bench/apps
    networks:
      - premium-network
    depends_on:
      - postgres-premium
      - redis-premium
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.erpnext.rule=Host(`erp.soloylibre.com`)"
      - "traefik.http.routers.erpnext.entrypoints=websecure"
      - "traefik.http.routers.erpnext.tls=true"

  # Plane - Project Management
  plane-web:
    image: makeplane/plane-frontend:latest
    container_name: josetusabe-plane-web
    ports:
      - "8973:3000"
    environment:
      - NEXT_PUBLIC_API_BASE_URL=https://plane-api.soloylibre.com
      - NEXT_PUBLIC_DEPLOY_URL=https://plane.soloylibre.com
    volumes:
      - plane_web_data:/app/web
    networks:
      - premium-network
    depends_on:
      - plane-api
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.plane-web.rule=Host(`plane.soloylibre.com`)"
      - "traefik.http.routers.plane-web.entrypoints=websecure"
      - "traefik.http.routers.plane-web.tls=true"

  plane-api:
    image: makeplane/plane-backend:latest
    container_name: josetusabe-plane-api
    ports:
      - "8974:8000"
    environment:
      - DATABASE_URL=****************************************************************/plane
      - REDIS_URL=redis://redis-premium:6379/4
      - SECRET_KEY=${PLANE_SECRET_KEY}
      - WEB_URL=https://plane.soloylibre.com
      - EMAIL_HOST=smtp.dynu.com
      - EMAIL_PORT=587
      - EMAIL_HOST_USER=${DYNU_EMAIL_USER}
      - EMAIL_HOST_PASSWORD=${DYNU_EMAIL_PASS}
      - EMAIL_USE_TLS=True
      - DEFAULT_FROM_EMAIL=${DYNU_EMAIL_FROM}
    volumes:
      - plane_api_data:/code/plane
    networks:
      - premium-network
    depends_on:
      - postgres-premium
      - redis-premium
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.plane-api.rule=Host(`plane-api.soloylibre.com`)"
      - "traefik.http.routers.plane-api.entrypoints=websecure"
      - "traefik.http.routers.plane-api.tls=true"

  # Supabase - Backend as a Service
  supabase-db:
    image: supabase/postgres:**********
    container_name: josetusabe-supabase-db
    ports:
      - "5435:5432"
    environment:
      - POSTGRES_DB=supabase
      - POSTGRES_USER=supabase
      - POSTGRES_PASSWORD=supabase_pass_123
      - JWT_SECRET=${SUPABASE_JWT_SECRET}
      - JWT_EXP=3600
    volumes:
      - supabase_db_data:/var/lib/postgresql/data
    networks:
      - premium-network
    restart: unless-stopped

  supabase-api:
    image: supabase/gotrue:latest
    container_name: josetusabe-supabase-api
    ports:
      - "8975:9999"
    environment:
      - GOTRUE_API_HOST=0.0.0.0
      - GOTRUE_API_PORT=9999
      - API_EXTERNAL_URL=https://supabase.soloylibre.com
      - GOTRUE_DB_DRIVER=postgres
      - GOTRUE_DB_DATABASE_URL=********************************************************/supabase
      - GOTRUE_SITE_URL=https://supabase.soloylibre.com
      - GOTRUE_JWT_SECRET=${SUPABASE_JWT_SECRET}
      - GOTRUE_JWT_EXP=3600
      - GOTRUE_SMTP_HOST=smtp.dynu.com
      - GOTRUE_SMTP_PORT=587
      - GOTRUE_SMTP_USER=${DYNU_EMAIL_USER}
      - GOTRUE_SMTP_PASS=${DYNU_EMAIL_PASS}
      - GOTRUE_SMTP_ADMIN_EMAIL=${DYNU_EMAIL_FROM}
    networks:
      - premium-network
    depends_on:
      - supabase-db
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.supabase-api.rule=Host(`supabase.soloylibre.com`)"
      - "traefik.http.routers.supabase-api.entrypoints=websecure"
      - "traefik.http.routers.supabase-api.tls=true"

  # Appwrite - Backend Platform
  appwrite:
    image: appwrite/appwrite:latest
    container_name: josetusabe-appwrite
    ports:
      - "8976:80"
      - "8977:443"
    environment:
      - _APP_ENV=production
      - _APP_WORKER_PER_CORE=6
      - _APP_LOCALE=en
      - _APP_CONSOLE_WHITELIST_ROOT=enabled
      - _APP_CONSOLE_WHITELIST_EMAILS=
      - _APP_CONSOLE_WHITELIST_IPS=
      - _APP_SYSTEM_EMAIL_NAME=Appwrite
      - _APP_SYSTEM_EMAIL_ADDRESS=${DYNU_EMAIL_FROM}
      - _APP_SYSTEM_RESPONSE_FORMAT=
      - _APP_OPTIONS_ABUSE=enabled
      - _APP_OPTIONS_FORCE_HTTPS=disabled
      - _APP_OPENSSL_KEY_V1=${APPWRITE_OPENSSL_KEY}
      - _APP_DOMAIN=appwrite.soloylibre.com
      - _APP_DOMAIN_TARGET=appwrite.soloylibre.com
      - _APP_REDIS_HOST=redis-premium
      - _APP_REDIS_PORT=6379
      - _APP_REDIS_USER=
      - _APP_REDIS_PASS=premium_redis_123
      - _APP_DB_HOST=postgres-premium
      - _APP_DB_PORT=5432
      - _APP_DB_SCHEMA=appwrite
      - _APP_DB_USER=premium_user
      - _APP_DB_PASS=premium_pass_123
      - _APP_SMTP_HOST=smtp.dynu.com
      - _APP_SMTP_PORT=587
      - _APP_SMTP_SECURE=tls
      - _APP_SMTP_USERNAME=${DYNU_EMAIL_USER}
      - _APP_SMTP_PASSWORD=${DYNU_EMAIL_PASS}
    volumes:
      - appwrite_data:/storage
      - ./appwrite/config:/usr/src/code/app/config
    networks:
      - premium-network
    depends_on:
      - postgres-premium
      - redis-premium
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.appwrite.rule=Host(`appwrite.soloylibre.com`)"
      - "traefik.http.routers.appwrite.entrypoints=websecure"
      - "traefik.http.routers.appwrite.tls=true"

  # PocketBase - Lightweight Backend
  pocketbase:
    image: spectado/pocketbase:latest
    container_name: josetusabe-pocketbase
    ports:
      - "8978:8090"
    environment:
      - PB_ENCRYPTION_KEY=${POCKETBASE_ENCRYPTION_KEY}
    volumes:
      - pocketbase_data:/pb_data
      - ./pocketbase/migrations:/pb_migrations
    networks:
      - premium-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.pocketbase.rule=Host(`pocketbase.soloylibre.com`)"
      - "traefik.http.routers.pocketbase.entrypoints=websecure"
      - "traefik.http.routers.pocketbase.tls=true"

  # Coolify - Self-hosted Vercel Alternative
  coolify:
    image: coollabsio/coolify:latest
    container_name: josetusabe-coolify
    ports:
      - "8979:8000"
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_URL=https://coolify.soloylibre.com
      - DB_CONNECTION=pgsql
      - DB_HOST=postgres-premium
      - DB_PORT=5432
      - DB_DATABASE=coolify
      - DB_USERNAME=premium_user
      - DB_PASSWORD=premium_pass_123
      - REDIS_HOST=redis-premium
      - REDIS_PASSWORD=premium_redis_123
      - REDIS_PORT=6379
      - MAIL_MAILER=smtp
      - MAIL_HOST=smtp.dynu.com
      - MAIL_PORT=587
      - MAIL_USERNAME=${DYNU_EMAIL_USER}
      - MAIL_PASSWORD=${DYNU_EMAIL_PASS}
      - MAIL_ENCRYPTION=tls
      - MAIL_FROM_ADDRESS=${DYNU_EMAIL_FROM}
    volumes:
      - coolify_data:/data
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - premium-network
    depends_on:
      - postgres-premium
      - redis-premium
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.coolify.rule=Host(`coolify.soloylibre.com`)"
      - "traefik.http.routers.coolify.entrypoints=websecure"
      - "traefik.http.routers.coolify.tls=true"

  # Chatwoot - Customer Support
  chatwoot:
    image: chatwoot/chatwoot:latest
    container_name: josetusabe-chatwoot
    ports:
      - "8980:3000"
    environment:
      - NODE_ENV=production
      - RAILS_ENV=production
      - INSTALLATION_ENV=docker
      - SECRET_KEY_BASE=${CHATWOOT_SECRET_KEY}
      - POSTGRES_HOST=postgres-premium
      - POSTGRES_USERNAME=premium_user
      - POSTGRES_PASSWORD=premium_pass_123
      - POSTGRES_DATABASE=chatwoot
      - REDIS_URL=redis://redis-premium:6379/5
      - REDIS_PASSWORD=premium_redis_123
      - FRONTEND_URL=https://chat.soloylibre.com
      - SMTP_DOMAIN=soloylibre.com
      - SMTP_ADDRESS=smtp.dynu.com
      - SMTP_PORT=587
      - SMTP_USERNAME=${DYNU_EMAIL_USER}
      - SMTP_PASSWORD=${DYNU_EMAIL_PASS}
      - SMTP_AUTHENTICATION=plain
      - SMTP_ENABLE_STARTTLS_AUTO=true
      - MAILER_SENDER_EMAIL=${DYNU_EMAIL_FROM}
    volumes:
      - chatwoot_data:/app/storage
    networks:
      - premium-network
    depends_on:
      - postgres-premium
      - redis-premium
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.chatwoot.rule=Host(`chat.soloylibre.com`)"
      - "traefik.http.routers.chatwoot.entrypoints=websecure"
      - "traefik.http.routers.chatwoot.tls=true"

  # Metabase - Business Intelligence
  metabase:
    image: metabase/metabase:latest
    container_name: josetusabe-metabase
    ports:
      - "8981:3000"
    environment:
      - MB_DB_TYPE=postgres
      - MB_DB_DBNAME=metabase
      - MB_DB_PORT=5432
      - MB_DB_USER=premium_user
      - MB_DB_PASS=premium_pass_123
      - MB_DB_HOST=postgres-premium
      - MB_EMAIL_SMTP_HOST=smtp.dynu.com
      - MB_EMAIL_SMTP_PORT=587
      - MB_EMAIL_SMTP_USERNAME=${DYNU_EMAIL_USER}
      - MB_EMAIL_SMTP_PASSWORD=${DYNU_EMAIL_PASS}
      - MB_EMAIL_SMTP_SECURITY=tls
      - MB_EMAIL_FROM_ADDRESS=${DYNU_EMAIL_FROM}
    volumes:
      - metabase_data:/metabase-data
    networks:
      - premium-network
    depends_on:
      - postgres-premium
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.metabase.rule=Host(`analytics.soloylibre.com`)"
      - "traefik.http.routers.metabase.entrypoints=websecure"
      - "traefik.http.routers.metabase.tls=true"

  # PostgreSQL for Premium Services
  postgres-premium:
    image: postgres:15-alpine
    container_name: josetusabe-postgres-premium
    ports:
      - "5436:5432"
    environment:
      - POSTGRES_DB=premium_services
      - POSTGRES_USER=premium_user
      - POSTGRES_PASSWORD=premium_pass_123
      - POSTGRES_MULTIPLE_DATABASES=erpnext,plane,coolify,chatwoot,metabase,appwrite
    volumes:
      - postgres_premium_data:/var/lib/postgresql/data
      - ./premium-services/database/init:/docker-entrypoint-initdb.d
    networks:
      - premium-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U premium_user"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Premium Services
  redis-premium:
    image: redis:7-alpine
    container_name: josetusabe-redis-premium
    ports:
      - "6382:6379"
    command: redis-server --appendonly yes --requirepass premium_redis_123
    volumes:
      - redis_premium_data:/data
    networks:
      - premium-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "premium_redis_123", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  erpnext_data:
  plane_web_data:
  plane_api_data:
  supabase_db_data:
  appwrite_data:
  pocketbase_data:
  coolify_data:
  chatwoot_data:
  metabase_data:
  postgres_premium_data:
  redis_premium_data:

networks:
  premium-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
