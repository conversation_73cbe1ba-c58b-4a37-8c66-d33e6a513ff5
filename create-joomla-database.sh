#!/bin/bash

# Create Joomla Database Tables
# Solo<PERSON><PERSON><PERSON> & JEYKO Dev - Head Developer: <PERSON>carnacion

echo "🔧 Creating Joomla database tables..."

# Create essential Joomla tables
docker exec soloylibre-mysql-joomla mysql -u joomla_user -pSoloYlibre_Joomla_2024! joomla_db << 'EOF'

-- Create users table
CREATE TABLE IF NOT EXISTS `sol_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(400) NOT NULL DEFAULT '',
  `username` varchar(150) NOT NULL DEFAULT '',
  `email` varchar(100) NOT NULL DEFAULT '',
  `password` varchar(100) NOT NULL DEFAULT '',
  `block` tinyint(4) NOT NULL DEFAULT '0',
  `sendEmail` tinyint(4) DEFAULT '0',
  `registerDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `lastvisitDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `activation` varchar(100) NOT NULL DEFAULT '',
  `params` text NOT NULL,
  `lastResetTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `resetCount` int(11) NOT NULL DEFAULT '0',
  `otpKey` varchar(1000) NOT NULL DEFAULT '',
  `otep` varchar(1000) NOT NULL DEFAULT '',
  `requireReset` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  KEY `idx_name` (`name`(100)),
  KEY `idx_block` (`block`),
  KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert SoloYlibre admin user
INSERT INTO `sol_users` (`name`, `username`, `email`, `password`, `block`, `sendEmail`, `registerDate`, `lastvisitDate`, `params`) 
VALUES ('Jose L Encarnacion - SoloYlibre', 'SoloYlibre', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 0, 1, NOW(), NOW(), '{}')
ON DUPLICATE KEY UPDATE name=VALUES(name), email=VALUES(email);

-- Create user groups table
CREATE TABLE IF NOT EXISTS `sol_usergroups` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` int(10) unsigned NOT NULL DEFAULT '0',
  `lft` int(11) NOT NULL DEFAULT '0',
  `rgt` int(11) NOT NULL DEFAULT '0',
  `level` int(10) unsigned NOT NULL DEFAULT '0',
  `path` varchar(400) NOT NULL DEFAULT '',
  `title` varchar(100) NOT NULL DEFAULT '',
  `alias` varchar(400) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_usergroup_parent_title_lookup` (`parent_id`,`title`),
  KEY `idx_usergroup_title_lookup` (`title`),
  KEY `idx_usergroup_adjacency_lookup` (`parent_id`),
  KEY `idx_usergroup_nested_set_lookup` (`lft`,`rgt`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default user groups
INSERT INTO `sol_usergroups` (`id`, `parent_id`, `lft`, `rgt`, `level`, `path`, `title`, `alias`) VALUES
(1, 0, 1, 22, 0, '', 'Public', 'public'),
(2, 1, 8, 21, 1, 'public', 'Registered', 'registered'),
(3, 2, 9, 16, 2, 'public/registered', 'Author', 'author'),
(4, 3, 10, 13, 3, 'public/registered/author', 'Editor', 'editor'),
(5, 4, 11, 12, 4, 'public/registered/author/editor', 'Publisher', 'publisher'),
(6, 1, 4, 7, 1, 'public', 'Manager', 'manager'),
(7, 6, 5, 6, 2, 'public/manager', 'Administrator', 'administrator'),
(8, 1, 2, 3, 1, 'public', 'Super Users', 'super-users')
ON DUPLICATE KEY UPDATE title=VALUES(title);

-- Create user group mapping
CREATE TABLE IF NOT EXISTS `sol_user_usergroup_map` (
  `user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `group_id` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`user_id`,`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Map SoloYlibre user to Super Users group
INSERT INTO `sol_user_usergroup_map` (`user_id`, `group_id`) VALUES (1, 8)
ON DUPLICATE KEY UPDATE user_id=VALUES(user_id);

-- Create session table
CREATE TABLE IF NOT EXISTS `sol_session` (
  `session_id` varbinary(192) NOT NULL,
  `client_id` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `guest` tinyint(4) unsigned DEFAULT '1',
  `time` int(11) NOT NULL DEFAULT '0',
  `data` mediumtext,
  `userid` int(11) DEFAULT '0',
  `username` varchar(150) DEFAULT '',
  PRIMARY KEY (`session_id`),
  KEY `userid` (`userid`),
  KEY `time` (`time`),
  KEY `client_id_guest` (`client_id`,`guest`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create content table
CREATE TABLE IF NOT EXISTS `sol_content` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `asset_id` int(10) unsigned NOT NULL DEFAULT '0',
  `title` varchar(255) NOT NULL DEFAULT '',
  `alias` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  `introtext` mediumtext NOT NULL,
  `fulltext` mediumtext NOT NULL,
  `state` tinyint(3) NOT NULL DEFAULT '0',
  `catid` int(10) unsigned NOT NULL DEFAULT '0',
  `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(10) unsigned NOT NULL DEFAULT '0',
  `created_by_alias` varchar(255) NOT NULL DEFAULT '',
  `modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified_by` int(10) unsigned NOT NULL DEFAULT '0',
  `checked_out` int(10) unsigned NOT NULL DEFAULT '0',
  `checked_out_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `publish_up` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `publish_down` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `images` text NOT NULL,
  `urls` text NOT NULL,
  `attribs` varchar(5120) NOT NULL,
  `version` int(10) unsigned NOT NULL DEFAULT '1',
  `ordering` int(11) NOT NULL DEFAULT '0',
  `metakey` text NOT NULL,
  `metadesc` text NOT NULL,
  `access` int(10) unsigned NOT NULL DEFAULT '0',
  `hits` int(10) unsigned NOT NULL DEFAULT '0',
  `metadata` text NOT NULL,
  `featured` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `language` char(7) NOT NULL COMMENT 'The language code for the article.',
  `xreference` varchar(50) NOT NULL COMMENT 'A reference to enable linkages to external data sets.',
  `note` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `idx_access` (`access`),
  KEY `idx_checkout` (`checked_out`),
  KEY `idx_state` (`state`),
  KEY `idx_catid` (`catid`),
  KEY `idx_createdby` (`created_by`),
  KEY `idx_featured_catid` (`featured`,`catid`),
  KEY `idx_language` (`language`),
  KEY `idx_xreference` (`xreference`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert welcome content
INSERT INTO `sol_content` (`title`, `alias`, `introtext`, `fulltext`, `state`, `catid`, `created_by`, `access`, `language`) VALUES
('Welcome to SoloYlibre', 'welcome-to-soloylibre', '<p>Welcome to the SoloYlibre business platform powered by JEYKO AI division.</p>', '<p>This is the complete business automation platform developed by Jose L Encarnacion for SoloYlibre company with JEYKO AI integration.</p><p>Features include:</p><ul><li>Multi-CMS Integration</li><li>AI-powered automation</li><li>Enterprise monitoring</li><li>Unified credential management</li></ul>', 1, 2, 1, 1, '*')
ON DUPLICATE KEY UPDATE title=VALUES(title);

-- Create extensions table
CREATE TABLE IF NOT EXISTS `sol_extensions` (
  `extension_id` int(11) NOT NULL AUTO_INCREMENT,
  `package_id` int(11) NOT NULL DEFAULT '0',
  `name` varchar(100) NOT NULL,
  `type` varchar(20) NOT NULL,
  `element` varchar(100) NOT NULL,
  `folder` varchar(100) NOT NULL,
  `client_id` tinyint(3) NOT NULL,
  `enabled` tinyint(3) NOT NULL DEFAULT '1',
  `access` int(10) unsigned NOT NULL DEFAULT '1',
  `protected` tinyint(3) NOT NULL DEFAULT '0',
  `manifest_cache` text NOT NULL,
  `params` text NOT NULL,
  `custom_data` text NOT NULL,
  `system_data` text NOT NULL,
  `checked_out` int(10) unsigned NOT NULL DEFAULT '0',
  `checked_out_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ordering` int(11) DEFAULT '0',
  `state` int(11) DEFAULT '0',
  PRIMARY KEY (`extension_id`),
  KEY `element_clientid` (`element`,`client_id`),
  KEY `element_folder_clientid` (`element`,`folder`,`client_id`),
  KEY `extension` (`type`,`element`,`folder`,`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

EOF

echo "✅ Joomla database tables created successfully!"
