# N8N Integration Configuration for All Services
# Professional 2000% IQ Automation Hub
# Jose L Encarnacion (JoseTusabe)

# N8N Workflow Templates and Integrations
version: '3.8'

services:
  # Enhanced N8N with All Integrations
  n8n-master:
    image: n8nio/n8n:latest
    container_name: josetusabe-n8n-master
    ports:
      - "5679:5678"
    environment:
      # Core Configuration
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=josetusabe
      - N8N_BASIC_AUTH_PASSWORD=josetusabe123
      - N8N_HOST=automation.soloylibre.com
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - WEBHOOK_URL=https://automation.soloylibre.com
      
      # Database Configuration
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres-business
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n_master
      - DB_POSTGRESDB_USER=business_user
      - DB_POSTGRESDB_PASSWORD=business_pass_123
      
      # Email Configuration (Dynu)
      - N8N_SMTP_HOST=smtp.dynu.com
      - N8N_SMTP_PORT=587
      - N8N_SMTP_USER=${DYNU_EMAIL_USER}
      - N8N_SMTP_PASS=${DYNU_EMAIL_PASS}
      - N8N_SMTP_SENDER=${DYNU_EMAIL_FROM}
      - N8N_SMTP_SSL=false
      - N8N_SMTP_TLS=true
      
      # Service Integrations
      - ZONOS_API_URL=http://josetusabe-zonos:3000
      - VOICE_AGENT_URL=http://josetusabe-voice-agent:3000
      - THEMER_API_URL=http://josetusabe-themer:3000
      - NEXTCLOUD_URL=http://josetusabe-nextcloud:80
      - DOCMOST_URL=http://josetusabe-docmost:3000
      - STRAPI_URL=http://josetusabe-strapi:1337
      - NOCODB_URL=http://josetusabe-nocodb:8080
      - FEEDHIVE_URL=http://josetusabe-feedhive:3000
      
      # AI Services Integration
      - OLLAMA_URL=http://josetusabe-ollama:11434
      - LLAMAGPT_URL=http://josetusabe-llamagpt:3000
      - AI_MANAGER_URL=http://josetusabe-ai-manager:8000
      
      # Project Integration
      - PROJECT1_API_URL=http://project1-api-gateway:8000
      - PROJECT2_API_URL=http://project2-api:8000
      - WORDPRESS_URL=http://wordpress:80
      
      # External APIs
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - TWITTER_API_KEY=${TWITTER_API_KEY}
      - FACEBOOK_API_KEY=${FACEBOOK_APP_ID}
      - LINKEDIN_API_KEY=${LINKEDIN_CLIENT_ID}
      - ZONOS_API_KEY=${ZONOS_API_KEY}
      
      # Custom Node Modules
      - N8N_CUSTOM_EXTENSIONS=/opt/custom-nodes
      
    volumes:
      - n8n_master_data:/home/<USER>/.n8n
      - ./n8n-integrations/workflows:/home/<USER>/.n8n/workflows
      - ./n8n-integrations/credentials:/home/<USER>/.n8n/credentials
      - ./n8n-integrations/custom-nodes:/opt/custom-nodes
      - ./n8n-integrations/templates:/opt/templates
    networks:
      - business-network
      - ai-network
      - project1-network
      - project2-network
    depends_on:
      - postgres-business
      - redis-business
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.n8n-master.rule=Host(`automation.soloylibre.com`)"
      - "traefik.http.routers.n8n-master.entrypoints=websecure"
      - "traefik.http.routers.n8n-master.tls=true"

  # Workflow Scheduler
  workflow-scheduler:
    build:
      context: ./workflow-scheduler
      dockerfile: Dockerfile
    container_name: josetusabe-workflow-scheduler
    environment:
      - N8N_API_URL=http://josetusabe-n8n-master:5678
      - N8N_API_KEY=${N8N_API_KEY}
      - REDIS_URL=redis://redis-business:6379/6
      - SCHEDULER_INTERVAL=60000
      - LOG_LEVEL=info
    volumes:
      - ./workflow-scheduler/schedules:/app/schedules
      - workflow_scheduler_logs:/app/logs
    networks:
      - business-network
    depends_on:
      - n8n-master
      - redis-business
    restart: unless-stopped

  # Integration API Gateway
  integration-gateway:
    build:
      context: ./integration-gateway
      dockerfile: Dockerfile
    container_name: josetusabe-integration-gateway
    ports:
      - "8971:8000"
    environment:
      - DATABASE_URL=*******************************************************************/integrations
      - REDIS_URL=redis://redis-business:6379/7
      - JWT_SECRET=${INTEGRATION_JWT_SECRET}
      - API_RATE_LIMIT=1000
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}
      
      # Service URLs for proxying
      - ZONOS_URL=http://josetusabe-zonos:3000
      - VOICE_AGENT_URL=http://josetusabe-voice-agent:3000
      - THEMER_URL=http://josetusabe-themer:3000
      - NEXTCLOUD_URL=http://josetusabe-nextcloud:80
      - DOCMOST_URL=http://josetusabe-docmost:3000
      - STRAPI_URL=http://josetusabe-strapi:1337
      - NOCODB_URL=http://josetusabe-nocodb:8080
      - FEEDHIVE_URL=http://josetusabe-feedhive:3000
      
    volumes:
      - ./integration-gateway:/app
      - integration_logs:/app/logs
    networks:
      - business-network
    depends_on:
      - postgres-business
      - redis-business
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.integration-gateway.rule=Host(`integrations.soloylibre.com`)"
      - "traefik.http.routers.integration-gateway.entrypoints=websecure"
      - "traefik.http.routers.integration-gateway.tls=true"

volumes:
  n8n_master_data:
  workflow_scheduler_logs:
  integration_logs:

---

# Pre-configured N8N Workflows for All Services

# Workflow 1: Content Creation Pipeline
content_creation_workflow:
  name: "Content Creation Pipeline"
  description: "AI-powered content creation and distribution"
  triggers:
    - webhook: "/webhook/content-request"
    - schedule: "0 9 * * 1-5"  # Weekdays at 9 AM
  nodes:
    1. HTTP Request to Voice Agent for content ideas
    2. OpenAI/Ollama for content generation
    3. Themer for visual design
    4. Strapi for content storage
    5. FeedHive for social media posting
    6. Email notification via Dynu

# Workflow 2: E-commerce Tax Calculation
ecommerce_tax_workflow:
  name: "E-commerce Tax Automation"
  description: "Automatic tax calculation with Zonos"
  triggers:
    - webhook: "/webhook/order-created"
  nodes:
    1. Receive order data
    2. Zonos API for tax calculation
    3. NocoDB for order storage
    4. Email confirmation via Dynu
    5. Update Project1/Project2 databases

# Workflow 3: Documentation Sync
documentation_sync_workflow:
  name: "Documentation Synchronization"
  description: "Sync docs across all platforms"
  triggers:
    - webhook: "/webhook/doc-updated"
    - schedule: "0 */6 * * *"  # Every 6 hours
  nodes:
    1. Docmost content retrieval
    2. Format conversion
    3. Nextcloud file update
    4. Strapi content update
    5. WordPress post creation
    6. Team notification

# Workflow 4: AI Model Management
ai_model_workflow:
  name: "AI Model Lifecycle Management"
  description: "Automated AI model deployment and monitoring"
  triggers:
    - webhook: "/webhook/model-update"
    - schedule: "0 2 * * *"  # Daily at 2 AM
  nodes:
    1. Check Ollama for new models
    2. Download and optimize with Reduced
    3. Update AI Manager registry
    4. Test model performance
    5. Update documentation in Docmost
    6. Notify team via email

# Workflow 5: Social Media Automation
social_media_workflow:
  name: "Social Media Content Automation"
  description: "Automated social media posting and engagement"
  triggers:
    - schedule: "0 8,12,17 * * *"  # 3 times daily
    - webhook: "/webhook/social-trigger"
  nodes:
    1. Generate content with AI
    2. Create visuals with Themer
    3. Schedule posts via FeedHive
    4. Monitor engagement
    5. Store analytics in NocoDB
    6. Generate reports

# Workflow 6: File Management Automation
file_management_workflow:
  name: "Automated File Management"
  description: "Smart file organization and backup"
  triggers:
    - webhook: "/webhook/file-uploaded"
    - schedule: "0 1 * * *"  # Daily at 1 AM
  nodes:
    1. Nextcloud file monitoring
    2. AI-powered file categorization
    3. Automatic tagging and organization
    4. Backup to MinIO
    5. Update file registry in NocoDB
    6. Cleanup old files

# Workflow 7: Customer Support Automation
support_automation_workflow:
  name: "Customer Support Automation"
  description: "AI-powered customer support"
  triggers:
    - webhook: "/webhook/support-ticket"
    - email: "<EMAIL>"
  nodes:
    1. Parse support request
    2. AI analysis with Ollama
    3. Generate response draft
    4. Create ticket in NocoDB
    5. Voice response via Voice Agent
    6. Follow-up scheduling

# Workflow 8: Business Intelligence
business_intelligence_workflow:
  name: "Business Intelligence Dashboard"
  description: "Automated business reporting"
  triggers:
    - schedule: "0 6 * * 1"  # Weekly on Monday at 6 AM
  nodes:
    1. Collect data from all services
    2. Process with AI analytics
    3. Generate visualizations
    4. Update Grafana dashboards
    5. Create reports in Docmost
    6. Email executive summary

# Workflow 9: Theme Management
theme_management_workflow:
  name: "Dynamic Theme Management"
  description: "Automated theme deployment and updates"
  triggers:
    - webhook: "/webhook/theme-update"
    - schedule: "0 3 * * *"  # Daily at 3 AM
  nodes:
    1. Scan for theme updates
    2. Process with Themer
    3. Deploy to WordPress
    4. Update Project1/Project2 themes
    5. Test compatibility
    6. Rollback if issues detected

# Workflow 10: Integration Health Monitoring
health_monitoring_workflow:
  name: "Integration Health Monitoring"
  description: "Monitor all service integrations"
  triggers:
    - schedule: "*/15 * * * *"  # Every 15 minutes
  nodes:
    1. Health check all services
    2. Performance metrics collection
    3. Error detection and logging
    4. Alert generation for issues
    5. Auto-healing attempts
    6. Escalation notifications
