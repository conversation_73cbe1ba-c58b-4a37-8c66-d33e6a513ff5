#!/bin/bash

# QUICK SERVICES SETUP - SoloYlibre & JEYKO
# Start remaining services and test everything

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              QUICK SERVICES SETUP                           ║"
    echo "║              SOLOYLIBRE & JEYKO                             ║"
    echo "║                                                              ║"
    echo "║  🚀 Starting remaining services and testing everything      ║"
    echo "║  👨‍💻 Head Developer: Jose L Encarnacion                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${CYAN}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check current services
check_current_services() {
    print_status "Checking currently running services..."
    
    echo "Current containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    print_success "Found $(docker ps -q | wc -l) running containers"
}

# Start CMS suite services
start_cms_services() {
    print_status "Starting CMS suite services..."
    
    if [ -f "docker-compose.cms-suite.yml" ]; then
        # Start specific services that aren't running
        docker-compose -f docker-compose.cms-suite.yml up -d postgres-master postgres-cms redis-master redis-cms
        sleep 5
        
        docker-compose -f docker-compose.cms-suite.yml up -d wordpress-multisite drupal joomla
        sleep 5
        
        docker-compose -f docker-compose.cms-suite.yml up -d pgadmin
        sleep 5
        
        print_success "CMS services started"
    else
        print_error "docker-compose.cms-suite.yml not found"
    fi
}

# Start AI services
start_ai_services() {
    print_status "Starting AI services..."
    
    if [ -f "docker-compose.ai-simple.yml" ]; then
        docker-compose -f docker-compose.ai-simple.yml up -d
        print_success "AI services started"
    elif [ -f "docker-compose.ai-services.yml" ]; then
        docker-compose -f docker-compose.ai-services.yml up -d
        print_success "AI services started"
    else
        print_error "AI services compose file not found"
    fi
}

# Start business services
start_business_services() {
    print_status "Starting business services..."
    
    if [ -f "docker-compose.business-saas.yml" ]; then
        docker-compose -f docker-compose.business-saas.yml up -d
        print_success "Business services started"
    else
        print_error "Business services compose file not found"
    fi
}

# Run setup scripts
run_setup_scripts() {
    print_status "Running setup scripts..."
    
    # Run PostgreSQL setup
    if [ -f "auto-connect-postgresql.sh" ]; then
        chmod +x auto-connect-postgresql.sh
        ./auto-connect-postgresql.sh || print_error "PostgreSQL setup failed"
    fi
    
    # Run Joomla setup
    if [ -f "auto-complete-joomla-install.sh" ]; then
        chmod +x auto-complete-joomla-install.sh
        ./auto-complete-joomla-install.sh || print_error "Joomla setup failed"
    fi
    
    # Fix any issues
    if [ -f "fix-all-services.sh" ]; then
        chmod +x fix-all-services.sh
        ./fix-all-services.sh || print_error "Service fixes failed"
    fi
}

# Test all services
test_all_services() {
    print_status "Testing all services..."
    
    # Define services to test
    declare -A services=(
        ["Portainer"]="http://localhost:9000"
        ["Grafana (Ultimate)"]="http://localhost:3000"
        ["Grafana (New)"]="http://localhost:3001"
        ["WordPress"]="http://localhost:1052"
        ["phpMyAdmin"]="http://localhost:2051"
        ["pgAdmin"]="http://localhost:5050"
        ["WordPress Multisite"]="http://localhost:8100"
        ["Drupal"]="http://localhost:8101"
        ["Joomla"]="http://localhost:8102"
        ["AI Chat"]="http://localhost:3002"
        ["Themer"]="http://localhost:3004"
        ["Docmost"]="http://localhost:3003"
        ["NocoDB"]="http://localhost:8080"
        ["MinIO"]="http://localhost:9003"
    )
    
    echo ""
    echo "🔍 Service Health Check Results:"
    echo "================================"
    
    working_services=0
    total_services=${#services[@]}
    
    for service in "${!services[@]}"; do
        url="${services[$service]}"
        if curl -s --max-time 3 "$url" > /dev/null 2>&1; then
            echo -e "✅ $service: ${GREEN}WORKING${NC} ($url)"
            ((working_services++))
        else
            echo -e "❌ $service: ${RED}NOT RESPONDING${NC} ($url)"
        fi
    done
    
    echo ""
    echo "📊 Service Status Summary:"
    echo "Working Services: $working_services/$total_services"
    echo "Success Rate: $(( working_services * 100 / total_services ))%"
}

# Show database connections
show_database_info() {
    print_status "Database connection information:"
    
    echo ""
    echo "🗄️ Database Services:"
    echo "===================="
    echo "PostgreSQL (Ultimate): localhost:5433"
    echo "Redis (Ultimate): localhost:6380"
    echo "Redis (Cache): localhost:6379"
    echo "MySQL (WordPress): Available via phpMyAdmin"
    echo ""
    echo "🔐 Database Credentials:"
    echo "======================="
    echo "PostgreSQL: <EMAIL> / Encarnacion12@amd12"
    echo "SoloYlibre Unified: SoloYlibre / 57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd"
    echo "phpMyAdmin: Access via http://localhost:2051"
}

# Generate service documentation
generate_quick_docs() {
    print_status "Generating service documentation..."
    
    cat > CURRENT_SERVICES_STATUS.md << EOF
# 🚀 SoloYlibre & JEYKO Current Services Status
## Running Services and Access Information

### 📊 Currently Running Services
Generated: $(date)
Total Containers: $(docker ps -q | wc -l)

### 🌐 Web Services
- **Portainer**: http://localhost:9000 (Container Management)
- **Grafana Ultimate**: http://localhost:3000 (Monitoring)
- **Grafana New**: http://localhost:3001 (Additional Monitoring)
- **WordPress**: http://localhost:1052 (Main WordPress)
- **phpMyAdmin**: http://localhost:2051 (Database Management)

### 🗄️ Database Services
- **PostgreSQL**: localhost:5433 (<EMAIL> / Encarnacion12@amd12)
- **Redis Ultimate**: localhost:6380
- **Redis Cache**: localhost:6379
- **MySQL**: Available via phpMyAdmin

### 🔧 Management Tools
- **pgAdmin**: http://localhost:5050 (PostgreSQL Management)
- **Portainer**: http://localhost:9000 (Docker Management)

### 🎯 Next Steps
1. Complete CMS suite setup (WordPress Multisite, Drupal, Joomla)
2. Start AI services (AI Chat, Themer, Docmost)
3. Configure business services (NocoDB, MinIO)
4. Test all service connections

### 📞 Support Information
- **Head Developer**: Jose L Encarnacion
- **Company**: SoloYlibre & JEYKO
- **PostgreSQL Admin**: <EMAIL>
- **Platform**: Ultimate Development Environment
EOF
    
    print_success "Documentation generated: CURRENT_SERVICES_STATUS.md"
}

# Show final status
show_final_status() {
    echo ""
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║              🎉 SERVICES SETUP COMPLETE! 🎉                ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🚀 SoloYlibre & JEYKO Services Status                     ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    
    # Count running containers
    total_containers=$(docker ps -q | wc -l)
    echo -e "${GREEN}║  📦 Total Containers Running: $total_containers                        ║${NC}"
    
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🌐 Key Service URLs:                                       ║${NC}"
    echo -e "${GREEN}║  • Portainer: http://localhost:9000                        ║${NC}"
    echo -e "${GREEN}║  • Grafana: http://localhost:3000                          ║${NC}"
    echo -e "${GREEN}║  • WordPress: http://localhost:1052                        ║${NC}"
    echo -e "${GREEN}║  • phpMyAdmin: http://localhost:2051                       ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🔐 PostgreSQL: <EMAIL> / Encarnacion12@amd12 ║${NC}"
    echo -e "${GREEN}║  📋 Documentation: CURRENT_SERVICES_STATUS.md              ║${NC}"
    echo -e "${GREEN}║  🏢 Company: SoloYlibre & JEYKO                            ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    
    echo ""
    print_success "Services setup completed!"
    print_status "Check CURRENT_SERVICES_STATUS.md for detailed information"
}

# Main execution
main() {
    print_header
    
    check_current_services
    start_cms_services
    start_ai_services
    start_business_services
    run_setup_scripts
    
    print_status "Waiting for services to initialize..."
    sleep 15
    
    test_all_services
    show_database_info
    generate_quick_docs
    show_final_status
}

# Run the setup
main "$@"
