version: '3.8'

services:
  # Traefik Reverse Proxy & Load Balancer
  traefik:
    image: traefik:v3.0
    container_name: master-traefik
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik Dashboard
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/acme.json
      - --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/acme.json:/acme.json
    networks:
      - master-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.localhost`)"
      - "traefik.http.routers.traefik.entrypoints=web"

  # Shared PostgreSQL Cluster
  postgres-master:
    image: postgres:15-alpine
    container_name: master-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=master_db
      - POSTGRES_USER=master_user
      - POSTGRES_PASSWORD=master_pass_123
      - POSTGRES_MULTIPLE_DATABASES=project1,project2,soloylibre,wordpress
    volumes:
      - postgres_master_data:/var/lib/postgresql/data
      - ./database/init-multiple-databases.sh:/docker-entrypoint-initdb.d/init-multiple-databases.sh
    networks:
      - master-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U master_user"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Shared Redis Cluster
  redis-master:
    image: redis:7-alpine
    container_name: master-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --cluster-enabled yes
    volumes:
      - redis_master_data:/data
    networks:
      - master-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Shared MinIO Storage
  minio-master:
    image: minio/minio:latest
    container_name: master-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=josetusabe
      - MINIO_ROOT_PASSWORD=josetusabe123
    command: server /data --console-address ":9001"
    volumes:
      - minio_master_data:/data
    networks:
      - master-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.minio.rule=Host(`files.localhost`)"
      - "traefik.http.routers.minio.entrypoints=web"
      - "traefik.http.services.minio.loadbalancer.server.port=9001"

  # Centralized Prometheus
  prometheus-master:
    image: prom/prometheus:latest
    container_name: master-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/rules:/etc/prometheus/rules
      - prometheus_master_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.time=30d'
    networks:
      - master-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prometheus.localhost`)"
      - "traefik.http.routers.prometheus.entrypoints=web"

  # Centralized Grafana
  grafana-master:
    image: grafana/grafana:latest
    container_name: master-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=josetusabe123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=http://grafana.localhost
      - GF_DATABASE_TYPE=postgres
      - GF_DATABASE_HOST=postgres-master:5432
      - GF_DATABASE_NAME=grafana
      - GF_DATABASE_USER=master_user
      - GF_DATABASE_PASSWORD=master_pass_123
    volumes:
      - grafana_master_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - master-network
    depends_on:
      - postgres-master
      - prometheus-master
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.localhost`)"
      - "traefik.http.routers.grafana.entrypoints=web"

  # Centralized Jaeger
  jaeger-master:
    image: jaegertracing/all-in-one:latest
    container_name: master-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - SPAN_STORAGE_TYPE=elasticsearch
      - ES_SERVER_URLS=http://elasticsearch:9200
    networks:
      - master-network
    depends_on:
      - elasticsearch
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.jaeger.rule=Host(`jaeger.localhost`)"
      - "traefik.http.routers.jaeger.entrypoints=web"
      - "traefik.http.services.jaeger.loadbalancer.server.port=16686"

  # Elasticsearch for Logs
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: master-elasticsearch
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - master-network

  # Kibana for Log Visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: master-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    networks:
      - master-network
    depends_on:
      - elasticsearch
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.kibana.rule=Host(`kibana.localhost`)"
      - "traefik.http.routers.kibana.entrypoints=web"

  # GitLab CE for Git + CI/CD
  gitlab:
    image: gitlab/gitlab-ce:latest
    container_name: master-gitlab
    hostname: 'gitlab.localhost'
    ports:
      - "8929:80"
      - "8922:22"
    environment:
      - GITLAB_OMNIBUS_CONFIG=|
          external_url 'http://gitlab.localhost'
          gitlab_rails['gitlab_shell_ssh_port'] = 8922
    volumes:
      - gitlab_config:/etc/gitlab
      - gitlab_logs:/var/log/gitlab
      - gitlab_data:/var/opt/gitlab
    networks:
      - master-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.gitlab.rule=Host(`gitlab.localhost`)"
      - "traefik.http.routers.gitlab.entrypoints=web"

  # Harbor Container Registry
  harbor-core:
    image: goharbor/harbor-core:v2.9.0
    container_name: master-harbor
    ports:
      - "8930:8080"
    environment:
      - CORE_SECRET=harbor_secret_123
      - JOBSERVICE_SECRET=jobservice_secret_123
    volumes:
      - harbor_data:/data
    networks:
      - master-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.harbor.rule=Host(`harbor.localhost`)"
      - "traefik.http.routers.harbor.entrypoints=web"

  # RustDesk Server for Remote Access
  rustdesk-server:
    image: rustdesk/rustdesk-server:latest
    container_name: master-rustdesk
    ports:
      - "21115:21115"
      - "21116:21116"
      - "21116:21116/udp"
      - "21117:21117"
      - "21118:21118"
      - "21119:21119"
    volumes:
      - rustdesk_data:/data
    networks:
      - master-network

  # Backup Service (Restic + Rclone)
  backup-service:
    build:
      context: ./backup
      dockerfile: Dockerfile
    container_name: master-backup
    environment:
      - RESTIC_REPOSITORY=/backup/restic
      - RESTIC_PASSWORD=backup_password_123
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
    volumes:
      - backup_data:/backup
      - postgres_master_data:/source/postgres:ro
      - redis_master_data:/source/redis:ro
      - minio_master_data:/source/minio:ro
      - ./backup/scripts:/scripts
    networks:
      - master-network

volumes:
  postgres_master_data:
  redis_master_data:
  minio_master_data:
  prometheus_master_data:
  grafana_master_data:
  elasticsearch_data:
  gitlab_config:
  gitlab_logs:
  gitlab_data:
  harbor_data:
  rustdesk_data:
  backup_data:

networks:
  master-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
