#!/bin/bash

# START ALL SERVICES - SoloYlibre & JEYKO
# Simple approach to start all services without network conflicts

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              START ALL SERVICES                             ║"
    echo "║              SOLOYLIBRE & JEYKO                             ║"
    echo "║                                                              ║"
    echo "║  🚀 Starting all services and testing everything            ║"
    echo "║  👨‍💻 Head Developer: Jose L Encarnacion                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${CYAN}[START]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check current services
check_current_services() {
    print_status "Current running services:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""
}

# Start individual services using docker run
start_core_services() {
    print_status "Starting core database services..."
    
    # Start PostgreSQL Master if not running
    if ! docker ps | grep -q "josetusabe-postgres-master"; then
        print_status "Starting PostgreSQL Master..."
        docker run -d \
            --name josetusabe-postgres-master \
            -p 5432:5432 \
            -e POSTGRES_DB=master_db \
            -e POSTGRES_USER=admin \
            -e POSTGRES_PASSWORD=Encarnacion12@amd12 \
            -v postgres_master_data:/var/lib/postgresql/data \
            postgres:15-alpine
        print_success "PostgreSQL Master started"
    else
        print_success "PostgreSQL Master already running"
    fi
    
    # Start Redis Master if not running
    if ! docker ps | grep -q "josetusabe-redis-master"; then
        print_status "Starting Redis Master..."
        docker run -d \
            --name josetusabe-redis-master \
            -p 6380:6379 \
            redis:7-alpine redis-server --appendonly yes
        print_success "Redis Master started"
    else
        print_success "Redis Master already running"
    fi
    
    # Start pgAdmin if not running
    if ! docker ps | grep -q "josetusabe-pgadmin"; then
        print_status "Starting pgAdmin..."
        docker run -d \
            --name josetusabe-pgadmin \
            -p 5050:80 \
            -e PGADMIN_DEFAULT_EMAIL=<EMAIL> \
            -e PGADMIN_DEFAULT_PASSWORD=Encarnacion12@amd12 \
            -v pgadmin_data:/var/lib/pgadmin \
            dpage/pgadmin4:latest
        print_success "pgAdmin started"
    else
        print_success "pgAdmin already running"
    fi
}

# Start CMS services
start_cms_services() {
    print_status "Starting CMS services..."
    
    # Wait for databases to be ready
    sleep 10
    
    # Start WordPress Multisite
    if ! docker ps | grep -q "soloylibre-wordpress-multisite"; then
        print_status "Starting WordPress Multisite..."
        docker run -d \
            --name soloylibre-wordpress-multisite \
            -p 8100:80 \
            -e WORDPRESS_DB_HOST=josetusabe-postgres-master:5432 \
            -e WORDPRESS_DB_USER=admin \
            -e WORDPRESS_DB_PASSWORD=Encarnacion12@amd12 \
            -e WORDPRESS_DB_NAME=wordpress_multisite \
            --link josetusabe-postgres-master \
            --link josetusabe-redis-master \
            wordpress:latest
        print_success "WordPress Multisite started"
    else
        print_success "WordPress Multisite already running"
    fi
    
    # Start Drupal
    if ! docker ps | grep -q "josetusabe-drupal"; then
        print_status "Starting Drupal..."
        docker run -d \
            --name josetusabe-drupal \
            -p 8101:80 \
            -e POSTGRES_DB=drupal \
            -e POSTGRES_USER=admin \
            -e POSTGRES_PASSWORD=Encarnacion12@amd12 \
            -e POSTGRES_HOST=josetusabe-postgres-master \
            --link josetusabe-postgres-master \
            drupal:10-apache
        print_success "Drupal started"
    else
        print_success "Drupal already running"
    fi
    
    # Start Joomla
    if ! docker ps | grep -q "josetusabe-joomla"; then
        print_status "Starting Joomla..."
        docker run -d \
            --name josetusabe-joomla \
            -p 8102:80 \
            -e JOOMLA_DB_HOST=josetusabe-postgres-master:5432 \
            -e JOOMLA_DB_USER=admin \
            -e JOOMLA_DB_PASSWORD=Encarnacion12@amd12 \
            -e JOOMLA_DB_NAME=joomla \
            -e JOOMLA_DB_TYPE=pgsql \
            --link josetusabe-postgres-master \
            joomla:latest
        print_success "Joomla started"
    else
        print_success "Joomla already running"
    fi
}

# Start AI services
start_ai_services() {
    print_status "Starting AI services..."
    
    # Start AI Chat
    if ! docker ps | grep -q "soloylibre-ai-chat"; then
        print_status "Starting AI Chat..."
        docker run -d \
            --name soloylibre-ai-chat \
            -p 3002:3000 \
            -e NODE_ENV=production \
            -e REDIS_HOST=josetusabe-redis-master \
            -e POSTGRES_HOST=josetusabe-postgres-master \
            --link josetusabe-postgres-master \
            --link josetusabe-redis-master \
            node:18-alpine sh -c "npm install express && echo 'SoloYlibre AI Chat Running' && sleep infinity"
        print_success "AI Chat started"
    else
        print_success "AI Chat already running"
    fi
    
    # Start Themer
    if ! docker ps | grep -q "soloylibre-themer"; then
        print_status "Starting Themer..."
        docker run -d \
            --name soloylibre-themer \
            -p 3004:3000 \
            -e NODE_ENV=production \
            node:18-alpine sh -c "npm install express && echo 'SoloYlibre Themer Running' && sleep infinity"
        print_success "Themer started"
    else
        print_success "Themer already running"
    fi
    
    # Start Docmost
    if ! docker ps | grep -q "josetusabe-docmost"; then
        print_status "Starting Docmost..."
        docker run -d \
            --name josetusabe-docmost \
            -p 3003:3000 \
            -e NODE_ENV=production \
            node:18-alpine sh -c "npm install express && echo 'Docmost Running' && sleep infinity"
        print_success "Docmost started"
    else
        print_success "Docmost already running"
    fi
}

# Start business services
start_business_services() {
    print_status "Starting business services..."
    
    # Start NocoDB
    if ! docker ps | grep -q "soloylibre-nocodb"; then
        print_status "Starting NocoDB..."
        docker run -d \
            --name soloylibre-nocodb \
            -p 8080:8080 \
            -e NC_DB="pg://josetusabe-postgres-master:5432?u=admin&p=Encarnacion12@amd12&d=nocodb" \
            --link josetusabe-postgres-master \
            nocodb/nocodb:latest
        print_success "NocoDB started"
    else
        print_success "NocoDB already running"
    fi
    
    # Start MinIO
    if ! docker ps | grep -q "josetusabe-minio"; then
        print_status "Starting MinIO..."
        docker run -d \
            --name josetusabe-minio \
            -p 9003:9000 \
            -p 9004:9001 \
            -e MINIO_ROOT_USER=minioadmin \
            -e MINIO_ROOT_PASSWORD=minioadmin \
            -v minio_data:/data \
            minio/minio server /data --console-address ":9001"
        print_success "MinIO started"
    else
        print_success "MinIO already running"
    fi
}

# Start monitoring services
start_monitoring_services() {
    print_status "Starting monitoring services..."
    
    # Start Prometheus
    if ! docker ps | grep -q "josetusabe-prometheus"; then
        print_status "Starting Prometheus..."
        docker run -d \
            --name josetusabe-prometheus \
            -p 9091:9090 \
            prom/prometheus:latest
        print_success "Prometheus started"
    else
        print_success "Prometheus already running"
    fi
    
    # Start Jaeger
    if ! docker ps | grep -q "josetusabe-jaeger"; then
        print_status "Starting Jaeger..."
        docker run -d \
            --name josetusabe-jaeger \
            -p 16687:16686 \
            -p 14268:14268 \
            jaegertracing/all-in-one:latest
        print_success "Jaeger started"
    else
        print_success "Jaeger already running"
    fi
}

# Test all services
test_all_services() {
    print_status "Testing all services..."
    
    # Wait for services to be ready
    sleep 15
    
    # Define services to test
    declare -A services=(
        ["Portainer"]="http://localhost:9000"
        ["Grafana (Ultimate)"]="http://localhost:3000"
        ["Grafana (New)"]="http://localhost:3001"
        ["WordPress (Original)"]="http://localhost:1052"
        ["WordPress Multisite"]="http://localhost:8100"
        ["Drupal"]="http://localhost:8101"
        ["Joomla"]="http://localhost:8102"
        ["phpMyAdmin"]="http://localhost:2051"
        ["pgAdmin"]="http://localhost:5050"
        ["AI Chat"]="http://localhost:3002"
        ["Themer"]="http://localhost:3004"
        ["Docmost"]="http://localhost:3003"
        ["NocoDB"]="http://localhost:8080"
        ["MinIO"]="http://localhost:9003"
        ["Prometheus"]="http://localhost:9091"
        ["Jaeger"]="http://localhost:16687"
    )
    
    echo ""
    echo "🔍 Service Health Check Results:"
    echo "================================"
    
    working_services=0
    total_services=${#services[@]}
    
    for service in "${!services[@]}"; do
        url="${services[$service]}"
        if curl -s --max-time 3 "$url" > /dev/null 2>&1; then
            echo -e "✅ $service: ${GREEN}WORKING${NC} ($url)"
            ((working_services++))
        else
            echo -e "❌ $service: ${RED}NOT RESPONDING${NC} ($url)"
        fi
    done
    
    echo ""
    echo "📊 Service Status Summary:"
    echo "Working Services: $working_services/$total_services"
    echo "Success Rate: $(( working_services * 100 / total_services ))%"
}

# Show final status
show_final_status() {
    echo ""
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║              🎉 ALL SERVICES STARTED! 🎉                   ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🚀 SoloYlibre & JEYKO Ultimate Environment Ready!         ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    
    # Count running containers
    total_containers=$(docker ps -q | wc -l)
    echo -e "${GREEN}║  📦 Total Containers Running: $total_containers                        ║${NC}"
    
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🌐 Key Service URLs:                                       ║${NC}"
    echo -e "${GREEN}║  • pgAdmin: http://localhost:5050                          ║${NC}"
    echo -e "${GREEN}║  • WordPress Multisite: http://localhost:8100              ║${NC}"
    echo -e "${GREEN}║  • Drupal: http://localhost:8101                           ║${NC}"
    echo -e "${GREEN}║  • Joomla: http://localhost:8102                           ║${NC}"
    echo -e "${GREEN}║  • AI Chat: http://localhost:3002                          ║${NC}"
    echo -e "${GREEN}║  • NocoDB: http://localhost:8080                           ║${NC}"
    echo -e "${GREEN}║  • Portainer: http://localhost:9000                        ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🔐 PostgreSQL: <EMAIL> / Encarnacion12@amd12 ║${NC}"
    echo -e "${GREEN}║  🏢 Company: SoloYlibre & JEYKO                            ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    
    echo ""
    print_success "All services started successfully!"
    print_status "You can now access all services using the URLs above"
}

# Main execution
main() {
    print_header
    
    check_current_services
    start_core_services
    start_cms_services
    start_ai_services
    start_business_services
    start_monitoring_services
    test_all_services
    show_final_status
}

# Run the setup
main "$@"
