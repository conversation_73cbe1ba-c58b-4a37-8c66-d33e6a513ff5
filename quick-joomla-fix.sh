#!/bin/bash

# QUICK JOOMLA 500 ERROR FIX
# SoloYlibre & J<PERSON>YKO Dev - Head Developer: <PERSON> L Encarnacion

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[QUICK FIX]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                QUICK JOOMLA 500 ERROR FIX                   ║"
echo "║                SOLOYLIBRE & JEYKO DEV                       ║"
echo "║                                                              ║"
echo "║  🚀 Rapid repair for immediate functionality                 ║"
echo "║  👨‍💻 Head Developer: Jose L Encarnacion                      ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Wait for containers to be ready
print_status "Waiting for containers to be ready..."
sleep 30

# Fix database permissions
print_status "Fixing database permissions..."
docker exec soloylibre-mysql-joomla mysql -u root -pSoloYlibre_MySQL_Root_2024! -e "
    GRANT ALL PRIVILEGES ON joomla_db.* TO 'joomla_user'@'%';
    FLUSH PRIVILEGES;
"

# Create basic Joomla tables
print_status "Creating basic Joomla tables..."
docker exec soloylibre-mysql-joomla mysql -u joomla_user -pSoloYlibre_Joomla_2024! joomla_db << 'EOF'
CREATE TABLE IF NOT EXISTS `sol_session` (
  `session_id` varbinary(192) NOT NULL,
  `client_id` tinyint unsigned NOT NULL DEFAULT '0',
  `guest` tinyint unsigned DEFAULT '1',
  `time` int NOT NULL DEFAULT '0',
  `data` mediumtext,
  `userid` int DEFAULT '0',
  `username` varchar(150) DEFAULT '',
  PRIMARY KEY (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `sol_users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(400) NOT NULL DEFAULT '',
  `username` varchar(150) NOT NULL DEFAULT '',
  `email` varchar(100) NOT NULL DEFAULT '',
  `password` varchar(100) NOT NULL DEFAULT '',
  `block` tinyint NOT NULL DEFAULT '0',
  `sendEmail` tinyint DEFAULT '0',
  `registerDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `lastvisitDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `activation` varchar(100) NOT NULL DEFAULT '',
  `params` text NOT NULL,
  `lastResetTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `resetCount` int NOT NULL DEFAULT '0',
  `otpKey` varchar(1000) NOT NULL DEFAULT '',
  `otep` varchar(1000) NOT NULL DEFAULT '',
  `requireReset` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `sol_users` (`id`, `name`, `username`, `email`, `password`, `block`, `sendEmail`) VALUES
(1, 'Jose L Encarnacion - SoloYlibre', 'SoloYlibre', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 0, 1)
ON DUPLICATE KEY UPDATE name=VALUES(name);
EOF

# Fix file permissions
print_status "Fixing file permissions..."
docker exec josetusabe-joomla bash -c "
    cd /var/www/html
    chown -R www-data:www-data /var/www/html
    chmod -R 755 /var/www/html
    mkdir -p tmp logs cache administrator/cache administrator/logs images media
    chmod -R 777 tmp logs cache administrator/cache administrator/logs images media
"

# Create configuration.php
print_status "Creating configuration.php..."
docker exec josetusabe-joomla bash -c "
cd /var/www/html
cat > configuration.php << 'EOF'
<?php
class JConfig {
    public \$offline = '0';
    public \$offline_message = 'SoloYlibre site is temporarily offline for maintenance.';
    public \$display_offline_message = '1';
    public \$offline_image = '';
    public \$sitename = 'SoloYlibre Joomla Platform';
    public \$editor = 'tinymce';
    public \$captcha = '0';
    public \$list_limit = '20';
    public \$access = '1';
    public \$debug = '0';
    public \$debug_lang = '0';
    public \$debug_lang_const = '1';
    public \$dbtype = 'mysqli';
    public \$host = 'soloylibre-mysql-joomla';
    public \$user = 'joomla_user';
    public \$password = 'SoloYlibre_Joomla_2024!';
    public \$db = 'joomla_db';
    public \$dbprefix = 'sol_';
    public \$live_site = '';
    public \$secret = 'SoloYlibre_Secret_Key_2024_JEYKO_QuickFix';
    public \$gzip = '0';
    public \$error_reporting = 'default';
    public \$helpurl = 'https://help.joomla.org/proxy?keyref=Help{major}{minor}:{keyref}&lang={langcode}';
    public \$offset = 'UTC';
    public \$mailonline = '1';
    public \$mailer = 'mail';
    public \$mailfrom = '<EMAIL>';
    public \$fromname = 'SoloYlibre';
    public \$sendmail = '/usr/sbin/sendmail';
    public \$smtpauth = '0';
    public \$smtpuser = '';
    public \$smtppass = '';
    public \$smtphost = 'localhost';
    public \$smtpsecure = 'none';
    public \$smtpport = '25';
    public \$caching = '0';
    public \$cache_handler = 'file';
    public \$cachetime = '15';
    public \$cache_platformprefix = '0';
    public \$MetaDesc = 'SoloYlibre business platform powered by JEYKO AI division';
    public \$MetaKeys = 'SoloYlibre, JEYKO, AI, business, platform';
    public \$MetaTitle = '1';
    public \$MetaAuthor = '1';
    public \$MetaVersion = '0';
    public \$robots = '';
    public \$sef = '1';
    public \$sef_rewrite = '0';
    public \$sef_suffix = '0';
    public \$unicodeslugs = '0';
    public \$feed_limit = '10';
    public \$feed_email = 'none';
    public \$log_path = '/var/www/html/administrator/logs';
    public \$tmp_path = '/var/www/html/tmp';
    public \$lifetime = '15';
    public \$session_handler = 'database';
    public \$shared_session = '0';
}
EOF

chown www-data:www-data configuration.php
chmod 644 configuration.php
"

# Remove installation directory
print_status "Removing installation directory..."
docker exec josetusabe-joomla rm -rf /var/www/html/installation

# Test the fix
print_status "Testing Joomla..."
sleep 10

response=$(curl -s -w "%{http_code}" http://localhost:8102 -o /tmp/test.html)

if [ "$response" = "200" ]; then
    print_success "Joomla is responding with HTTP 200!"
    if grep -q "SoloYlibre\|Joomla" /tmp/test.html 2>/dev/null; then
        print_success "Joomla content is loading correctly!"
    else
        print_success "Joomla is responding (may need content setup)"
    fi
else
    print_error "Joomla still has issues (HTTP $response)"
    echo "Response content:"
    head -5 /tmp/test.html 2>/dev/null || echo "No content"
fi

rm -f /tmp/test.html

echo ""
echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║                🎉 QUICK FIX COMPLETED! 🎉                   ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║  🌐 Test: http://localhost:8102                            ║${NC}"
echo -e "${GREEN}║  🔧 Admin: http://localhost:8102/administrator             ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║  🔐 Username: SoloYlibre                                   ║${NC}"
echo -e "${GREEN}║  🔑 Password: 57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd          ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║  🏢 SoloYlibre & JEYKO - Testing Ready! 🚀               ║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"

print_success "Quick fix completed! Test the URLs above."
