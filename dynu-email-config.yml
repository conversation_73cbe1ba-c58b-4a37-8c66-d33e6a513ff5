# Dynu Email Server Integration Configuration
# Jose L Encarnacion (JoseTusabe) - Professional 2000% IQ Setup

# Environment Variables for Dynu Integration
# Create a .env file with these variables:

# DYNU Email Configuration
DYNU_EMAIL_USER=<EMAIL>
DYNU_EMAIL_PASS=your-dynu-password
DYNU_EMAIL_FROM=<EMAIL>
DYNU_SMTP_HOST=smtp.dynu.com
DYNU_SMTP_PORT=587
DYNU_IMAP_HOST=imap.dynu.com
DYNU_IMAP_PORT=993

# Domain Configuration
DYNU_DOMAIN_1=soloylibre.com
DYNU_DOMAIN_2=josetusabe.com
DYNU_DOMAIN_3=1and1photo.com
DYNU_DOMAIN_4=joselencarnacion.com

# Email Aliases Configuration
DYNU_ALIAS_ADMIN=<EMAIL>
DYNU_ALIAS_SUPPORT=<EMAIL>
DYNU_ALIAS_NOREPLY=<EMAIL>
DYNU_ALIAS_NOTIFICATIONS=<EMAIL>

---

# Docker Compose Override for Email Integration
version: '3.8'

services:
  # Email Service Integration
  email-service:
    image: alpine:latest
    container_name: josetusabe-email-service
    command: |
      sh -c "
        apk add --no-cache curl postfix mailx &&
        echo 'relayhost = [smtp.dynu.com]:587' >> /etc/postfix/main.cf &&
        echo 'smtp_use_tls = yes' >> /etc/postfix/main.cf &&
        echo 'smtp_sasl_auth_enable = yes' >> /etc/postfix/main.cf &&
        echo 'smtp_sasl_security_options = noanonymous' >> /etc/postfix/main.cf &&
        echo 'smtp_sasl_password_maps = hash:/etc/postfix/sasl_passwd' >> /etc/postfix/main.cf &&
        echo '[smtp.dynu.com]:587 ${DYNU_EMAIL_USER}:${DYNU_EMAIL_PASS}' > /etc/postfix/sasl_passwd &&
        postmap /etc/postfix/sasl_passwd &&
        chmod 600 /etc/postfix/sasl_passwd* &&
        postfix start &&
        tail -f /var/log/mail.log
      "
    environment:
      - DYNU_EMAIL_USER=${DYNU_EMAIL_USER}
      - DYNU_EMAIL_PASS=${DYNU_EMAIL_PASS}
    volumes:
      - email_logs:/var/log
      - email_config:/etc/postfix
    networks:
      - ai-network
    restart: unless-stopped

  # Update Project1 with Email Integration
  project1-notifications:
    environment:
      # Add Dynu SMTP configuration
      - SMTP_HOST=smtp.dynu.com
      - SMTP_PORT=587
      - SMTP_USER=${DYNU_EMAIL_USER}
      - SMTP_PASS=${DYNU_EMAIL_PASS}
      - SMTP_FROM=${DYNU_EMAIL_FROM}
      - SMTP_TLS=true
      - EMAIL_TEMPLATES_PATH=/app/templates/email

  # Update Project2 with Email Integration  
  project2-notifications:
    environment:
      # Add Dynu SMTP configuration
      - SMTP_HOST=smtp.dynu.com
      - SMTP_PORT=587
      - SMTP_USER=${DYNU_EMAIL_USER}
      - SMTP_PASS=${DYNU_EMAIL_PASS}
      - SMTP_FROM=${DYNU_EMAIL_FROM}
      - SMTP_TLS=true
      - EMAIL_QUEUE_REDIS=redis://redis-ai:6379/1

  # Update WordPress with Email Integration
  wordpress:
    environment:
      # WordPress SMTP Configuration
      - WORDPRESS_CONFIG_EXTRA=|
        define('SMTP_HOST', 'smtp.dynu.com');
        define('SMTP_PORT', 587);
        define('SMTP_USER', '${DYNU_EMAIL_USER}');
        define('SMTP_PASS', '${DYNU_EMAIL_PASS}');
        define('SMTP_FROM', '${DYNU_EMAIL_FROM}');
        define('SMTP_FROMNAME', 'JoseTusabe');

  # Update n8n with Enhanced Email Workflows
  n8n:
    environment:
      # Enhanced email configuration
      - N8N_SMTP_HOST=smtp.dynu.com
      - N8N_SMTP_PORT=587
      - N8N_SMTP_USER=${DYNU_EMAIL_USER}
      - N8N_SMTP_PASS=${DYNU_EMAIL_PASS}
      - N8N_SMTP_SENDER=${DYNU_EMAIL_FROM}
      - N8N_SMTP_SSL=false
      - N8N_SMTP_TLS=true
      # IMAP configuration for reading emails
      - N8N_IMAP_HOST=imap.dynu.com
      - N8N_IMAP_PORT=993
      - N8N_IMAP_USER=${DYNU_EMAIL_USER}
      - N8N_IMAP_PASS=${DYNU_EMAIL_PASS}
      - N8N_IMAP_SSL=true

volumes:
  email_logs:
  email_config:

---

# Email Testing and Monitoring Configuration
email_monitoring:
  image: mailhog/mailhog:latest
  container_name: josetusabe-email-monitor
  ports:
    - "1025:1025"  # SMTP
    - "8025:8025"  # Web UI
  environment:
    - MH_STORAGE=maildir
    - MH_MAILDIR_PATH=/maildir
  volumes:
    - email_monitor_data:/maildir
  networks:
    - ai-network
  restart: unless-stopped
  labels:
    - "traefik.enable=true"
    - "traefik.http.routers.mailhog.rule=Host(`mail-test.localhost`) || Host(`mail-test.soloylibre.com`)"
    - "traefik.http.routers.mailhog.entrypoints=web,websecure"
    - "traefik.http.routers.mailhog.tls=true"
    - "traefik.http.services.mailhog.loadbalancer.server.port=8025"

# Email Queue Worker
email_worker:
  build:
    context: ./email-worker
    dockerfile: Dockerfile
  container_name: josetusabe-email-worker
  environment:
    - REDIS_URL=redis://redis-ai:6379/2
    - SMTP_HOST=smtp.dynu.com
    - SMTP_PORT=587
    - SMTP_USER=${DYNU_EMAIL_USER}
    - SMTP_PASS=${DYNU_EMAIL_PASS}
    - SMTP_FROM=${DYNU_EMAIL_FROM}
    - WORKER_CONCURRENCY=5
    - RETRY_ATTEMPTS=3
  volumes:
    - ./email-worker/templates:/app/templates
    - email_worker_logs:/app/logs
  networks:
    - ai-network
  depends_on:
    - redis-ai
  restart: unless-stopped

volumes:
  email_monitor_data:
  email_worker_logs:
