services:
  # WordPress Multisite CMS - SoloYlibre & JEYKO
  wordpress-multisite:
    image: wordpress:latest
    container_name: soloylibre-wordpress-multisite
    ports:
      - "8100:80"
    environment:
      - WORDPRESS_DB_HOST=soloylibre-postgres-cms:5432
      - WORDPRESS_DB_USER=cms_user
      - WORDPRESS_DB_PASSWORD=CMS_JoseTusabe_2024!
      - WORDPRESS_DB_NAME=wordpress_multisite
      - WORDPRESS_CONFIG_EXTRA=
          define('WP_ALLOW_MULTISITE', true);
          define('MULTISITE', true);
          define('SUBDOMAIN_INSTALL', false);
          define('DOMAIN_CURRENT_SITE', 'wordpress.soloylibre.com');
          define('PATH_CURRENT_SITE', '/');
          define('SITE_ID_CURRENT_SITE', 1);
          define('BLOG_ID_CURRENT_SITE', 1);
          define('WP_REDIS_HOST', 'josetusabe-redis-master');
          define('WP_REDIS_PORT', 6379);
          define('WP_REDIS_PASSWORD', 'Redis_Master_2024!');
          define('ELEVENLABS_API_KEY', '${ELEVENLABS_API_KEY}');
          define('ZONOS_API_KEY', '${ZONOS_API_KEY}');
    volumes:
      - wordpress_multisite_data:/var/www/html
      - ./cms-configs/wordpress/plugins:/var/www/html/wp-content/plugins/custom
      - ./cms-configs/wordpress/themes:/var/www/html/wp-content/themes/custom
    networks:
      - cms-network
      - core-network
    depends_on:
      - postgres-cms
      - redis-cms
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.wordpress.rule=Host(`wordpress.soloylibre.com`)"
      - "traefik.http.routers.wordpress.entrypoints=websecure"
      - "traefik.http.routers.wordpress.tls=true"

  # Drupal CMS
  drupal:
    image: drupal:10-apache
    container_name: josetusabe-drupal
    ports:
      - "8101:80"
    environment:
      - POSTGRES_DB=drupal
      - POSTGRES_USER=master_user
      - POSTGRES_PASSWORD=JoseTusabe_Master_2024!
      - POSTGRES_HOST=josetusabe-postgres-master
      - POSTGRES_PORT=5432
      - DRUPAL_REDIS_HOST=josetusabe-redis-master
      - DRUPAL_REDIS_PORT=6379
      - DRUPAL_REDIS_PASSWORD=Redis_Master_2024!
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - ZONOS_API_KEY=${ZONOS_API_KEY}
    volumes:
      - drupal_data:/var/www/html
      - ./cms-configs/drupal/modules:/var/www/html/modules/custom
      - ./cms-configs/drupal/themes:/var/www/html/themes/custom
      - ./cms-configs/drupal/settings.php:/var/www/html/sites/default/settings.php
    networks:
      - cms-network
      - core-network
    depends_on:
      - postgres-cms
      - redis-cms
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.drupal.rule=Host(`drupal.soloylibre.com`)"
      - "traefik.http.routers.drupal.entrypoints=websecure"
      - "traefik.http.routers.drupal.tls=true"

  # Joomla CMS
  joomla:
    image: joomla:latest
    container_name: josetusabe-joomla
    ports:
      - "8102:80"
    environment:
      - JOOMLA_DB_HOST=josetusabe-postgres-master:5432
      - JOOMLA_DB_USER=master_user
      - JOOMLA_DB_PASSWORD=JoseTusabe_Master_2024!
      - JOOMLA_DB_NAME=joomla
      - JOOMLA_DB_TYPE=pgsql
      - REDIS_HOST=josetusabe-redis-master
      - REDIS_PORT=6379
      - REDIS_PASSWORD=Redis_Master_2024!
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - ZONOS_API_KEY=${ZONOS_API_KEY}
    volumes:
      - joomla_data:/var/www/html
      - ./cms-configs/joomla/extensions:/var/www/html/administrator/components/custom
      - ./cms-configs/joomla/templates:/var/www/html/templates/custom
    networks:
      - cms-network
      - core-network
    depends_on:
      - postgres-cms
      - redis-cms
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.joomla.rule=Host(`joomla.soloylibre.com`)"
      - "traefik.http.routers.joomla.entrypoints=websecure"
      - "traefik.http.routers.joomla.tls=true"

  # Ghost CMS
  ghost:
    image: ghost:latest
    container_name: josetusabe-ghost
    ports:
      - "8103:2368"
    environment:
      - NODE_ENV=production
      - url=https://ghost.soloylibre.com
      - database__client=postgres
      - database__connection__host=josetusabe-postgres-master
      - database__connection__port=5432
      - database__connection__user=master_user
      - database__connection__password=JoseTusabe_Master_2024!
      - database__connection__database=ghost
      - mail__transport=SMTP
      - mail__options__service=SMTP
      - mail__options__host=smtp.dynu.com
      - mail__options__port=587
      - mail__options__auth__user=${DYNU_EMAIL_USER}
      - mail__options__auth__pass=${DYNU_EMAIL_PASS}
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - ZONOS_API_KEY=${ZONOS_API_KEY}
    volumes:
      - ghost_data:/var/lib/ghost/content
      - ./cms-configs/ghost/themes:/var/lib/ghost/content/themes/custom
    networks:
      - cms-network
      - core-network
    depends_on:
      - postgres-cms
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ghost.rule=Host(`ghost.soloylibre.com`)"
      - "traefik.http.routers.ghost.entrypoints=websecure"
      - "traefik.http.routers.ghost.tls=true"

  # Strapi Headless CMS
  strapi:
    image: strapi/strapi:latest
    container_name: josetusabe-strapi
    ports:
      - "8104:1337"
    environment:
      - NODE_ENV=production
      - DATABASE_CLIENT=postgres
      - DATABASE_HOST=josetusabe-postgres-master
      - DATABASE_PORT=5432
      - DATABASE_NAME=strapi
      - DATABASE_USERNAME=master_user
      - DATABASE_PASSWORD=JoseTusabe_Master_2024!
      - DATABASE_SSL=false
      - JWT_SECRET=${STRAPI_JWT_SECRET}
      - ADMIN_JWT_SECRET=${STRAPI_ADMIN_JWT_SECRET}
      - APP_KEYS=${STRAPI_APP_KEYS}
      - API_TOKEN_SALT=${STRAPI_API_TOKEN_SALT}
      - TRANSFER_TOKEN_SALT=${STRAPI_TRANSFER_TOKEN_SALT}
      - REDIS_HOST=josetusabe-redis-master
      - REDIS_PORT=6379
      - REDIS_PASSWORD=Redis_Master_2024!
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - ZONOS_API_KEY=${ZONOS_API_KEY}
      - SMTP_HOST=smtp.dynu.com
      - SMTP_PORT=587
      - SMTP_USERNAME=${DYNU_EMAIL_USER}
      - SMTP_PASSWORD=${DYNU_EMAIL_PASS}
    volumes:
      - strapi_data:/opt/app
      - ./cms-configs/strapi/plugins:/opt/app/src/plugins/custom
      - ./cms-configs/strapi/api:/opt/app/src/api/custom
    networks:
      - cms-network
      - core-network
    depends_on:
      - postgres-cms
      - redis-cms
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.strapi.rule=Host(`strapi.soloylibre.com`)"
      - "traefik.http.routers.strapi.entrypoints=websecure"
      - "traefik.http.routers.strapi.tls=true"

  # ElevenLabs TTS Service
  elevenlabs-tts:
    image: node:18-alpine
    container_name: josetusabe-elevenlabs-tts
    ports:
      - "8105:3000"
    environment:
      - NODE_ENV=production
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - REDIS_HOST=josetusabe-redis-master
      - REDIS_PORT=6379
      - REDIS_PASSWORD=Redis_Master_2024!
      - POSTGRES_HOST=josetusabe-postgres-master
      - POSTGRES_PORT=5432
      - POSTGRES_USER=master_user
      - POSTGRES_PASSWORD=JoseTusabe_Master_2024!
      - POSTGRES_DB=elevenlabs_tts
    working_dir: /app
    command: >
      sh -c "
        npm install express axios redis pg multer &&
        node server.js
      "
    volumes:
      - ./cms-configs/elevenlabs:/app
      - elevenlabs_audio:/app/audio
    networks:
      - cms-network
      - core-network
    depends_on:
      - postgres-cms
      - redis-cms
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.elevenlabs.rule=Host(`tts.soloylibre.com`)"
      - "traefik.http.routers.elevenlabs.entrypoints=websecure"
      - "traefik.http.routers.elevenlabs.tls=true"

  # Zonos AI TTS Service
  zonos-ai-tts:
    image: python:3.11-alpine
    container_name: josetusabe-zonos-ai-tts
    ports:
      - "8106:5000"
    environment:
      - FLASK_ENV=production
      - ZONOS_API_KEY=${ZONOS_API_KEY}
      - REDIS_HOST=josetusabe-redis-master
      - REDIS_PORT=6379
      - REDIS_PASSWORD=Redis_Master_2024!
      - POSTGRES_HOST=josetusabe-postgres-master
      - POSTGRES_PORT=5432
      - POSTGRES_USER=master_user
      - POSTGRES_PASSWORD=JoseTusabe_Master_2024!
      - POSTGRES_DB=zonos_ai_tts
    working_dir: /app
    command: >
      sh -c "
        pip install flask requests redis psycopg2-binary python-dotenv &&
        python app.py
      "
    volumes:
      - ./cms-configs/zonos-ai:/app
      - zonos_audio:/app/audio
    networks:
      - cms-network
      - core-network
    depends_on:
      - postgres-cms
      - redis-cms
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.zonos-ai.rule=Host(`zonos-tts.soloylibre.com`)"
      - "traefik.http.routers.zonos-ai.entrypoints=websecure"
      - "traefik.http.routers.zonos-ai.tls=true"

  # CMS Integration Gateway
  cms-gateway:
    image: node:18-alpine
    container_name: josetusabe-cms-gateway
    ports:
      - "8107:4000"
    environment:
      - NODE_ENV=production
      - REDIS_HOST=josetusabe-redis-master
      - REDIS_PORT=6379
      - REDIS_PASSWORD=Redis_Master_2024!
      - POSTGRES_HOST=josetusabe-postgres-master
      - POSTGRES_PORT=5432
      - POSTGRES_USER=master_user
      - POSTGRES_PASSWORD=JoseTusabe_Master_2024!
      - POSTGRES_DB=cms_gateway
      - JWT_SECRET=${INTEGRATION_JWT_SECRET}
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - ZONOS_API_KEY=${ZONOS_API_KEY}
    working_dir: /app
    command: >
      sh -c "
        npm install express axios redis pg jsonwebtoken bcryptjs cors helmet &&
        node gateway.js
      "
    volumes:
      - ./cms-configs/gateway:/app
    networks:
      - cms-network
      - core-network
    depends_on:
      - postgres-cms
      - redis-cms
      - elevenlabs-tts
      - zonos-ai-tts
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cms-gateway.rule=Host(`cms-api.soloylibre.com`)"
      - "traefik.http.routers.cms-gateway.entrypoints=websecure"
      - "traefik.http.routers.cms-gateway.tls=true"

  # PostgreSQL for CMS
  postgres-cms:
    image: postgres:15-alpine
    container_name: josetusabe-postgres-cms
    ports:
      - "5434:5432"
    environment:
      - POSTGRES_DB=cms_master
      - POSTGRES_USER=cms_user
      - POSTGRES_PASSWORD=CMS_JoseTusabe_2024!
      - POSTGRES_MULTIPLE_DATABASES=wordpress_multisite,drupal,joomla,ghost,strapi,elevenlabs_tts,zonos_ai_tts,cms_gateway
    volumes:
      - postgres_cms_data:/var/lib/postgresql/data
      - ./cms-configs/database/init-cms.sql:/docker-entrypoint-initdb.d/init-cms.sql
    networks:
      - cms-network
      - core-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cms_user"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for CMS
  redis-cms:
    image: redis:7-alpine
    container_name: josetusabe-redis-cms
    ports:
      - "6381:6379"
    command: redis-server --appendonly yes --requirepass CMS_Redis_2024!
    volumes:
      - redis_cms_data:/data
    networks:
      - cms-network
      - core-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "CMS_Redis_2024!", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  wordpress_multisite_data:
  drupal_data:
  joomla_data:
  ghost_data:
  strapi_data:
  elevenlabs_audio:
  zonos_audio:
  postgres_cms_data:
  redis_cms_data:

networks:
  cms-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  core-network:
    external: true
    name: ultimate_dev_env_core-network
