#!/bin/bash

# Complete Environment Installation Script
# Professional 2000% IQ Setup with AI Services + Dynu Email Integration
# Jose L Encarnacion (JoseTusabe)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              COMPLETE ENVIRONMENT INSTALLER                 ║"
    echo "║         AI Services + Dynu Email + Microservices            ║"
    echo "║              Jose L Encarnacion (JoseTusabe)                 ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available"
        exit 1
    fi
    
    # Check if .env file exists
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from example..."
        cp .env.example .env
        print_warning "Please edit .env file with your actual values before continuing"
        read -p "Press Enter after editing .env file..."
    fi
    
    print_success "Prerequisites check completed"
}

# Create necessary directories
create_directories() {
    print_status "Creating directory structure..."
    
    # AI Services directories
    mkdir -p ./ai-services/{database,monitoring,models}
    mkdir -p ./ai-services/monitoring/{dashboards,datasources}
    mkdir -p ./ollama/models
    mkdir -p ./reduced/{models,cache}
    mkdir -p ./n8n/{workflows,credentials}
    
    # Email directories
    mkdir -p ./email/{templates,logs,config}
    mkdir -p ./email-worker/{templates,logs}
    
    # Project directories
    mkdir -p ./project1/{frontend,services,database,monitoring}
    mkdir -p ./project1/services/{core,ml,auth,notifications}
    mkdir -p ./project2/{frontend,backend,admin,analytics}
    
    # Monitoring directories
    mkdir -p ./monitoring/{prometheus,grafana,jaeger}
    mkdir -p ./monitoring/prometheus/{rules,targets}
    mkdir -p ./monitoring/grafana/{dashboards,datasources}
    
    # Storage directories
    mkdir -p ./storage/{minio,backup,logs}
    
    # Traefik directories
    mkdir -p ./traefik/{config,acme}
    
    # Database directories
    mkdir -p ./database/init
    
    print_success "Directory structure created"
}

# Install core infrastructure
install_core_infrastructure() {
    print_status "Installing core infrastructure..."
    
    # Start master services
    docker compose -f docker-compose.master.yml up -d
    
    # Wait for services to be ready
    print_status "Waiting for core services to be ready..."
    sleep 30
    
    # Check if services are running
    if docker ps | grep -q "master-postgres"; then
        print_success "PostgreSQL is running"
    else
        print_error "PostgreSQL failed to start"
        exit 1
    fi
    
    if docker ps | grep -q "master-redis"; then
        print_success "Redis is running"
    else
        print_error "Redis failed to start"
        exit 1
    fi
    
    print_success "Core infrastructure installed"
}

# Install AI services
install_ai_services() {
    print_status "Installing AI services (Ollama, LlamaGPT, Reduced, n8n)..."
    
    # Start AI services
    docker compose -f docker-compose.ai-services.yml up -d
    
    # Wait for Ollama to be ready
    print_status "Waiting for Ollama to be ready..."
    sleep 60
    
    # Download initial models
    print_status "Downloading AI models..."
    docker exec josetusabe-ollama ollama pull llama2:7b
    docker exec josetusabe-ollama ollama pull codellama:7b
    docker exec josetusabe-ollama ollama pull mistral:7b
    
    print_success "AI services installed"
}

# Install Project1 (Microservices)
install_project1() {
    print_status "Installing Project1 (Microservices with FastAPI + ML)..."
    
    # Start Project1 services
    docker compose -f docker-compose.project1.yml up -d
    
    # Wait for services
    sleep 30
    
    print_success "Project1 microservices installed"
}

# Install Project2 (Independent Module)
install_project2() {
    print_status "Installing Project2 (Independent Module)..."
    
    # Start Project2 services
    docker compose -f docker-compose.project2.yml up -d
    
    # Wait for services
    sleep 30
    
    print_success "Project2 module installed"
}

# Configure email integration
configure_email_integration() {
    print_status "Configuring Dynu email integration..."
    
    # Apply email configuration
    docker compose -f dynu-email-config.yml up -d
    
    # Test email configuration
    print_status "Testing email configuration..."
    
    # Send test email through n8n
    curl -X POST http://localhost:5678/webhook-test/email \
        -H "Content-Type: application/json" \
        -d '{"to":"<EMAIL>","subject":"Test Email","body":"Email integration test"}' \
        || print_warning "Email test failed - check configuration"
    
    print_success "Email integration configured"
}

# Setup monitoring and alerting
setup_monitoring() {
    print_status "Setting up monitoring and alerting..."
    
    # Create Grafana dashboards
    cat > ./monitoring/grafana/dashboards/ai-services.json << 'EOF'
{
  "dashboard": {
    "title": "AI Services Dashboard",
    "panels": [
      {
        "title": "Ollama Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(ollama_request_duration_seconds_bucket[5m]))"
          }
        ]
      },
      {
        "title": "Model Usage",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(ollama_requests_total[5m])"
          }
        ]
      }
    ]
  }
}
EOF
    
    # Create Prometheus rules
    cat > ./monitoring/prometheus/rules/ai-services.yml << 'EOF'
groups:
  - name: ai-services
    rules:
      - alert: OllamaDown
        expr: up{job="ollama"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Ollama service is down"
      
      - alert: HighModelLatency
        expr: histogram_quantile(0.95, rate(ollama_request_duration_seconds_bucket[5m])) > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High model inference latency"
EOF
    
    print_success "Monitoring configured"
}

# Create service discovery
create_service_discovery() {
    print_status "Creating service discovery configuration..."
    
    # Create Traefik dynamic configuration
    cat > ./traefik/config/dynamic.yml << 'EOF'
http:
  routers:
    ai-services:
      rule: "Host(`ai.soloylibre.com`)"
      service: llamagpt
      tls:
        certResolver: letsencrypt
    
    n8n-workflows:
      rule: "Host(`workflows.soloylibre.com`)"
      service: n8n
      tls:
        certResolver: letsencrypt
    
    project1-api:
      rule: "Host(`api.soloylibre.com`) && PathPrefix(`/v1`)"
      service: project1-gateway
      tls:
        certResolver: letsencrypt

  services:
    llamagpt:
      loadBalancer:
        servers:
          - url: "http://josetusabe-llamagpt:3000"
    
    n8n:
      loadBalancer:
        servers:
          - url: "http://josetusabe-n8n:5678"
    
    project1-gateway:
      loadBalancer:
        servers:
          - url: "http://project1-api-gateway:8000"
EOF
    
    print_success "Service discovery configured"
}

# Run health checks
run_health_checks() {
    print_status "Running health checks..."
    
    # Check all services
    services=(
        "master-postgres:5432"
        "master-redis:6379"
        "josetusabe-ollama:11434"
        "josetusabe-llamagpt:3000"
        "josetusabe-n8n:5678"
        "project1-api-gateway:8000"
        "project2-api:8000"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r container port <<< "$service"
        if docker exec "$container" nc -z localhost "$port" 2>/dev/null; then
            print_success "$container is healthy"
        else
            print_warning "$container health check failed"
        fi
    done
}

# Display access information
display_access_info() {
    print_success "Installation completed! Here are your access URLs:"
    echo ""
    echo -e "${GREEN}🚀 MAIN APPLICATIONS${NC}"
    echo "   • Main Site: https://soloylibre.com"
    echo "   • API Gateway: https://api.soloylibre.com"
    echo "   • Project1 Frontend: http://localhost:8800"
    echo "   • Project2 Frontend: http://localhost:8850"
    echo ""
    echo -e "${BLUE}🤖 AI SERVICES${NC}"
    echo "   • LlamaGPT: http://localhost:3001"
    echo "   • Ollama API: http://localhost:11434"
    echo "   • AI Manager: http://localhost:8951"
    echo "   • Reduced: http://localhost:8950"
    echo ""
    echo -e "${PURPLE}🔧 AUTOMATION & WORKFLOWS${NC}"
    echo "   • n8n Workflows: http://localhost:5678"
    echo "   • Email Monitor: http://localhost:8025"
    echo ""
    echo -e "${CYAN}📊 MONITORING${NC}"
    echo "   • Grafana: http://localhost:3000"
    echo "   • Prometheus: http://localhost:9090"
    echo "   • Jaeger: http://localhost:16686"
    echo "   • Portainer: http://localhost:9000"
    echo ""
    echo -e "${YELLOW}💾 STORAGE & DATA${NC}"
    echo "   • MinIO Console: http://localhost:9001"
    echo "   • MLflow: http://localhost:8830"
    echo ""
    echo -e "${RED}🔐 DEFAULT CREDENTIALS${NC}"
    echo "   • Grafana: josetusabe / josetusabe123"
    echo "   • n8n: josetusabe / josetusabe123"
    echo "   • MinIO: josetusabe / josetusabe123"
    echo "   • Portainer: admin / (set on first login)"
    echo ""
    echo -e "${GREEN}📧 EMAIL CONFIGURATION${NC}"
    echo "   • SMTP: smtp.dynu.com:587"
    echo "   • Email Relay: localhost:1587"
    echo "   • Test Interface: http://localhost:8025"
    echo ""
    echo -e "${BLUE}🎯 NEXT STEPS${NC}"
    echo "   1. Configure DNS records for your domains"
    echo "   2. Update .env file with your actual credentials"
    echo "   3. Test email functionality"
    echo "   4. Deploy your applications"
    echo "   5. Configure monitoring alerts"
    echo ""
}

# Main installation function
main() {
    print_header
    
    check_prerequisites
    create_directories
    install_core_infrastructure
    install_ai_services
    install_project1
    install_project2
    configure_email_integration
    setup_monitoring
    create_service_discovery
    run_health_checks
    display_access_info
    
    print_success "🎉 Complete environment installation finished!"
}

# Run main function
main "$@"
