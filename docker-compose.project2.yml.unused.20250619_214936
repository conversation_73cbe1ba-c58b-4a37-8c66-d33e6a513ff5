version: '3.8'

services:
  # Project2 Main Frontend
  project2-frontend:
    build:
      context: ./project2/frontend
      dockerfile: Dockerfile
    container_name: project2-frontend
    ports:
      - "8850:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8860
      - REACT_APP_ENV=development
    volumes:
      - ./project2/frontend:/app
      - /app/node_modules
    networks:
      - project2-network
    depends_on:
      - project2-api

  # Admin Dashboard
  project2-admin:
    build:
      context: ./project2/admin
      dockerfile: Dockerfile
    container_name: project2-admin
    ports:
      - "8851:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8860
      - REACT_APP_ADMIN_MODE=true
    volumes:
      - ./project2/admin:/app
      - /app/node_modules
    networks:
      - project2-network
    depends_on:
      - project2-api

  # Analytics Dashboard
  project2-analytics:
    build:
      context: ./project2/analytics
      dockerfile: Dockerfile
    container_name: project2-analytics
    ports:
      - "8852:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8860
      - REACT_APP_ANALYTICS_MODE=true
    volumes:
      - ./project2/analytics:/app
      - /app/node_modules
    networks:
      - project2-network
    depends_on:
      - project2-api

  # Main API Server
  project2-api:
    build:
      context: ./project2/backend
      dockerfile: Dockerfile
    container_name: project2-api
    ports:
      - "8860:8000"
    environment:
      - DATABASE_URL=**************************************************/project2
      - REDIS_URL=redis://project2-redis:6379
      - JWT_SECRET=your-super-secret-jwt-key
      - CORS_ORIGINS=http://localhost:8850,http://localhost:8851,http://localhost:8852
    volumes:
      - ./project2/backend:/app
      - ./project2/uploads:/app/uploads
    networks:
      - project2-network
    depends_on:
      - project2-db
      - project2-redis

  # Background Jobs Worker
  project2-worker:
    build:
      context: ./project2/backend
      dockerfile: Dockerfile.worker
    container_name: project2-worker
    environment:
      - DATABASE_URL=**************************************************/project2
      - REDIS_URL=redis://project2-redis:6379
      - WORKER_MODE=true
    volumes:
      - ./project2/backend:/app
      - ./project2/uploads:/app/uploads
    networks:
      - project2-network
    depends_on:
      - project2-db
      - project2-redis

  # File Processing Service
  project2-processor:
    build:
      context: ./project2/processor
      dockerfile: Dockerfile
    container_name: project2-processor
    ports:
      - "8862:8000"
    environment:
      - DATABASE_URL=**************************************************/project2
      - REDIS_URL=redis://project2-redis:6379
      - STORAGE_PATH=/app/storage
    volumes:
      - ./project2/processor:/app
      - ./project2/storage:/app/storage
    networks:
      - project2-network
    depends_on:
      - project2-db
      - project2-redis

  # Notification Service
  project2-notifications:
    build:
      context: ./project2/notifications
      dockerfile: Dockerfile
    container_name: project2-notifications
    ports:
      - "8863:8000"
    environment:
      - DATABASE_URL=**************************************************/project2
      - REDIS_URL=redis://project2-redis:6379
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
    volumes:
      - ./project2/notifications:/app
    networks:
      - project2-network
    depends_on:
      - project2-db
      - project2-redis

  # PostgreSQL Database
  project2-db:
    image: postgres:15-alpine
    container_name: project2-database
    ports:
      - "8870:5432"
    environment:
      - POSTGRES_DB=project2
      - POSTGRES_USER=project2
      - POSTGRES_PASSWORD=project2123
    volumes:
      - project2_postgres_data:/var/lib/postgresql/data
      - ./project2/database/init:/docker-entrypoint-initdb.d
    networks:
      - project2-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U project2"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  project2-redis:
    image: redis:7-alpine
    container_name: project2-redis
    ports:
      - "8871:6379"
    command: redis-server --appendonly yes
    volumes:
      - project2_redis_data:/data
    networks:
      - project2-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # File Storage API (MinIO)
  project2-storage:
    image: minio/minio:latest
    container_name: project2-storage
    ports:
      - "8872:9000"
      - "8873:9001"
    environment:
      - MINIO_ROOT_USER=project2
      - MINIO_ROOT_PASSWORD=project2123
    command: server /data --console-address ":9001"
    volumes:
      - project2_minio_data:/data
    networks:
      - project2-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Nginx Reverse Proxy
  project2-nginx:
    image: nginx:alpine
    container_name: project2-nginx
    ports:
      - "8880:80"
    volumes:
      - ./project2/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./project2/nginx/conf.d:/etc/nginx/conf.d
    networks:
      - project2-network
    depends_on:
      - project2-frontend
      - project2-admin
      - project2-analytics
      - project2-api

  # Project2 Monitoring (Prometheus)
  project2-prometheus:
    image: prom/prometheus:latest
    container_name: project2-prometheus
    ports:
      - "8874:9090"
    volumes:
      - ./project2/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - project2_prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - project2-network

  # Project2 Grafana
  project2-grafana:
    image: grafana/grafana:latest
    container_name: project2-grafana
    ports:
      - "8875:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=http://localhost:8875
    volumes:
      - project2_grafana_data:/var/lib/grafana
      - ./project2/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./project2/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - project2-network
    depends_on:
      - project2-prometheus

volumes:
  project2_postgres_data:
  project2_redis_data:
  project2_minio_data:
  project2_prometheus_data:
  project2_grafana_data:

networks:
  project2-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
