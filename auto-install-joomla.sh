#!/bin/bash

# Auto-install Joomla for SoloYlibre & JEYKO
# Head Developer: <PERSON> L Encarnacion

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[JOOMLA INSTALL]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Starting automated Joomla installation for SoloYlibre & JEYKO..."

# Wait for services to be ready
sleep 5

# Install Joom<PERSON> via automated POST request
print_status "Installing Joomla with SoloYlibre configuration..."

# Step 1: Language selection and site configuration
curl -X POST http://localhost:8102/installation/index.php \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "task=installation.setlanguage" \
    -d "lang=en-GB" \
    -c joomla_cookies.txt \
    || print_error "Language selection failed"

# Step 2: Site configuration
curl -X POST http://localhost:8102/installation/index.php \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -b joomla_cookies.txt \
    -c joomla_cookies.txt \
    -d "task=installation.setup" \
    -d "jform[site_name]=SoloYlibre Joomla Platform" \
    -d "jform[site_metadesc]=SoloYlibre business platform powered by JEYKO AI division" \
    -d "jform[admin_email]=<EMAIL>" \
    -d "jform[admin_user]=Jose L Encarnacion" \
    -d "jform[admin_username]=SoloYlibre" \
    -d "jform[admin_password]=57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd" \
    -d "jform[admin_password2]=57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd" \
    || print_error "Site configuration failed"

# Step 3: Database configuration
curl -X POST http://localhost:8102/installation/index.php \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -b joomla_cookies.txt \
    -c joomla_cookies.txt \
    -d "task=installation.database" \
    -d "jform[db_type]=mysqli" \
    -d "jform[db_host]=soloylibre-mysql-joomla" \
    -d "jform[db_user]=joomla_user" \
    -d "jform[db_pass]=SoloYlibre_Joomla_2024!" \
    -d "jform[db_name]=joomla_db" \
    -d "jform[db_prefix]=sol_" \
    -d "jform[db_old]=backup" \
    || print_error "Database configuration failed"

# Step 4: Finalize installation
curl -X POST http://localhost:8102/installation/index.php \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -b joomla_cookies.txt \
    -c joomla_cookies.txt \
    -d "task=installation.install" \
    || print_error "Installation finalization failed"

print_success "Joomla installation completed"

# Remove installation directory
print_status "Removing installation directory..."
docker exec josetusabe-joomla rm -rf /var/www/html/installation || print_error "Could not remove installation directory"

print_success "Installation directory removed"

# Clean up cookies
rm -f joomla_cookies.txt

# Test the installation
print_status "Testing Joomla installation..."
sleep 5

if curl -s http://localhost:8102 | grep -q "SoloYlibre"; then
    print_success "Joomla is working with SoloYlibre branding"
else
    print_error "Joomla installation may need manual verification"
fi

echo ""
echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║                 JOOMLA INSTALLATION COMPLETE                ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║  🌐 Frontend: http://localhost:8102                        ║${NC}"
echo -e "${GREEN}║  🔧 Admin: http://localhost:8102/administrator             ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║  🔐 LOGIN CREDENTIALS:                                     ║${NC}"
echo -e "${GREEN}║  Username: SoloYlibre                                       ║${NC}"
echo -e "${GREEN}║  Password: 57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd              ║${NC}"
echo -e "${GREEN}║  Email: <EMAIL>                                ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║  🏢 Company: SoloYlibre                                    ║${NC}"
echo -e "${GREEN}║  🤖 AI Division: JEYKO                                     ║${NC}"
echo -e "${GREEN}║  👨‍💻 Head Developer: Jose L Encarnacion                    ║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
