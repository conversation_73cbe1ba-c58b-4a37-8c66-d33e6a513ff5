# Environment Variables for JoseTusabe Ultimate Dev Environment
# Professional 2000% IQ Configuration
# Copy this file to .env and update with your actual values

# ================================
# DYNU EMAIL CONFIGURATION
# ================================
DYNU_EMAIL_USER=<EMAIL>
DYNU_EMAIL_PASS=your-dynu-password
DYNU_EMAIL_FROM=<EMAIL>
DYNU_SMTP_HOST=smtp.dynu.com
DYNU_SMTP_PORT=587
DYNU_IMAP_HOST=imap.dynu.com
DYNU_IMAP_PORT=993

# ================================
# DOMAIN CONFIGURATION
# ================================
PRIMARY_DOMAIN=soloylibre.com
DOMAIN_1=josetusabe.com
DOMAIN_2=1and1photo.com
DOMAIN_3=joselencarnacion.com

# ================================
# DATABASE CONFIGURATION
# ================================
# Master Database
POSTGRES_MASTER_USER=master_user
POSTGRES_MASTER_PASS=master_pass_123
POSTGRES_MASTER_DB=master_db

# Project1 Database
PROJECT1_DB_USER=project1
PROJECT1_DB_PASS=project1123
PROJECT1_DB_NAME=project1

# Project2 Database
PROJECT2_DB_USER=project2
PROJECT2_DB_PASS=project2123
PROJECT2_DB_NAME=project2

# AI Services Database
AI_DB_USER=ai_user
AI_DB_PASS=ai_pass_123
AI_DB_NAME=ai_services

# ================================
# REDIS CONFIGURATION
# ================================
REDIS_MASTER_PASS=redis_master_123
REDIS_AI_PASS=redis_ai_123

# ================================
# STORAGE CONFIGURATION
# ================================
# MinIO Configuration
MINIO_ROOT_USER=josetusabe
MINIO_ROOT_PASSWORD=josetusabe123
MINIO_BUCKET_PROJECT1=project1-storage
MINIO_BUCKET_PROJECT2=project2-storage
MINIO_BUCKET_AI=ai-models

# ================================
# AI SERVICES CONFIGURATION
# ================================
# Ollama Configuration
OLLAMA_HOST=0.0.0.0
OLLAMA_ORIGINS=*
OLLAMA_MODELS=llama2:7b,codellama:7b,mistral:7b

# Hugging Face Token (for model downloads)
HUGGINGFACE_TOKEN=your-huggingface-token

# OpenAI API (if needed)
OPENAI_API_KEY=your-openai-api-key

# ================================
# N8N CONFIGURATION
# ================================
N8N_BASIC_AUTH_USER=josetusabe
N8N_BASIC_AUTH_PASSWORD=josetusabe123
N8N_ENCRYPTION_KEY=your-n8n-encryption-key
N8N_USER_MANAGEMENT_JWT_SECRET=your-jwt-secret

# ================================
# MONITORING CONFIGURATION
# ================================
# Grafana
GRAFANA_ADMIN_USER=josetusabe
GRAFANA_ADMIN_PASSWORD=josetusabe123

# Prometheus
PROMETHEUS_RETENTION_TIME=30d
PROMETHEUS_STORAGE_SIZE=50Gi

# ================================
# SECURITY CONFIGURATION
# ================================
# JWT Secrets
JWT_SECRET_PROJECT1=your-project1-jwt-secret
JWT_SECRET_PROJECT2=your-project2-jwt-secret

# API Keys
API_KEY_PROJECT1=your-project1-api-key
API_KEY_PROJECT2=your-project2-api-key

# SSL Configuration
SSL_EMAIL=<EMAIL>
ACME_EMAIL=<EMAIL>

# ================================
# BACKUP CONFIGURATION
# ================================
# Restic Backup
RESTIC_REPOSITORY=/backup/restic
RESTIC_PASSWORD=backup_password_123
BACKUP_SCHEDULE=0 2 * * *

# Cloud Backup (optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_BUCKET_NAME=josetusabe-backups
AWS_REGION=us-east-1

# ================================
# SYNOLOGY CONFIGURATION
# ================================
SYNOLOGY_IP=*************
SYNOLOGY_USER=admin
SYNOLOGY_PASS=your-synology-password

# ================================
# DEVELOPMENT CONFIGURATION
# ================================
# Environment
NODE_ENV=production
PYTHON_ENV=production
DEBUG=false

# Hot Reload (development only)
CHOKIDAR_USEPOLLING=true
WATCHPACK_POLLING=true

# ================================
# EXTERNAL SERVICES
# ================================
# Pinecone (for vector database)
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=us-east1-gcp

# MLflow
MLFLOW_TRACKING_URI=http://mlflow:5000
MLFLOW_S3_ENDPOINT_URL=http://minio:9000

# ================================
# NOTIFICATION CONFIGURATION
# ================================
# Slack (optional)
SLACK_WEBHOOK_URL=your-slack-webhook-url
SLACK_CHANNEL=#alerts

# Discord (optional)
DISCORD_WEBHOOK_URL=your-discord-webhook-url

# Telegram (optional)
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_CHAT_ID=your-telegram-chat-id

# ================================
# PERFORMANCE CONFIGURATION
# ================================
# Resource Limits
MAX_MEMORY_PROJECT1=2Gi
MAX_CPU_PROJECT1=1000m
MAX_MEMORY_PROJECT2=2Gi
MAX_CPU_PROJECT2=1000m

# Scaling Configuration
MIN_REPLICAS=2
MAX_REPLICAS=10
TARGET_CPU_UTILIZATION=70

# ================================
# LOGGING CONFIGURATION
# ================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_RETENTION_DAYS=30

# Elasticsearch
ELASTIC_PASSWORD=elastic_password_123
KIBANA_PASSWORD=kibana_password_123

# ================================
# CUSTOM CONFIGURATION
# ================================
# Your custom environment variables here
CUSTOM_VAR_1=value1
CUSTOM_VAR_2=value2
