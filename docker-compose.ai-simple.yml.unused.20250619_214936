version: '3.8'

services:
  # Simple AI Chat Interface for SoloYlibre & JEYKO
  ai-chat:
    image: node:18-alpine
    container_name: soloylibre-ai-chat
    ports:
      - "3002:3000"
    working_dir: /app
    command: >
      sh -c "
        npm install express axios cors &&
        cat > server.js << 'EOF'
        const express = require('express');
        const cors = require('cors');
        const app = express();
        
        app.use(cors());
        app.use(express.json());
        
        const COMPANY_CONFIG = {
          name: 'SoloYlibre',
          aiDivision: 'JEY<PERSON>',
          headDeveloper: '<PERSON>',
          email: '<EMAIL>'
        };
        
        app.get('/', (req, res) => {
          res.json({
            message: 'Welcome to SoloYlibre & JEYKO AI Chat',
            company: COMPANY_CONFIG.name,
            aiDivision: COMPANY_CONFIG.aiDivision,
            headDeveloper: COMPANY_CONFIG.headDeveloper,
            status: 'operational',
            services: ['AI Chat', 'Content Processing', 'Voice Integration']
          });
        });
        
        app.post('/api/chat', (req, res) => {
          const { message } = req.body;
          res.json({
            response: 'Hello from ' + COMPANY_CONFIG.name + ' and ' + COMPANY_CONFIG.aiDivision + ' AI division. How can we help you today?',
            company: COMPANY_CONFIG.name,
            aiDivision: COMPANY_CONFIG.aiDivision,
            timestamp: new Date().toISOString()
          });
        });
        
        app.listen(3000, '0.0.0.0', () => {
          console.log('SoloYlibre & JEYKO AI Chat ready on port 3000');
        });
        EOF
        node server.js
      "
    environment:
      - COMPANY_NAME=SoloYlibre
      - AI_DIVISION=JEYKO
    networks:
      - ai-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ai-chat.rule=Host(`ai.soloylibre.com`) || Host(`ai.localhost`)"

  # Document Management - Docmost
  docmost:
    image: docmost/docmost:latest
    container_name: soloylibre-docmost
    ports:
      - "3003:3000"
    environment:
      - APP_URL=http://docs.soloylibre.com
      - DATABASE_URL=***********************************************************************/docmost
      - REDIS_URL=redis://soloylibre-redis-cms:6379
      - APP_SECRET=SoloYlibre_Docmost_Secret_2024_JEYKO
      - COMPANY_NAME=SoloYlibre
      - AI_DIVISION=JEYKO
    networks:
      - cms-network
      - ai-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.docmost.rule=Host(`docs.soloylibre.com`) || Host(`docs.localhost`)"

  # NocoDB - Database Interface
  nocodb:
    image: nocodb/nocodb:latest
    container_name: soloylibre-nocodb
    ports:
      - "8080:8080"
    environment:
      - NC_DB=pg://soloylibre-postgres-cms:5432?u=cms_user&p=CMS_JoseTusabe_2024!&d=nocodb
      - NC_AUTH_JWT_SECRET=SoloYlibre_NocoDB_JWT_2024_JEYKO
      - NC_PUBLIC_URL=http://db.soloylibre.com
      - NC_ADMIN_EMAIL=<EMAIL>
      - NC_ADMIN_PASSWORD=57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd
    volumes:
      - nocodb_data:/usr/app/data
    networks:
      - cms-network
      - ai-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.nocodb.rule=Host(`db.soloylibre.com`) || Host(`db.localhost`)"

  # Themer - Design System
  themer:
    image: node:18-alpine
    container_name: soloylibre-themer
    ports:
      - "3004:3000"
    working_dir: /app
    command: >
      sh -c "
        npm install express multer sharp &&
        cat > server.js << 'EOF'
        const express = require('express');
        const app = express();
        
        app.use(express.json());
        app.use(express.static('public'));
        
        const themes = {
          soloylibre: {
            primary: '#2196F3',
            secondary: '#21CBF3',
            name: 'SoloYlibre'
          },
          jeyko: {
            primary: '#FF6B35',
            secondary: '#F7931E',
            name: 'JEYKO'
          }
        };
        
        app.get('/', (req, res) => {
          res.json({
            message: 'SoloYlibre & JEYKO Theme Manager',
            themes: themes,
            company: 'SoloYlibre',
            aiDivision: 'JEYKO'
          });
        });
        
        app.get('/api/themes', (req, res) => {
          res.json(themes);
        });
        
        app.listen(3000, '0.0.0.0', () => {
          console.log('SoloYlibre Themer ready on port 3000');
        });
        EOF
        node server.js
      "
    networks:
      - ai-network
    restart: unless-stopped

volumes:
  nocodb_data:

networks:
  ai-network:
    driver: bridge
  cms-network:
    external: true
    name: ultimate_dev_env_cms-network
