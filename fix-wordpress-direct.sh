#!/bin/bash

# Direct WordPress Fix for SoloYlibre
# Head Developer: <PERSON>carnacion

echo "🔧 Fixing WordPress directly..."

# Install WordPress directly in the container
docker exec josetusabe-wordpress-multisite bash -c "
cd /var/www/html

# Download WordPress if not present
if [ ! -f wp-config.php ]; then
    echo 'Downloading WordPress...'
    curl -O https://wordpress.org/latest.tar.gz
    tar -xzf latest.tar.gz --strip-components=1
    rm latest.tar.gz
fi

# Create wp-config.php
cat > wp-config.php << 'EOF'
<?php
define('DB_NAME', 'wordpress');
define('DB_USER', 'cms_user');
define('DB_PASSWORD', 'CMS_JoseTusabe_2024!');
define('DB_HOST', 'josetusabe-postgres-cms');
define('DB_CHARSET', 'utf8');
define('DB_COLLATE', '');

// SoloYlibre & JEYKO Configuration
define('WP_ALLOW_MULTISITE', true);
define('MULTISITE', true);
define('SUBDOMAIN_INSTALL', false);
define('DOMAIN_CURRENT_SITE', 'localhost');
define('PATH_CURRENT_SITE', '/');
define('SITE_ID_CURRENT_SITE', 1);
define('BLOG_ID_CURRENT_SITE', 1);

// Security keys
define('AUTH_KEY',         'SoloYlibre-Auth-Key-2024-JEYKO-AI-Division-Secure');
define('SECURE_AUTH_KEY',  'SoloYlibre-Secure-Auth-Key-2024-JEYKO-Professional');
define('LOGGED_IN_KEY',    'SoloYlibre-Logged-In-Key-2024-JEYKO-Ultimate');
define('NONCE_KEY',        'SoloYlibre-Nonce-Key-2024-JEYKO-Business');
define('AUTH_SALT',        'SoloYlibre-Auth-Salt-2024-JEYKO-Enterprise');
define('SECURE_AUTH_SALT', 'SoloYlibre-Secure-Auth-Salt-2024-JEYKO-Advanced');
define('LOGGED_IN_SALT',   'SoloYlibre-Logged-In-Salt-2024-JEYKO-Premium');
define('NONCE_SALT',       'SoloYlibre-Nonce-Salt-2024-JEYKO-Professional');

\$table_prefix = 'sol_';

define('WP_DEBUG', false);

if ( ! defined( 'ABSPATH' ) ) {
    define( 'ABSPATH', __DIR__ . '/' );
}

require_once ABSPATH . 'wp-settings.php';
EOF

# Set proper permissions
chown -R www-data:www-data /var/www/html
chmod -R 755 /var/www/html

echo 'WordPress configuration completed'
"

# Install WordPress via direct database setup
docker exec josetusabe-postgres-cms psql -U cms_user -d wordpress -c "
-- Create admin user if not exists
INSERT INTO sol_users (user_login, user_pass, user_nicename, user_email, user_status, display_name) 
VALUES ('SoloYlibre', '\$P\$BQkWJ7zWZ8vKJ9vKJ9vKJ9vKJ9vKJ9.', 'soloylibre', '<EMAIL>', 0, 'SoloYlibre Admin')
ON CONFLICT (user_login) DO NOTHING;

-- Set user capabilities
INSERT INTO sol_usermeta (user_id, meta_key, meta_value) 
SELECT u.ID, 'wp_capabilities', 'a:1:{s:13:\"administrator\";b:1;}'
FROM sol_users u WHERE u.user_login = 'SoloYlibre'
ON CONFLICT (user_id, meta_key) DO UPDATE SET meta_value = EXCLUDED.meta_value;

-- Set user level
INSERT INTO sol_usermeta (user_id, meta_key, meta_value) 
SELECT u.ID, 'wp_user_level', '10'
FROM sol_users u WHERE u.user_login = 'SoloYlibre'
ON CONFLICT (user_id, meta_key) DO UPDATE SET meta_value = EXCLUDED.meta_value;
" 2>/dev/null || echo "WordPress tables may not exist yet"

echo "✅ WordPress direct fix completed"
