{"name": "SoloYlibre & JEYKO Content Sync Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "content-sync", "responseMode": "responseNode"}, "name": "Webhook - Content Sync", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "soloylibre-content-sync"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"cms_source\"]}}", "operation": "equal", "value2": "wordpress"}]}}, "name": "Check Source CMS", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "http://soloylibre-drupal:80/api/content", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Company", "value": "SoloYlibre"}, {"name": "X-AI-Division", "value": "JEYKO"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "title", "value": "={{$json[\"title\"]}}"}, {"name": "content", "value": "={{$json[\"content\"]}}"}, {"name": "source", "value": "SoloYlibre WordPress"}, {"name": "ai_division", "value": "JEYKO"}]}}, "name": "Sync to <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [680, 200]}, {"parameters": {"url": "http://soloylibre-joomla:80/api/content", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Company", "value": "SoloYlibre"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "title", "value": "={{$json[\"title\"]}}"}, {"name": "content", "value": "={{$json[\"content\"]}}"}, {"name": "source", "value": "SoloYlibre WordPress"}]}}, "name": "Sync to <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"url": "http://soloylibre-strapi:1337/api/contents", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Company", "value": "SoloYlibre"}, {"name": "X-AI-Division", "value": "JEYKO"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "data", "value": "={{JSON.stringify({title: $json[\"title\"], content: $json[\"content\"], source: \"SoloYlibre WordPress\", ai_division: \"JEYKO\"})}}"}]}}, "name": "Sync to <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"url": "http://soloylibre-elevenlabs-tts:3000/api/tts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{$json[\"content\"]}}"}, {"name": "voice_id", "value": "EXAVITQu4vr4xnSDxMaL"}, {"name": "cms_source", "value": "n8n-sync"}, {"name": "user_id", "value": "SoloYlibre"}, {"name": "company", "value": "SoloYlibre"}, {"name": "ai_division", "value": "JEYKO"}]}}, "name": "Generate TTS - ElevenLabs", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"url": "http://soloylibre-zonos-ai-tts:5000/api/tts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{$json[\"content\"]}}"}, {"name": "language", "value": "es"}, {"name": "voice", "value": "maria"}, {"name": "cms_source", "value": "n8n-sync"}, {"name": "user_id", "value": "SoloYlibre"}, {"name": "company", "value": "SoloYlibre"}]}}, "name": "Generate TTS - Zonos Spanish", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{JSON.stringify({\n  \"status\": \"success\",\n  \"message\": \"Content synced across SoloYlibre & JEYKO platforms\",\n  \"company\": \"SoloYlibre\",\n  \"ai_division\": \"JEYKO\",\n  \"synced_to\": [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>rap<PERSON>\"],\n  \"tts_generated\": [\"ElevenLabs English\", \"Zonos Spanish\"],\n  \"timestamp\": new Date().toISOString()\n})}}"}, "name": "Response - Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 300]}], "connections": {"Webhook - Content Sync": {"main": [[{"node": "Check Source CMS", "type": "main", "index": 0}]]}, "Check Source CMS": {"main": [[{"node": "Sync to <PERSON><PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Sync to <PERSON><PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Sync to <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Sync to Drupal": {"main": [[{"node": "Generate TTS - ElevenLabs", "type": "main", "index": 0}]]}, "Sync to Joomla": {"main": [[{"node": "Generate TTS - Zonos Spanish", "type": "main", "index": 0}]]}, "Generate TTS - ElevenLabs": {"main": [[{"node": "Response - Success", "type": "main", "index": 0}]]}, "Generate TTS - Zonos Spanish": {"main": [[{"node": "Response - Success", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1", "meta": {"instanceId": "soloylibre-jeyko-n8n"}, "id": "1", "tags": [{"name": "SoloYlibre", "id": "1"}, {"name": "JEYKO", "id": "2"}, {"name": "Content Sync", "id": "3"}]}