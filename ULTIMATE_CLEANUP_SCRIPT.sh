#!/bin/bash

# ULTIMATE CLEANUP SCRIPT - SoloYlibre & JEYKO
# Clean all unused ports, containers, and non-working Docker configurations
# Keep only actively working services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${RED}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              🧹 ULTIMATE CLEANUP SCRIPT 🧹                 ║"
    echo "║              SOLOYLIBRE & JEYKO                             ║"
    echo "║                                                              ║"
    echo "║  ⚠️  CLEANING ALL UNUSED PORTS & DOCKER CONFIGS ⚠️         ║"
    echo "║  👨‍💻 Head Developer: Jose L Encarnacion                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${CYAN}[CLEANUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Define currently working services and their ports
WORKING_SERVICES=(
    "portainer:9000,9443"
    "soloylibre_ultimate_grafana_josetusabe:3000"
    "soloylibre_grafana_josetusabe:3001"
    "soloylibre_wordpress_josetusabe:1052"
    "soloylibre_ultimate_phpmyadmin_josetusabe:2051"
    "josetusabe-pgadmin:5050"
    "soloylibre_database_josetusabe:5433"
    "josetusabe-postgres-master:5432"
    "soloylibre_cache_josetusabe:6380"
    "soloylibre_ultimate_redis_josetusabe:6379"
    "soloylibre_ultimate_wordpress_db_josetusabe:3306"
)

WORKING_PORTS=(9000 9443 3000 3001 1052 2051 5050 5433 5432 6380 6379 3306)

# Show current status
show_current_status() {
    print_status "Current running containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""
    
    print_status "Current port usage:"
    lsof -i -P | grep LISTEN | grep -E ":(3000|3001|1052|2051|5050|5433|5432|6380|6379|9000|9443)" | sort -n || true
    echo ""
}

# Kill processes on unused ports
cleanup_unused_ports() {
    print_status "Cleaning up unused ports..."
    
    # Get all listening ports
    ALL_PORTS=$(lsof -i -P | grep LISTEN | awk '{print $9}' | cut -d':' -f2 | sort -n | uniq)
    
    for port in $ALL_PORTS; do
        # Check if port is in working ports list
        if [[ ! " ${WORKING_PORTS[@]} " =~ " ${port} " ]]; then
            print_warning "Found unused port: $port"
            
            # Get process using this port
            PID=$(lsof -ti:$port 2>/dev/null || true)
            if [ ! -z "$PID" ]; then
                PROCESS_NAME=$(ps -p $PID -o comm= 2>/dev/null || echo "unknown")
                print_warning "Killing process $PROCESS_NAME (PID: $PID) on port $port"
                kill -9 $PID 2>/dev/null || true
                print_success "Killed process on port $port"
            fi
        else
            print_success "Port $port is in use by working service - keeping"
        fi
    done
}

# Stop and remove unused containers
cleanup_unused_containers() {
    print_status "Cleaning up unused containers..."
    
    # Get all container names
    ALL_CONTAINERS=$(docker ps -a --format "{{.Names}}")
    
    # Extract working container names
    WORKING_CONTAINER_NAMES=()
    for service in "${WORKING_SERVICES[@]}"; do
        container_name=$(echo $service | cut -d':' -f1)
        WORKING_CONTAINER_NAMES+=("$container_name")
    done
    
    for container in $ALL_CONTAINERS; do
        # Check if container is in working list
        if [[ ! " ${WORKING_CONTAINER_NAMES[@]} " =~ " ${container} " ]]; then
            print_warning "Found unused container: $container"
            
            # Stop and remove container
            docker stop $container 2>/dev/null || true
            docker rm $container 2>/dev/null || true
            print_success "Removed unused container: $container"
        else
            print_success "Container $container is working - keeping"
        fi
    done
}

# Clean up unused Docker images
cleanup_unused_images() {
    print_status "Cleaning up unused Docker images..."
    
    # Remove dangling images
    DANGLING_IMAGES=$(docker images -f "dangling=true" -q)
    if [ ! -z "$DANGLING_IMAGES" ]; then
        print_warning "Removing dangling images..."
        docker rmi $DANGLING_IMAGES 2>/dev/null || true
        print_success "Removed dangling images"
    fi
    
    # Remove unused images (not used by any container)
    UNUSED_IMAGES=$(docker images --format "table {{.Repository}}:{{.Tag}}\t{{.ID}}" | grep -v "REPOSITORY" | while read line; do
        image_id=$(echo $line | awk '{print $2}')
        if [ -z "$(docker ps -a --filter ancestor=$image_id --format {{.ID}})" ]; then
            echo $image_id
        fi
    done)
    
    if [ ! -z "$UNUSED_IMAGES" ]; then
        print_warning "Removing unused images..."
        echo "$UNUSED_IMAGES" | xargs docker rmi 2>/dev/null || true
        print_success "Removed unused images"
    fi
}

# Clean up unused volumes
cleanup_unused_volumes() {
    print_status "Cleaning up unused Docker volumes..."
    
    # Remove unused volumes
    UNUSED_VOLUMES=$(docker volume ls -qf dangling=true)
    if [ ! -z "$UNUSED_VOLUMES" ]; then
        print_warning "Removing unused volumes..."
        docker volume rm $UNUSED_VOLUMES 2>/dev/null || true
        print_success "Removed unused volumes"
    fi
}

# Clean up unused networks
cleanup_unused_networks() {
    print_status "Cleaning up unused Docker networks..."
    
    # Remove unused networks
    UNUSED_NETWORKS=$(docker network ls --filter "dangling=true" -q)
    if [ ! -z "$UNUSED_NETWORKS" ]; then
        print_warning "Removing unused networks..."
        docker network rm $UNUSED_NETWORKS 2>/dev/null || true
        print_success "Removed unused networks"
    fi
    
    # Remove custom networks not in use
    ALL_NETWORKS=$(docker network ls --format "{{.Name}}" | grep -v -E "^(bridge|host|none)$")
    for network in $ALL_NETWORKS; do
        # Check if network is being used
        NETWORK_USAGE=$(docker network inspect $network --format "{{.Containers}}" 2>/dev/null || echo "{}")
        if [ "$NETWORK_USAGE" = "{}" ]; then
            print_warning "Removing unused network: $network"
            docker network rm $network 2>/dev/null || true
            print_success "Removed unused network: $network"
        fi
    done
}

# Remove non-working Docker compose files
cleanup_docker_compose_files() {
    print_status "Cleaning up non-working Docker compose files..."
    
    # Find all docker-compose files
    COMPOSE_FILES=$(find . -name "docker-compose*.yml" -o -name "docker-compose*.yaml" 2>/dev/null)
    
    for compose_file in $COMPOSE_FILES; do
        print_status "Checking compose file: $compose_file"
        
        # Try to validate the compose file
        if ! docker-compose -f "$compose_file" config >/dev/null 2>&1; then
            print_warning "Invalid compose file: $compose_file"
            
            # Create backup before removing
            backup_file="${compose_file}.backup.$(date +%Y%m%d_%H%M%S)"
            cp "$compose_file" "$backup_file"
            print_warning "Backed up to: $backup_file"
            
            # Remove the invalid file
            rm "$compose_file"
            print_success "Removed invalid compose file: $compose_file"
        else
            # Check if any services from this file are running
            SERVICES_IN_FILE=$(docker-compose -f "$compose_file" config --services 2>/dev/null || true)
            RUNNING_FROM_FILE=false
            
            for service in $SERVICES_IN_FILE; do
                if docker ps --format "{{.Names}}" | grep -q "$service"; then
                    RUNNING_FROM_FILE=true
                    break
                fi
            done
            
            if [ "$RUNNING_FROM_FILE" = false ]; then
                print_warning "No services running from: $compose_file"
                
                # Create backup before removing
                backup_file="${compose_file}.unused.$(date +%Y%m%d_%H%M%S)"
                cp "$compose_file" "$backup_file"
                print_warning "Backed up to: $backup_file"
                
                # Remove the unused file
                rm "$compose_file"
                print_success "Removed unused compose file: $compose_file"
            else
                print_success "Compose file has running services - keeping: $compose_file"
            fi
        fi
    done
}

# Clean up temporary files
cleanup_temp_files() {
    print_status "Cleaning up temporary files..."
    
    # Remove Docker build cache
    docker builder prune -f 2>/dev/null || true
    
    # Remove system cache
    docker system prune -f 2>/dev/null || true
    
    # Remove log files older than 7 days
    find . -name "*.log" -type f -mtime +7 -delete 2>/dev/null || true
    
    # Remove backup files older than 30 days
    find . -name "*.backup.*" -type f -mtime +30 -delete 2>/dev/null || true
    
    print_success "Cleaned up temporary files"
}

# Generate cleanup report
generate_cleanup_report() {
    print_status "Generating cleanup report..."
    
    cat > CLEANUP_REPORT.md << EOF
# 🧹 Ultimate Cleanup Report
## SoloYlibre & JEYKO Environment Cleanup

### 📊 Cleanup Summary
**Date**: $(date)
**Performed by**: Ultimate Cleanup Script
**Environment**: SoloYlibre & JEYKO Ultimate Development Environment

---

## ✅ Services Kept (Working)
$(for service in "${WORKING_SERVICES[@]}"; do
    container_name=$(echo $service | cut -d':' -f1)
    ports=$(echo $service | cut -d':' -f2)
    echo "- **$container_name**: Ports $ports"
done)

---

## 🗑️ Cleanup Actions Performed

### Containers
- Stopped and removed all unused containers
- Kept only actively working containers

### Ports
- Cleaned up all unused port bindings
- Kept only ports used by working services: $(echo ${WORKING_PORTS[@]} | tr ' ' ', ')

### Docker Resources
- Removed dangling images
- Removed unused volumes
- Removed unused networks
- Cleaned Docker build cache

### Files
- Removed invalid Docker compose files
- Removed unused Docker compose files
- Cleaned temporary files and logs
- Created backups of removed files

---

## 📊 Current Status
**Running Containers**: $(docker ps -q | wc -l)
**Active Ports**: $(echo ${WORKING_PORTS[@]} | wc -w)
**Docker Images**: $(docker images -q | wc -l)
**Docker Volumes**: $(docker volume ls -q | wc -l)
**Docker Networks**: $(docker network ls -q | wc -l)

---

## 🎯 Result
✅ **Environment Cleaned**: All unused resources removed
✅ **Working Services**: All kept and operational
✅ **Performance**: Improved system performance
✅ **Storage**: Freed up disk space
✅ **Security**: Removed unused attack surfaces

---

*Cleanup completed: $(date)*
*Status: Environment optimized and ready for production*
EOF
    
    print_success "Cleanup report generated: CLEANUP_REPORT.md"
}

# Show final status
show_final_status() {
    echo ""
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║              🎉 CLEANUP COMPLETE! 🎉                       ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🧹 SoloYlibre & JEYKO Environment Cleaned!               ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    
    # Count current resources
    running_containers=$(docker ps -q | wc -l)
    total_images=$(docker images -q | wc -l)
    total_volumes=$(docker volume ls -q | wc -l)
    
    echo -e "${GREEN}║  📦 Running Containers: $running_containers                              ║${NC}"
    echo -e "${GREEN}║  🖼️  Docker Images: $total_images                                   ║${NC}"
    echo -e "${GREEN}║  💾 Docker Volumes: $total_volumes                                   ║${NC}"
    
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🌐 Active Ports: $(echo ${WORKING_PORTS[@]} | tr ' ' ', ')                    ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  ✅ All unused resources removed                           ║${NC}"
    echo -e "${GREEN}║  ✅ Working services preserved                             ║${NC}"
    echo -e "${GREEN}║  ✅ System optimized for performance                       ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  📋 Report: CLEANUP_REPORT.md                              ║${NC}"
    echo -e "${GREEN}║  🏢 Company: SoloYlibre & JEYKO                            ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    
    echo ""
    print_success "Ultimate cleanup completed successfully!"
    print_status "Your environment is now optimized and ready for production!"
}

# Main execution
main() {
    print_header
    
    print_warning "This script will clean ALL unused ports and Docker configurations!"
    print_warning "Only working services will be preserved."
    echo ""
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        show_current_status
        cleanup_unused_ports
        cleanup_unused_containers
        cleanup_unused_images
        cleanup_unused_volumes
        cleanup_unused_networks
        cleanup_docker_compose_files
        cleanup_temp_files
        generate_cleanup_report
        show_final_status
    else
        print_status "Cleanup cancelled by user"
        exit 0
    fi
}

# Run the cleanup
main "$@"
