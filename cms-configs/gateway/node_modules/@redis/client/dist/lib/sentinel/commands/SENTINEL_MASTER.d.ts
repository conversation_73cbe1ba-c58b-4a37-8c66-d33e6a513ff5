import { RedisArgument, MapReply, BlobStringReply } from '../../RESP/types';
import { CommandParser } from '../../client/parser';
declare const _default: {
    /**
     * Returns information about the specified master.
     * @param parser - The Redis command parser.
     * @param dbname - Name of the master.
     */
    readonly parseCommand: (this: void, parser: CommandParser, dbname: RedisArgument) => void;
    readonly transformReply: {
        readonly 2: (reply: import("../../RESP/types").ArrayReply<BlobStringReply<string>>, preserve?: any, typeMapping?: import("../../RESP/types").TypeMapping | undefined) => MapReply<BlobStringReply<string>, BlobStringReply<string>>;
        readonly 3: () => MapReply<BlobStringReply, BlobStringReply>;
    };
};
export default _default;
//# sourceMappingURL=SENTINEL_MASTER.d.ts.map