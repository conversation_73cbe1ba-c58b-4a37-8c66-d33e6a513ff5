import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the RPOPLPUSH command
     *
     * @param parser - The command parser
     * @param source - The source list key
     * @param destination - The destination list key
     * @see https://redis.io/commands/rpoplpush/
     */
    readonly parseCommand: (this: void, parser: CommandParser, source: RedisArgument, destination: RedisArgument) => void;
    readonly transformReply: () => BlobStringReply | NullReply;
};
export default _default;
//# sourceMappingURL=RPOPLPUSH.d.ts.map