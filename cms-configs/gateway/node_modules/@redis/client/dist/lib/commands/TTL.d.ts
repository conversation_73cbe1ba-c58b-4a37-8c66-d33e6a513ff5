import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the TTL command to get the remaining time to live of a key
     *
     * @param parser - The command parser
     * @param key - Key to check
     * @returns Time to live in seconds, -2 if key does not exist, -1 if has no timeout
     * @see https://redis.io/commands/ttl/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=TTL.d.ts.map