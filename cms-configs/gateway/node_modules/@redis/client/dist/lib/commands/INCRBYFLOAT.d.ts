import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the INCRBYFLOAT command
     *
     * @param parser - The command parser
     * @param key - The key to increment
     * @param increment - The floating-point value to increment by
     * @see https://redis.io/commands/incrbyfloat/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, increment: number) => void;
    readonly transformReply: () => BlobStringReply;
};
export default _default;
//# sourceMappingURL=INCRBYFLOAT.d.ts.map