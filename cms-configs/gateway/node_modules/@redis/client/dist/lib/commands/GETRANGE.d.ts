import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Returns a substring of the string stored at a key
     * @param parser - The Redis command parser
     * @param key - Key to get substring from
     * @param start - Start position of the substring
     * @param end - End position of the substring
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, start: number, end: number) => void;
    readonly transformReply: () => BlobStringReply | NullReply;
};
export default _default;
//# sourceMappingURL=GETRANGE.d.ts.map