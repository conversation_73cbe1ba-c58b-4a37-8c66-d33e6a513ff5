import { CommandParser } from '../client/parser';
import { NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the PFCOUNT command
     *
     * @param parser - The command parser
     * @param keys - One or more keys of HyperLogLog structures to count
     * @see https://redis.io/commands/pfcount/
     */
    readonly parseCommand: (this: void, parser: CommandParser, keys: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=PFCOUNT.d.ts.map