import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply, NullReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the OBJECT REFCOUNT command
     *
     * @param parser - The command parser
     * @param key - The key to get the reference count for
     * @see https://redis.io/commands/object-refcount/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => NumberReply | NullReply;
};
export default _default;
//# sourceMappingURL=OBJECT_REFCOUNT.d.ts.map