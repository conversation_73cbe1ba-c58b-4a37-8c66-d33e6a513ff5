import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the LCS command (Longest Common Substring)
     *
     * @param parser - The command parser
     * @param key1 - First key containing the first string
     * @param key2 - Second key containing the second string
     * @see https://redis.io/commands/lcs/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key1: RedisArgument, key2: RedisArgument) => void;
    readonly transformReply: () => BlobStringReply;
};
export default _default;
//# sourceMappingURL=LCS.d.ts.map