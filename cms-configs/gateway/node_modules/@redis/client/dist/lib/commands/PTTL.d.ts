import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the PTTL command
     *
     * @param parser - The command parser
     * @param key - The key to get the time to live in milliseconds
     * @see https://redis.io/commands/pttl/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=PTTL.d.ts.map