import { CommandParser } from '../client/parser';
import { SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Controls whether to prevent the client's connections from being evicted
     * @param parser - The Redis command parser
     * @param value - Whether to enable (true) or disable (false) the no-evict mode
     */
    readonly parseCommand: (this: void, parser: CommandParser, value: boolean) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=CLIENT_NO-EVICT.d.ts.map