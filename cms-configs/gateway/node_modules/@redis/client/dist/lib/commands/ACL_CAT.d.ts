import { CommandParser } from '../client/parser';
import { RedisArgument, ArrayReply, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Lists ACL categories or commands in a category
     * @param parser - The Redis command parser
     * @param categoryName - Optional category name to filter commands
     */
    readonly parseCommand: (this: void, parser: CommandParser, categoryName?: RedisArgument) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply>;
};
export default _default;
//# sourceMappingURL=ACL_CAT.d.ts.map