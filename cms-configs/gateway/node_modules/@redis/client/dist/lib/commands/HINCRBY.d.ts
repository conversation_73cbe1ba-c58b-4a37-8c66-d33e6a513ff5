import { <PERSON><PERSON>arser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    /**
     * Increments the integer value of a field in a hash by the given number
     * @param parser - The Redis command parser
     * @param key - Key of the hash
     * @param field - Field to increment
     * @param increment - Increment amount
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, field: RedisArgument, increment: number) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=HINCRBY.d.ts.map