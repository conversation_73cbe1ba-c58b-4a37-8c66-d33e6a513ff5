import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the OBJECT ENCODING command
     *
     * @param parser - The command parser
     * @param key - The key to get the internal encoding for
     * @see https://redis.io/commands/object-encoding/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => BlobStringReply | NullReply;
};
export default _default;
//# sourceMappingURL=OBJECT_ENCODING.d.ts.map