import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the PEXPIRE command
     *
     * @param parser - The command parser
     * @param key - The key to set the expiration for
     * @param ms - The expiration time in milliseconds
     * @param mode - Optional mode for the command ('NX', 'XX', 'GT', 'LT')
     * @see https://redis.io/commands/pexpire/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, ms: number, mode?: 'NX' | 'XX' | 'GT' | 'LT') => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=PEXPIRE.d.ts.map