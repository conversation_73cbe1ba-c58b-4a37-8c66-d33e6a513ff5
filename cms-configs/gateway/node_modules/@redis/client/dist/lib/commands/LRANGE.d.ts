import { CommandParser } from '../client/parser';
import { RedisArgument, ArrayReply, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the LRANGE command
     *
     * @param parser - The command parser
     * @param key - The key of the list
     * @param start - The starting index
     * @param stop - The ending index
     * @see https://redis.io/commands/lrange/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, start: number, stop: number) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply>;
};
export default _default;
//# sourceMappingURL=LRANGE.d.ts.map