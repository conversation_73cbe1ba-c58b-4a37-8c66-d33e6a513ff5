import { CommandParser } from '../client/parser';
import { ArrayReply, NullReply, NumberReply, RedisArgument } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    /**
     * Removes the expiration from the specified fields in a hash.
     * @param parser - The Redis command parser.
     * @param key - Key of the hash.
     * @param fields - Fields to remove expiration from.
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, fields: RedisVariadicArgument) => void;
    readonly transformReply: () => ArrayReply<NumberReply> | NullReply;
};
export default _default;
//# sourceMappingURL=HPERSIST.d.ts.map