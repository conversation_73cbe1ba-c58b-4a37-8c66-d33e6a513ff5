import { CommandParser } from '../client/parser';
import { ArrayReply, NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the SCRIPT EXISTS command
     *
     * @param parser - The command parser
     * @param sha1 - One or more SHA1 digests of scripts
     * @see https://redis.io/commands/script-exists/
     */
    readonly parseCommand: (this: void, parser: CommandParser, sha1: RedisVariadicArgument) => void;
    readonly transformReply: () => ArrayReply<NumberReply>;
};
export default _default;
//# sourceMappingURL=SCRIPT_EXISTS.d.ts.map