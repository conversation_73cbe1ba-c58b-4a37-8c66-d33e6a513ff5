import { CommandParser } from '../client/parser';
import { ArrayReply, NullReply, NumberReply, RedisArgument } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the HPEXPIRETIME command
     *
     * @param parser - The command parser
     * @param key - The key to retrieve expiration time for
     * @param fields - The fields to retrieve expiration time for
     * @see https://redis.io/commands/hpexpiretime/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, fields: RedisVariadicArgument) => void;
    readonly transformReply: () => ArrayReply<NumberReply> | NullReply;
};
export default _default;
//# sourceMappingURL=HPEXPIRETIME.d.ts.map