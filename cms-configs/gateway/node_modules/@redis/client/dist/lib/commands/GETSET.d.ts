import { <PERSON>Parser } from '../client/parser';
import { RedisArgument, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Sets a key to a new value and returns its old value
     * @param parser - The Redis command parser
     * @param key - Key to set
     * @param value - Value to set
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, value: RedisArgument) => void;
    readonly transformReply: () => BlobStringReply | NullReply;
};
export default _default;
//# sourceMappingURL=GETSET.d.ts.map