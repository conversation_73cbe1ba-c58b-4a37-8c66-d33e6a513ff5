import { Command<PERSON>arser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the HSETNX command
     *
     * @param parser - The command parser
     * @param key - The key of the hash
     * @param field - The field to set if it does not exist
     * @param value - The value to set
     * @see https://redis.io/commands/hsetnx/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, field: RedisArgument, value: RedisArgument) => void;
    readonly transformReply: () => NumberReply<0 | 1>;
};
export default _default;
//# sourceMappingURL=HSETNX.d.ts.map