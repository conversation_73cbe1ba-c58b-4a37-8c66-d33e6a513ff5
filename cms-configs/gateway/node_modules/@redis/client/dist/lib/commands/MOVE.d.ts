import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the MOVE command
     *
     * @param parser - The command parser
     * @param key - The key to move
     * @param db - The destination database index
     * @see https://redis.io/commands/move/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, db: number) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=MOVE.d.ts.map