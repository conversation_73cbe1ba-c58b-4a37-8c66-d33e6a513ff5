import { CommandParser } from '../client/parser';
import { RedisArgument, ArrayReply, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the RPOP command with count parameter
     *
     * @param parser - The command parser
     * @param key - The list key to pop from
     * @param count - The number of elements to pop
     * @see https://redis.io/commands/rpop/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, count: number) => void;
    readonly transformReply: () => ArrayReply<BlobStringReply> | NullReply;
};
export default _default;
//# sourceMappingURL=RPOP_COUNT.d.ts.map