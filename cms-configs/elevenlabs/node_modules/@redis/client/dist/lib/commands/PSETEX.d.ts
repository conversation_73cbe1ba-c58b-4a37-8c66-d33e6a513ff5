import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the PSETEX command
     *
     * @param parser - The command parser
     * @param key - The key to set
     * @param ms - The expiration time in milliseconds
     * @param value - The value to set
     * @see https://redis.io/commands/psetex/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, ms: number, value: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=PSETEX.d.ts.map