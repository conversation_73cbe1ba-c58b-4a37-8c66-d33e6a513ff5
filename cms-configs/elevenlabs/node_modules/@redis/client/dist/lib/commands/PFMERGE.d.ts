import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    /**
     * Constructs the PFMERGE command
     *
     * @param parser - The command parser
     * @param destination - The destination key to merge to
     * @param sources - One or more source keys to merge from
     * @see https://redis.io/commands/pfmerge/
     */
    readonly parseCommand: (this: void, parser: CommandParser, destination: RedisArgument, sources?: RedisVariadicArgument) => void;
    readonly transformReply: () => SimpleStringReply;
};
export default _default;
//# sourceMappingURL=PFMERGE.d.ts.map