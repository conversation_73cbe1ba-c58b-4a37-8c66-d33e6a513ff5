import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply, BlobStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the PING command
     *
     * @param parser - The command parser
     * @param message - Optional message to be returned instead of PONG
     * @see https://redis.io/commands/ping/
     */
    readonly parseCommand: (this: void, parser: CommandParser, message?: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply | BlobStringReply;
};
export default _default;
//# sourceMappingURL=PING.d.ts.map