import { CommandParser } from '../client/parser';
import { NumberReply } from '../RESP/types';
import { RedisVariadicArgument } from './generic-transformers';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Constructs the UNLINK command to asynchronously delete one or more keys
     *
     * @param parser - The command parser
     * @param keys - One or more keys to unlink
     * @returns The number of keys that were unlinked
     * @see https://redis.io/commands/unlink/
     */
    readonly parseCommand: (this: void, parser: CommandParser, keys: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=UNLINK.d.ts.map