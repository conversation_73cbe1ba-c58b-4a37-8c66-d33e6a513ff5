import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the LSET command
     *
     * @param parser - The command parser
     * @param key - The key of the list
     * @param index - The index of the element to replace
     * @param element - The new value to set
     * @see https://redis.io/commands/lset/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, index: number, element: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=LSET.d.ts.map