import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
import { SortOptions } from './SORT';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Sorts the elements in a list, set or sorted set and stores the result in a new list.
     * @param parser - The Redis command parser.
     * @param source - Key of the source list, set or sorted set.
     * @param destination - Destination key where the result will be stored.
     * @param options - Optional sorting parameters.
     */
    readonly parseCommand: (this: void, parser: CommandParser, source: RedisArgument, destination: RedisArgument, options?: SortOptions) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=SORT_STORE.d.ts.map