import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the LPOP command
     *
     * @param parser - The command parser
     * @param key - The key of the list to pop from
     * @see https://redis.io/commands/lpop/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => BlobStringReply | NullReply;
};
export default _default;
//# sourceMappingURL=LPOP.d.ts.map