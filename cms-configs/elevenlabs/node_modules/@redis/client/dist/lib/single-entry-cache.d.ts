export default class SingleEntryCache<K, V> {
    #private;
    /**
     * Retrieves an instance from the cache based on the provided key object.
     *
     * @param keyObj - The key object to look up in the cache.
     * @returns The cached instance if found, undefined otherwise.
     *
     * @remarks
     * This method uses JSON.stringify for comparison, which may not work correctly
     * if the properties in the key object are rearranged or reordered.
     */
    get(keyObj?: K): V | undefined;
    set(keyObj: K | undefined, obj: V): void;
}
//# sourceMappingURL=single-entry-cache.d.ts.map