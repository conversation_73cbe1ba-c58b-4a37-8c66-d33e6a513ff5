declare const _default: {
    readonly SENTINEL_SENTINELS: {
        readonly parseCommand: (this: void, parser: import("../../..").CommandParser, dbname: import("../../RESP/types").RedisArgument) => void;
        readonly transformReply: {
            readonly 2: (reply: import("../../RESP/types").ArrayReply<import("../../RESP/types").ArrayReply<import("../../RESP/types").BlobStringReply<string>>>, preserve?: any, typeMapping?: import("../../RESP/types").TypeMapping | undefined) => import("../../RESP/types").MapReply<import("../../RESP/types").BlobStringReply<string>, import("../../RESP/types").BlobStringReply<string>>[];
            readonly 3: () => import("../../RESP/types").ArrayReply<import("../../RESP/types").MapReply<import("../../RESP/types").BlobStringReply<string>, import("../../RESP/types").BlobStringReply<string>>>;
        };
    };
    readonly sentinelSentinels: {
        readonly parseCommand: (this: void, parser: import("../../..").CommandParser, dbname: import("../../RESP/types").RedisArgument) => void;
        readonly transformReply: {
            readonly 2: (reply: import("../../RESP/types").ArrayReply<import("../../RESP/types").ArrayReply<import("../../RESP/types").BlobStringReply<string>>>, preserve?: any, typeMapping?: import("../../RESP/types").TypeMapping | undefined) => import("../../RESP/types").MapReply<import("../../RESP/types").BlobStringReply<string>, import("../../RESP/types").BlobStringReply<string>>[];
            readonly 3: () => import("../../RESP/types").ArrayReply<import("../../RESP/types").MapReply<import("../../RESP/types").BlobStringReply<string>, import("../../RESP/types").BlobStringReply<string>>>;
        };
    };
    readonly SENTINEL_MASTER: {
        readonly parseCommand: (this: void, parser: import("../../..").CommandParser, dbname: import("../../RESP/types").RedisArgument) => void;
        readonly transformReply: {
            readonly 2: (reply: import("../../RESP/types").ArrayReply<import("../../RESP/types").BlobStringReply<string>>, preserve?: any, typeMapping?: import("../../RESP/types").TypeMapping | undefined) => import("../../RESP/types").MapReply<import("../../RESP/types").BlobStringReply<string>, import("../../RESP/types").BlobStringReply<string>>;
            readonly 3: () => import("../../RESP/types").MapReply<import("../../RESP/types").BlobStringReply<string>, import("../../RESP/types").BlobStringReply<string>>;
        };
    };
    readonly sentinelMaster: {
        readonly parseCommand: (this: void, parser: import("../../..").CommandParser, dbname: import("../../RESP/types").RedisArgument) => void;
        readonly transformReply: {
            readonly 2: (reply: import("../../RESP/types").ArrayReply<import("../../RESP/types").BlobStringReply<string>>, preserve?: any, typeMapping?: import("../../RESP/types").TypeMapping | undefined) => import("../../RESP/types").MapReply<import("../../RESP/types").BlobStringReply<string>, import("../../RESP/types").BlobStringReply<string>>;
            readonly 3: () => import("../../RESP/types").MapReply<import("../../RESP/types").BlobStringReply<string>, import("../../RESP/types").BlobStringReply<string>>;
        };
    };
    readonly SENTINEL_REPLICAS: {
        readonly parseCommand: (this: void, parser: import("../../..").CommandParser, dbname: import("../../RESP/types").RedisArgument) => void;
        readonly transformReply: {
            readonly 2: (reply: import("../../RESP/types").ArrayReply<import("../../RESP/types").ArrayReply<import("../../RESP/types").BlobStringReply<string>>>, preserve?: any, typeMapping?: import("../../RESP/types").TypeMapping | undefined) => import("../../RESP/types").MapReply<import("../../RESP/types").BlobStringReply<string>, import("../../RESP/types").BlobStringReply<string>>[];
            readonly 3: () => import("../../RESP/types").ArrayReply<import("../../RESP/types").MapReply<import("../../RESP/types").BlobStringReply<string>, import("../../RESP/types").BlobStringReply<string>>>;
        };
    };
    readonly sentinelReplicas: {
        readonly parseCommand: (this: void, parser: import("../../..").CommandParser, dbname: import("../../RESP/types").RedisArgument) => void;
        readonly transformReply: {
            readonly 2: (reply: import("../../RESP/types").ArrayReply<import("../../RESP/types").ArrayReply<import("../../RESP/types").BlobStringReply<string>>>, preserve?: any, typeMapping?: import("../../RESP/types").TypeMapping | undefined) => import("../../RESP/types").MapReply<import("../../RESP/types").BlobStringReply<string>, import("../../RESP/types").BlobStringReply<string>>[];
            readonly 3: () => import("../../RESP/types").ArrayReply<import("../../RESP/types").MapReply<import("../../RESP/types").BlobStringReply<string>, import("../../RESP/types").BlobStringReply<string>>>;
        };
    };
    readonly SENTINEL_MONITOR: {
        readonly parseCommand: (this: void, parser: import("../../..").CommandParser, dbname: import("../../RESP/types").RedisArgument, host: import("../../RESP/types").RedisArgument, port: import("../../RESP/types").RedisArgument, quorum: import("../../RESP/types").RedisArgument) => void;
        readonly transformReply: () => import("../../RESP/types").SimpleStringReply<"OK">;
    };
    readonly sentinelMonitor: {
        readonly parseCommand: (this: void, parser: import("../../..").CommandParser, dbname: import("../../RESP/types").RedisArgument, host: import("../../RESP/types").RedisArgument, port: import("../../RESP/types").RedisArgument, quorum: import("../../RESP/types").RedisArgument) => void;
        readonly transformReply: () => import("../../RESP/types").SimpleStringReply<"OK">;
    };
    readonly SENTINEL_SET: {
        readonly parseCommand: (this: void, parser: import("../../..").CommandParser, dbname: import("../../RESP/types").RedisArgument, options: import("./SENTINEL_SET").SentinelSetOptions) => void;
        readonly transformReply: () => import("../../RESP/types").SimpleStringReply<"OK">;
    };
    readonly sentinelSet: {
        readonly parseCommand: (this: void, parser: import("../../..").CommandParser, dbname: import("../../RESP/types").RedisArgument, options: import("./SENTINEL_SET").SentinelSetOptions) => void;
        readonly transformReply: () => import("../../RESP/types").SimpleStringReply<"OK">;
    };
};
export default _default;
//# sourceMappingURL=index.d.ts.map