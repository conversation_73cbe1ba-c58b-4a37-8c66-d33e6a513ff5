version: '3.8'

services:
  portainer:
    image: portainer/portainer-ce:latest
    container_name: portainer
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - portainer_data:/data
    ports:
      - "9000:9000"
      - "9443:9443"
    networks:
      - portainer_network
    environment:
      - PORTAINER_ADMIN_PASSWORD_FILE=/run/secrets/portainer_admin_password
    secrets:
      - portainer_admin_password
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.portainer.rule=Host(`portainer.localhost`)"
      - "traefik.http.routers.portainer.entrypoints=web"
      - "traefik.http.services.portainer.loadbalancer.server.port=9000"
      - "traefik.http.routers.portainer-secure.rule=Host(`portainer.localhost`)"
      - "traefik.http.routers.portainer-secure.entrypoints=websecure"
      - "traefik.http.routers.portainer-secure.tls=true"

  # Optional: Portainer Agent for remote Docker environments
  portainer-agent:
    image: portainer/agent:latest
    container_name: portainer-agent
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /var/lib/docker/volumes:/var/lib/docker/volumes
    ports:
      - "9001:9001"
    networks:
      - portainer_network
    environment:
      - AGENT_CLUSTER_ADDR=portainer-agent
      - AGENT_PORT=9001
      - LOG_LEVEL=INFO

volumes:
  portainer_data:
    driver: local

networks:
  portainer_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

secrets:
  portainer_admin_password:
    file: ./secrets/portainer_admin_password.txt
