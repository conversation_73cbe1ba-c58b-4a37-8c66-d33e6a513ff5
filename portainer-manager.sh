#!/bin/bash

# Portainer Management Script
# Professional 2000% IQ Management for Ultimate Dev Environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PORTAINER_CONTAINER_NAME="portainer"
PORTAINER_HTTP_PORT="9000"
PORTAINER_HTTPS_PORT="9443"
PORTAINER_VOLUME="portainer_data"

# Function to print colored output
print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    PORTAINER MANAGER                        ║"
    echo "║              Professional 2000% IQ Management               ║"
    echo "║                Ultimate Dev Environment                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_action() {
    echo -e "${PURPLE}[ACTION]${NC} $1"
}

# Check if Portainer is running
check_portainer_status() {
    if docker ps --format "table {{.Names}}" | grep -q "^${PORTAINER_CONTAINER_NAME}$"; then
        return 0  # Running
    else
        return 1  # Not running
    fi
}

# Start Portainer
start_portainer() {
    print_action "Starting Portainer..."

    if check_portainer_status; then
        print_warning "Portainer is already running"
        return 0
    fi

    # Check if container exists but is stopped
    if docker ps -a --format "table {{.Names}}" | grep -q "^${PORTAINER_CONTAINER_NAME}$"; then
        print_status "Starting existing Portainer container..."
        docker start ${PORTAINER_CONTAINER_NAME}
    else
        print_status "Creating new Portainer container..."
        docker run -d \
            -p ${PORTAINER_HTTP_PORT}:9000 \
            -p ${PORTAINER_HTTPS_PORT}:9443 \
            --name ${PORTAINER_CONTAINER_NAME} \
            --restart=always \
            -v /var/run/docker.sock:/var/run/docker.sock \
            -v ${PORTAINER_VOLUME}:/data \
            portainer/portainer-ce:latest
    fi

    print_success "Portainer started successfully!"
}

# Stop Portainer
stop_portainer() {
    print_action "Stopping Portainer..."

    if ! check_portainer_status; then
        print_warning "Portainer is not running"
        return 0
    fi

    docker stop ${PORTAINER_CONTAINER_NAME}
    print_success "Portainer stopped successfully!"
}

# Restart Portainer
restart_portainer() {
    print_action "Restarting Portainer..."
    stop_portainer
    sleep 2
    start_portainer
}

# Show Portainer status
show_status() {
    print_header
    print_status "Checking Portainer status..."

    if check_portainer_status; then
        print_success "✅ Portainer is RUNNING"

        # Get container details
        CONTAINER_ID=$(docker ps --filter "name=${PORTAINER_CONTAINER_NAME}" --format "{{.ID}}")
        UPTIME=$(docker ps --filter "name=${PORTAINER_CONTAINER_NAME}" --format "{{.Status}}")

        echo ""
        echo -e "${BLUE}📊 Container Details:${NC}"
        echo "   • Container ID: ${CONTAINER_ID}"
        echo "   • Status: ${UPTIME}"
        echo "   • HTTP Port: ${PORTAINER_HTTP_PORT}"
        echo "   • HTTPS Port: ${PORTAINER_HTTPS_PORT}"
        echo ""
        echo -e "${GREEN}🌐 Access URLs:${NC}"
        echo "   • HTTP:  http://localhost:${PORTAINER_HTTP_PORT}"
        echo "   • HTTPS: https://localhost:${PORTAINER_HTTPS_PORT}"

    else
        print_error "❌ Portainer is NOT RUNNING"

        # Check if container exists
        if docker ps -a --format "table {{.Names}}" | grep -q "^${PORTAINER_CONTAINER_NAME}$"; then
            print_warning "Container exists but is stopped"
        else
            print_warning "Container does not exist"
        fi
    fi
}

# Show logs
show_logs() {
    print_action "Showing Portainer logs..."

    if ! docker ps -a --format "table {{.Names}}" | grep -q "^${PORTAINER_CONTAINER_NAME}$"; then
        print_error "Portainer container does not exist"
        return 1
    fi

    docker logs -f ${PORTAINER_CONTAINER_NAME}
}

# Update Portainer
update_portainer() {
    print_action "Updating Portainer..."

    print_status "Pulling latest Portainer image..."
    docker pull portainer/portainer-ce:latest

    if check_portainer_status; then
        print_status "Stopping current Portainer instance..."
        docker stop ${PORTAINER_CONTAINER_NAME}
    fi

    print_status "Removing old container..."
    docker rm ${PORTAINER_CONTAINER_NAME} 2>/dev/null || true

    print_status "Starting updated Portainer..."
    start_portainer

    print_success "Portainer updated successfully!"
}

# Remove Portainer completely
remove_portainer() {
    print_action "Removing Portainer completely..."

    read -p "Are you sure you want to remove Portainer and all its data? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Operation cancelled"
        return 0
    fi

    if check_portainer_status; then
        print_status "Stopping Portainer..."
        docker stop ${PORTAINER_CONTAINER_NAME}
    fi

    print_status "Removing container..."
    docker rm ${PORTAINER_CONTAINER_NAME} 2>/dev/null || true

    print_status "Removing volume..."
    docker volume rm ${PORTAINER_VOLUME} 2>/dev/null || true

    print_success "Portainer removed completely!"
}

# Show help
show_help() {
    print_header
    echo -e "${YELLOW}Usage:${NC} $0 [COMMAND]"
    echo ""
    echo -e "${YELLOW}Commands:${NC}"
    echo "  start     Start Portainer"
    echo "  stop      Stop Portainer"
    echo "  restart   Restart Portainer"
    echo "  status    Show Portainer status"
    echo "  logs      Show Portainer logs"
    echo "  update    Update Portainer to latest version"
    echo "  remove    Remove Portainer completely"
    echo "  help      Show this help message"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo "  $0 start"
    echo "  $0 status"
    echo "  $0 logs"
    echo ""
}

# Main function
main() {
    case "${1:-status}" in
        start)
            print_header
            start_portainer
            ;;
        stop)
            print_header
            stop_portainer
            ;;
        restart)
            print_header
            restart_portainer
            ;;
        status)
            show_status
            ;;
        logs)
            print_header
            show_logs
            ;;
        update)
            print_header
            update_portainer
            ;;
        remove)
            print_header
            remove_portainer
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"