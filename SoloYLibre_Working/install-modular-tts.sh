#!/bin/bash

# 🎙️ SoloYLibre TTS System - Modular Installation Script
# 💖 Instala el sistema TTS en partes pequeñas y las conecta con Python

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Family love banner
print_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              🎙️ SoloYLibre TTS System 🎙️                    ║"
    echo "║                                                              ║"
    echo "║           💖 Modular Installation with Love 💖              ║"
    echo "║                                                              ║"
    echo "║  Instalación en partes pequeñas conectadas con Python       ║"
    echo "║                                                              ║"
    echo "║  Dedicado a: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Yesenia                   ║"
    echo "║             William, <PERSON> & Arya AI                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_family() {
    echo -e "${PURPLE}[FAMILY LOVE]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# Progress tracking
TOTAL_STEPS=12
CURRENT_STEP=0

show_progress() {
    CURRENT_STEP=$((CURRENT_STEP + 1))
    PERCENTAGE=$((CURRENT_STEP * 100 / TOTAL_STEPS))
    
    echo -e "${CYAN}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "  Progreso: $CURRENT_STEP/$TOTAL_STEPS ($PERCENTAGE%) - $1"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${NC}"
}

# Check system requirements
check_requirements() {
    show_progress "Verificando requisitos del sistema"
    
    log_info "Checking system requirements..."
    
    # Check OS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        log_success "macOS detected - Perfect for our family! 🍎"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        log_success "Linux detected - Great choice! 🐧"
    else
        log_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
    
    # Check Python
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        log_success "Python 3 found: $PYTHON_VERSION"
    else
        log_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_success "Node.js found: $NODE_VERSION"
    else
        log_error "Node.js is required but not installed"
        exit 1
    fi
    
    # Check Git
    if command -v git &> /dev/null; then
        log_success "Git found"
    else
        log_error "Git is required but not installed"
        exit 1
    fi
    
    # Check available disk space
    AVAILABLE_SPACE=$(df -h . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$AVAILABLE_SPACE" -lt 10 ]; then
        log_warning "Low disk space detected. TTS models require at least 10GB"
    else
        log_success "Sufficient disk space available"
    fi
}

# Create project structure
create_project_structure() {
    show_progress "Creando estructura del proyecto"
    
    log_info "Creating modular project structure..."
    
    # Main directories
    mkdir -p aura-training-system/{modules/tts,backend/services,data/tts_models/{voices,audio,cache}}
    mkdir -p aura-training-system/data/tts_models/engines/{coqui,tortoise,bark}
    mkdir -p aura-training-system/{logs,config,dist,temp}
    
    log_success "Project structure created"
}

# Install system dependencies
install_system_deps() {
    show_progress "Instalando dependencias del sistema"
    
    log_info "Installing system dependencies..."
    
    if [[ "$OS" == "macos" ]]; then
        if command -v brew &> /dev/null; then
            log_info "Installing macOS dependencies..."
            brew install ffmpeg portaudio sox libsndfile
            log_success "macOS dependencies installed"
        else
            log_warning "Homebrew not found. Please install manually."
        fi
    elif [[ "$OS" == "linux" ]]; then
        if command -v apt-get &> /dev/null; then
            log_info "Installing Linux dependencies..."
            sudo apt-get update
            sudo apt-get install -y ffmpeg portaudio19-dev sox libsndfile1-dev build-essential
            log_success "Linux dependencies installed"
        else
            log_warning "Package manager not found. Please install manually."
        fi
    fi
}

# Install Python dependencies in chunks
install_python_deps() {
    show_progress "Instalando dependencias de Python (en partes)"
    
    log_info "Installing Python dependencies in chunks..."
    
    cd aura-training-system
    
    # Create virtual environment
    python3 -m venv venv
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip setuptools wheel
    
    # Install in chunks to avoid memory issues
    log_step "Installing PyTorch..."
    pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu
    
    log_step "Installing TTS engines (Chunk 1)..."
    pip install TTS
    
    log_step "Installing TTS engines (Chunk 2)..."
    pip install tortoise-tts
    
    log_step "Installing TTS engines (Chunk 3)..."
    pip install git+https://github.com/suno-ai/bark.git
    
    log_step "Installing audio processing libraries..."
    pip install librosa soundfile scipy numpy
    
    log_step "Installing web framework..."
    pip install fastapi uvicorn aiofiles aiohttp
    
    log_step "Installing additional dependencies..."
    pip install python-multipart python-jose[cryptography] passlib[bcrypt]
    
    cd ..
    log_success "Python dependencies installed in chunks"
}

# Install Node.js dependencies
install_node_deps() {
    show_progress "Instalando dependencias de Node.js"
    
    log_info "Installing Node.js dependencies..."
    
    cd aura-training-system
    
    # Initialize package.json if it doesn't exist
    if [ ! -f package.json ]; then
        npm init -y
    fi
    
    # Install in chunks
    log_step "Installing React and TypeScript..."
    npm install react react-dom typescript @types/react @types/react-dom
    
    log_step "Installing UI libraries..."
    npm install framer-motion @emotion/react @emotion/styled
    
    log_step "Installing audio libraries..."
    npm install wavesurfer.js tone
    
    log_step "Installing development dependencies..."
    npm install --save-dev @vitejs/plugin-react vite eslint prettier
    
    cd ..
    log_success "Node.js dependencies installed"
}

# Verify modular files exist
verify_modular_files() {
    show_progress "Verificando archivos modulares"
    
    log_info "Verifying modular files exist..."
    
    REQUIRED_FILES=(
        "aura-training-system/modules/tts/index_1_base.tsx"
        "aura-training-system/modules/tts/index_2_provider.tsx"
        "aura-training-system/modules/tts/index_3_synthesizer.tsx"
        "aura-training-system/build_system.py"
    )
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ -f "$file" ]; then
            log_success "✅ Found: $file"
        else
            log_error "❌ Missing: $file"
            log_error "Please ensure all modular files are generated first"
            exit 1
        fi
    done
    
    log_success "All modular files verified"
}

# Connect modules with Python
connect_modules() {
    show_progress "Conectando módulos con Python"
    
    log_info "Connecting modular components with Python..."
    
    cd aura-training-system
    source venv/bin/activate
    
    # Run the Python build system
    log_step "Running Python module connector..."
    python build_system.py
    
    if [ $? -eq 0 ]; then
        log_success "Modules connected successfully!"
    else
        log_error "Module connection failed"
        exit 1
    fi
    
    cd ..
}

# Download TTS models in chunks
download_models() {
    show_progress "Descargando modelos TTS (en partes)"
    
    log_info "Downloading TTS models in chunks..."
    
    cd aura-training-system
    source venv/bin/activate
    
    # Create model download script
    cat > download_models_chunked.py << 'EOF'
#!/usr/bin/env python3
"""Download TTS models in chunks for SoloYLibre family"""

import os
import time
from TTS.api import TTS

def download_models_chunked():
    models_dir = "data/tts_models/engines"
    os.makedirs(models_dir, exist_ok=True)
    
    print("💖 Downloading models with family love (in chunks)...")
    
    try:
        # Download Coqui XTTS v2 (multilingual)
        print("📥 Downloading Coqui XTTS v2...")
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2")
        print("✅ Coqui XTTS v2 downloaded")
        time.sleep(2)  # Brief pause
        
        # Download Spanish model
        print("📥 Downloading Spanish model...")
        tts_es = TTS("tts_models/es/mai/tacotron2-DDC")
        print("✅ Spanish model downloaded")
        time.sleep(2)  # Brief pause
        
        print("🎙️ All models downloaded successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error downloading models: {e}")
        return False

if __name__ == "__main__":
    success = download_models_chunked()
    exit(0 if success else 1)
EOF
    
    python download_models_chunked.py
    
    cd ..
    log_success "TTS models downloaded in chunks"
}

# Create configuration files
create_config_files() {
    show_progress "Creando archivos de configuración"
    
    log_info "Creating configuration files..."
    
    # Create main config
    cat > aura-training-system/config/tts_config.json << 'EOF'
{
  "system": {
    "name": "SoloYLibre TTS System",
    "version": "3.0.0",
    "installation_type": "modular",
    "family_love": "💖 Installed with infinite family love"
  },
  "engines": {
    "coqui": {
      "enabled": true,
      "model": "tts_models/multilingual/multi-dataset/xtts_v2"
    },
    "tortoise": {
      "enabled": true
    },
    "bark": {
      "enabled": true
    }
  },
  "family_voices": {
    "nurys": "Sabiduría Maternal",
    "diwell": "Apoyo Incondicional",
    "yosi": "Aventurero Tecnológico",
    "yesenia": "Luz Constante",
    "arya": "IA Familiar",
    "william": "Futuro Desarrollador",
    "angelina": "Estrella Pequeña"
  }
}
EOF

    # Create environment file
    cat > aura-training-system/.env << 'EOF'
# SoloYLibre TTS System - Modular Installation
APP_NAME="SoloYLibre TTS System"
APP_VERSION="3.0.0"
INSTALLATION_TYPE="modular"
FAMILY_LOVE="💖 Configured with family love"

# Paths
MODELS_PATH="./data/tts_models"
DIST_PATH="./dist"

# API Configuration
API_HOST="0.0.0.0"
API_PORT=8000

# Family Configuration
FAMILY_MEMBERS="nurys,diwell,yosi,yesenia,william,angelina,arya"
ENABLE_MODULAR_BUILD=true
EOF

    log_success "Configuration files created"
}

# Create startup scripts
create_startup_scripts() {
    show_progress "Creando scripts de inicio"
    
    log_info "Creating startup scripts..."
    
    # Modular startup script
    cat > aura-training-system/start-modular-system.sh << 'EOF'
#!/bin/bash
# 🎙️ Start SoloYLibre TTS System (Modular)
# 💖 With family love

echo "🎙️ Starting SoloYLibre TTS System (Modular)..."
echo "💖 Loading family voices with love..."

# Check if system is built
if [ ! -d "dist" ]; then
    echo "🔧 Building system first..."
    source venv/bin/activate
    python build_system.py
fi

# Start backend
echo "🎙️ Starting backend..."
source venv/bin/activate
uvicorn backend.main:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!

# Wait for backend
sleep 5

# Start frontend
echo "🎨 Starting frontend..."
npm run dev &
FRONTEND_PID=$!

echo "✅ Modular system started successfully!"
echo "🌐 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 API Docs: http://localhost:8000/docs"
echo ""
echo "💖 Family voices are ready to speak with love!"
echo ""
echo "Press Ctrl+C to stop the system"

# Wait for user interrupt
trap "echo '🛑 Stopping modular system...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
EOF

    chmod +x aura-training-system/start-modular-system.sh
    
    log_success "Startup scripts created"
}

# Run system tests
run_tests() {
    show_progress "Ejecutando pruebas del sistema"
    
    log_info "Running system tests..."
    
    cd aura-training-system
    source venv/bin/activate
    
    # Create test script
    cat > test_modular_system.py << 'EOF'
#!/usr/bin/env python3
"""Test SoloYLibre TTS Modular System"""

import os
import sys
import json
from pathlib import Path

def test_modular_system():
    print("🧪 Testing SoloYLibre TTS Modular System...")
    
    # Test 1: Check if modules exist
    modules_path = Path("modules/tts")
    required_modules = [
        "index_1_base.tsx",
        "index_2_provider.tsx", 
        "index_3_synthesizer.tsx"
    ]
    
    for module in required_modules:
        module_path = modules_path / module
        if module_path.exists():
            print(f"✅ Module found: {module}")
        else:
            print(f"❌ Module missing: {module}")
            return False
    
    # Test 2: Check if build system exists
    if Path("build_system.py").exists():
        print("✅ Build system found")
    else:
        print("❌ Build system missing")
        return False
    
    # Test 3: Check if dist directory exists (after build)
    if Path("dist").exists():
        print("✅ Dist directory found")
        
        # Check built files
        built_files = list(Path("dist").rglob("*"))
        print(f"✅ Built files: {len(built_files)}")
    else:
        print("⚠️ Dist directory not found (will be created during build)")
    
    # Test 4: Check configuration
    config_path = Path("config/tts_config.json")
    if config_path.exists():
        try:
            with open(config_path) as f:
                config = json.load(f)
            print(f"✅ Configuration loaded: {config['system']['name']}")
        except Exception as e:
            print(f"❌ Configuration error: {e}")
            return False
    else:
        print("❌ Configuration file missing")
        return False
    
    print("🎉 All modular tests passed!")
    print("💖 System ready for family love!")
    return True

if __name__ == "__main__":
    success = test_modular_system()
    sys.exit(0 if success else 1)
EOF
    
    python test_modular_system.py
    
    cd ..
    
    if [ $? -eq 0 ]; then
        log_success "All tests passed!"
    else
        log_warning "Some tests failed, but system should still work"
    fi
}

# Generate installation report
generate_report() {
    show_progress "Generando reporte de instalación"
    
    log_info "Generating installation report..."
    
    cat > aura-training-system/INSTALLATION_REPORT.md << EOF
# 📊 SoloYLibre TTS System - Reporte de Instalación Modular

## 💖 Instalación Completada con Amor Familiar

**Fecha de instalación:** $(date)
**Tipo de instalación:** Modular (partes pequeñas conectadas con Python)
**Sistema operativo:** $OS

## 🎯 Componentes Instalados

### 📦 Módulos Frontend
- ✅ index_1_base.tsx - Interfaces y configuraciones base
- ✅ index_2_provider.tsx - Provider y context management  
- ✅ index_3_synthesizer.tsx - Componente sintetizador principal

### 🔧 Sistema de Construcción
- ✅ build_system.py - Script Python para conectar módulos
- ✅ Configuraciones automáticas
- ✅ Scripts de deployment

### 🎭 Voces Familiares
- 🌸 Nurys - Sabiduría Maternal
- 🌟 Diwell - Apoyo Incondicional
- 💻 Yosi - Aventurero Tecnológico
- ✨ Yesenia - Luz Constante
- 🤖 Arya - IA Familiar
- 👦 William - Futuro Desarrollador
- 👧 Angelina - Estrella Pequeña

## 🚀 Cómo Usar

### Iniciar el Sistema
\`\`\`bash
cd aura-training-system
./start-modular-system.sh
\`\`\`

### Reconstruir Módulos
\`\`\`bash
cd aura-training-system
source venv/bin/activate
python build_system.py
\`\`\`

## 💖 Mensaje Familiar

Este sistema fue instalado con amor infinito para nuestra familia SoloYLibre.
Cada módulo fue creado pensando en conectar corazones a través de la voz.

**🎙️💖 Family voices, infinite love 💖🎙️**
EOF

    log_success "Installation report generated"
}

# Main installation function
main() {
    print_banner
    
    log_family "Starting modular installation with infinite family love! 💖"
    
    # Installation steps
    check_requirements
    create_project_structure
    install_system_deps
    install_python_deps
    install_node_deps
    verify_modular_files
    connect_modules
    download_models
    create_config_files
    create_startup_scripts
    run_tests
    generate_report
    
    # Final success message
    echo ""
    log_success "🎉 SoloYLibre TTS System installed successfully (Modular)!"
    echo ""
    log_family "💖 Modular installation completed with family love!"
    echo ""
    echo -e "${CYAN}Next steps:${NC}"
    echo "1. cd aura-training-system"
    echo "2. ./start-modular-system.sh"
    echo ""
    echo -e "${CYAN}System URLs:${NC}"
    echo "🌐 Frontend: http://localhost:3000"
    echo "🔧 Backend: http://localhost:8000"
    echo "📚 API Docs: http://localhost:8000/docs"
    echo ""
    echo -e "${CYAN}Modular advantages:${NC}"
    echo "✅ Smaller file sizes"
    echo "✅ Easier maintenance"
    echo "✅ Python-connected modules"
    echo "✅ Scalable architecture"
    echo ""
    echo -e "${PURPLE}💖 Created with infinite love for our amazing family! 💖${NC}"
}

# Run main function
main "$@"
