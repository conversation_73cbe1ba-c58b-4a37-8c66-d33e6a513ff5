events {
    worker_connections 1024;
}

http {
    upstream postgres_backend {
        server postgres-soloylibre:5432;
    }
    
    upstream mongodb_backend {
        server mongodb-soloylibre:27017;
    }
    
    upstream redis_backend {
        server redis-soloylibre:6379;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location / {
            return 200 '
<!DOCTYPE html>
<html>
<head>
    <title>SoloYLibre Platform</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .container { max-width: 800px; margin: 0 auto; text-align: center; }
        .service { background: rgba(255,255,255,0.1); margin: 10px; padding: 20px; border-radius: 10px; }
        .status { color: #4CAF50; font-weight: bold; }
        a { color: #FFD700; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 SoloYLibre Platform</h1>
        <h2>Unified Multi-App Platform</h2>
        <p><strong>Company:</strong> SoloYlibre & JEYKO</p>
        <p><strong>Head Developer:</strong> Jose L Encarnacion</p>
        
        <div class="service">
            <h3>📊 Monitoring Services</h3>
            <p><span class="status">✅ ACTIVE</span></p>
            <p><a href="http://localhost:3005" target="_blank">Grafana Dashboard</a> (admin/admin)</p>
            <p><a href="http://localhost:9092" target="_blank">Prometheus Metrics</a></p>
        </div>
        
        <div class="service">
            <h3>🗄️ Database Services</h3>
            <p><span class="status">✅ ACTIVE</span></p>
            <p>PostgreSQL: localhost:5434 (postgres/password)</p>
            <p>MongoDB: localhost:27018</p>
            <p>Redis: localhost:6381</p>
        </div>
        
        <div class="service">
            <h3>💾 Storage & AI</h3>
            <p><span class="status">✅ ACTIVE</span></p>
            <p><a href="http://localhost:9005" target="_blank">MinIO Console</a> (minioadmin/minioadmin)</p>
            <p>Ollama AI: localhost:11435</p>
            <p>Elasticsearch: localhost:9201</p>
        </div>
        
        <div class="service">
            <h3>📧 Email Testing</h3>
            <p><span class="status">✅ ACTIVE</span></p>
            <p><a href="http://localhost:8026" target="_blank">MailHog Interface</a></p>
        </div>
        
        <div class="service">
            <h3>🔑 Default Credentials</h3>
            <p><strong>Admin:</strong> <EMAIL> / joseluis</p>
            <p><strong>Grafana:</strong> admin / admin</p>
            <p><strong>MinIO:</strong> minioadmin / minioadmin</p>
            <p><strong>PostgreSQL:</strong> postgres / password</p>
        </div>
        
        <div class="service">
            <h3>🎨 20 Professional Themes Available</h3>
            <p>Social Media • Dating • E-commerce • Business • Creative</p>
            <p>Instagram • Twitter • Discord • LinkedIn • TikTok</p>
            <p>Tinder • Bumble • Hinge • Match • Coffee Meets Bagel</p>
            <p>Shopify • Amazon • Etsy • Stripe • WooCommerce</p>
            <p>Microsoft • Slack • Notion • Dribbble • Behance</p>
        </div>
        
        <p><strong>Platform Status:</strong> <span class="status">✅ OPERATIONAL</span></p>
        <p><em>Infrastructure services ready for application deployment</em></p>
    </div>
</body>
</html>
            ';
            add_header Content-Type text/html;
        }
        
        location /health {
            return 200 '{"status":"healthy","platform":"SoloYLibre","services":"operational"}';
            add_header Content-Type application/json;
        }
    }
}
