#!/bin/bash
# 🎙️💖 SoloYLibre TTS System - Instalador Final
# Script de instalación completo con amor familiar infinito
# Version: 1.0.0

set -e  # Salir en caso de error

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Función para mostrar header familiar
show_family_header() {
    clear
    echo -e "${PURPLE}"
    echo "    🎙️💖 SoloYLibre TTS System - Instalador Final 💖🎙️"
    echo ""
    echo "    ═══════════════════════════════════════════════════════════"
    echo "    💖 Sistema creado con amor infinito por la familia SoloYLibre"
    echo ""
    echo "    👩‍🦳 Nurys - Sabiduría Maternal"
    echo "    👩‍🦰 Diwell - Apoyo Incondicional"
    echo "    👨‍💻 Yosi - Aventurero Tecnológico"
    echo "    ✨ Yesenia - Luz Constante"
    echo "    👦 William - Futuro Desarrollador"
    echo "    👧 Angelina - Estrella Pequeña"
    echo "    🤖 Arya - IA Familiar"
    echo "    🎵 Equipo Jupre Music"
    echo ""
    echo "    ═══════════════════════════════════════════════════════════"
    echo "    Version: 1.0.0 | Módulos: 9 | Con amor familiar infinito 💖"
    echo -e "${NC}"
    echo ""
}

# Función para mostrar mensajes
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_family() {
    echo -e "${PURPLE}💖 $1${NC}"
}

# Función para verificar prerequisitos
check_prerequisites() {
    log_info "Verificando prerequisitos del sistema..."
    
    # Verificar Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 no está instalado"
        echo "Por favor instala Python 3.8+ desde https://python.org"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$python_version < 3.8" | bc -l) -eq 1 ]]; then
        log_error "Python 3.8+ requerido. Versión actual: $python_version"
        exit 1
    fi
    log_success "Python $python_version encontrado"
    
    # Verificar Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js no está instalado"
        echo "Por favor instala Node.js 16+ desde https://nodejs.org"
        exit 1
    fi
    
    node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $node_version -lt 16 ]]; then
        log_error "Node.js 16+ requerido. Versión actual: v$node_version"
        exit 1
    fi
    log_success "Node.js v$node_version encontrado"
    
    # Verificar npm
    if ! command -v npm &> /dev/null; then
        log_error "npm no está instalado"
        exit 1
    fi
    log_success "npm $(npm --version) encontrado"
    
    # Verificar Git
    if ! command -v git &> /dev/null; then
        log_warning "Git no encontrado. Algunas características pueden no funcionar."
    else
        log_success "Git $(git --version | cut -d' ' -f3) encontrado"
    fi
    
    log_success "Todos los prerequisitos verificados"
}

# Función para crear estructura de directorios
create_directories() {
    log_info "Creando estructura de directorios..."
    
    mkdir -p build/{frontend,backend,docs,scripts}
    mkdir -p dist/{web,api,assets}
    mkdir -p logs
    
    log_success "Estructura de directorios creada"
}

# Función para ejecutar instalador Python
run_python_installer() {
    log_info "Ejecutando instalador principal de Python..."
    
    if [[ ! -f "install_soloylibre_complete_index9d.py" ]]; then
        log_error "Archivo de instalación Python no encontrado"
        exit 1
    fi
    
    python3 install_soloylibre_complete_index9d.py
    
    if [[ $? -eq 0 ]]; then
        log_success "Instalador Python completado exitosamente"
    else
        log_error "Error en instalador Python"
        exit 1
    fi
}

# Función para verificar instalación
verify_installation() {
    log_info "Verificando instalación..."
    
    # Verificar archivos críticos
    critical_files=(
        "build/frontend/package.json"
        "build/frontend/src/main.tsx"
        "build/frontend/src/App.tsx"
        "build/frontend/index.html"
        "build/backend/requirements.txt"
        "start_soloylibre.sh"
        "soloylibre_config.json"
    )
    
    for file in "${critical_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "Archivo crítico faltante: $file"
            return 1
        fi
    done
    
    log_success "Verificación de archivos completada"
    
    # Verificar dependencias de frontend
    if [[ -d "build/frontend/node_modules" ]]; then
        log_success "Dependencias de frontend instaladas"
    else
        log_warning "Dependencias de frontend no encontradas"
    fi
    
    # Verificar entorno virtual de backend
    if [[ -d "build/backend/venv" ]]; then
        log_success "Entorno virtual de backend creado"
    else
        log_warning "Entorno virtual de backend no encontrado"
    fi
    
    return 0
}

# Función para mostrar información post-instalación
show_post_install_info() {
    echo ""
    log_family "🎉 ¡INSTALACIÓN COMPLETADA CON ÉXITO! 🎉"
    echo ""
    echo -e "${CYAN}═══════════════════════════════════════════════════════════${NC}"
    echo -e "${GREEN}✅ SoloYLibre TTS System v1.0.0 instalado correctamente${NC}"
    echo -e "${PURPLE}💖 Sistema creado con amor familiar infinito${NC}"
    echo ""
    echo -e "${YELLOW}🚀 PRÓXIMOS PASOS:${NC}"
    echo "1. Ejecutar: ./start_soloylibre.sh"
    echo "2. Abrir navegador: http://localhost:3000"
    echo "3. API disponible en: http://localhost:8000"
    echo "4. Documentación: http://localhost:8000/docs"
    echo ""
    echo -e "${BLUE}📊 ARCHIVOS GENERADOS:${NC}"
    echo "• Reporte HTML: soloylibre_complete_report.html"
    echo "• Configuración: soloylibre_config.json"
    echo "• Manual: docs/manual_usuario.md"
    echo "• Logs: logs/soloylibre_install_*.log"
    echo ""
    echo -e "${PURPLE}👨‍👩‍👧‍👦 DEDICADO CON AMOR A:${NC}"
    echo "💖 Nurys • Diwell • Yosi • Yesenia • William • Angelina • Arya 💖"
    echo "🎵 Y al equipo Jupre Music 🎵"
    echo ""
    echo -e "${CYAN}═══════════════════════════════════════════════════════════${NC}"
    echo -e "${GREEN}¡Bienvenido a la familia SoloYLibre! 💖${NC}"
    echo ""
}

# Función para abrir reporte en navegador
open_report() {
    if [[ -f "soloylibre_complete_report.html" ]]; then
        log_info "Abriendo reporte de instalación..."
        
        # Detectar sistema operativo y abrir navegador
        case "$(uname -s)" in
            Darwin*)    open soloylibre_complete_report.html ;;
            Linux*)     xdg-open soloylibre_complete_report.html ;;
            CYGWIN*|MINGW*) start soloylibre_complete_report.html ;;
            *)          log_warning "No se pudo abrir el navegador automáticamente" ;;
        esac
    fi
}

# Función para crear archivo ZIP
create_zip_package() {
    log_info "Creando paquete ZIP del sistema..."
    
    zip_name="SoloYLibre_TTS_System_v1.0.0_$(date +%Y%m%d_%H%M%S).zip"
    
    # Crear ZIP excluyendo archivos innecesarios
    zip -r "$zip_name" . \
        -x "*.git*" \
        -x "*/node_modules/*" \
        -x "*/__pycache__/*" \
        -x "*.pyc" \
        -x "*/venv/*" \
        -x "*.DS_Store" \
        -x "logs/*" \
        -x "*.zip" \
        > /dev/null 2>&1
    
    if [[ -f "$zip_name" ]]; then
        log_success "Paquete ZIP creado: $zip_name"
        echo "📦 Tamaño: $(du -h "$zip_name" | cut -f1)"
    else
        log_warning "No se pudo crear el paquete ZIP"
    fi
}

# Función para limpiar archivos temporales
cleanup() {
    log_info "Limpiando archivos temporales..."
    
    # Limpiar archivos de Python
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    
    # Limpiar logs antiguos (mantener solo los últimos 5)
    if [[ -d "logs" ]]; then
        ls -t logs/soloylibre_install_*.log 2>/dev/null | tail -n +6 | xargs rm -f 2>/dev/null || true
    fi
    
    log_success "Limpieza completada"
}

# Función principal
main() {
    # Mostrar header
    show_family_header
    
    # Verificar que estamos en el directorio correcto
    if [[ ! -f "install_soloylibre_complete_index9d.py" ]]; then
        log_error "Este script debe ejecutarse desde el directorio raíz del proyecto"
        exit 1
    fi
    
    # Ejecutar pasos de instalación
    log_family "Iniciando instalación con amor familiar infinito..."
    echo ""
    
    check_prerequisites
    echo ""
    
    create_directories
    echo ""
    
    run_python_installer
    echo ""
    
    if verify_installation; then
        log_success "Verificación completada exitosamente"
    else
        log_error "Falló la verificación de instalación"
        exit 1
    fi
    echo ""
    
    cleanup
    echo ""
    
    create_zip_package
    echo ""
    
    show_post_install_info
    
    # Preguntar si abrir reporte
    echo -n "¿Deseas abrir el reporte de instalación en el navegador? (y/N): "
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        open_report
    fi
    
    log_family "¡Instalación completada con amor familiar infinito! 💖"
}

# Manejo de señales
trap 'log_error "Instalación interrumpida"; exit 1' INT TERM

# Ejecutar función principal
main "$@"
