/**
 * 📊 SoloYLibre Real-time Metrics Dashboard - Index 4: Dashboard Completo
 * Dashboard de métricas en tiempo real con animaciones inspiradas en Jitter
 * 💖 Dedicado con amor a nuestra familia
 * Version: 1.0.0
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AnimatedChart, FamilyMetricsChart } from '../charts/animated_charts_index1';
import { AnimatedCard, AnimatedStats, ProgressRing, FloatingElement } from '../ui/animated_ui_elements_index2';

// ===== INTERFACES =====
export interface DashboardMetrics {
  overview: OverviewMetrics;
  tts: TTSMetrics;
  social: SocialMetrics;
  family: FamilyMetrics;
  system: SystemMetrics;
  realtime: RealtimeMetrics;
}

export interface OverviewMetrics {
  totalUsers: number;
  activeUsers: number;
  totalSynthesis: number;
  familyLove: number;
  revenue: number;
  growth: number;
}

export interface TTSMetrics {
  totalSynthesis: number;
  averageQuality: number;
  popularVoices: VoiceUsage[];
  processingTime: number;
  errorRate: number;
  dailyUsage: number[];
}

export interface SocialMetrics {
  totalPosts: number;
  platforms: PlatformMetrics[];
  engagement: number;
  reach: number;
  autoPostSuccess: number;
  cookieCompliance: number;
}

export interface FamilyMetrics {
  voiceUsage: Record<string, number>;
  favoriteVoice: string;
  familyInteractions: number;
  loveScore: number;
  memberActivity: MemberActivity[];
}

export interface SystemMetrics {
  uptime: number;
  performance: number;
  security: number;
  alerts: number;
  features: FeatureUsage[];
}

export interface RealtimeMetrics {
  currentUsers: number;
  activeSynthesis: number;
  systemLoad: number;
  responseTime: number;
  lastUpdate: Date;
}

export interface VoiceUsage {
  name: string;
  count: number;
  percentage: number;
  familyMember: string;
  color: string;
}

export interface PlatformMetrics {
  name: string;
  posts: number;
  engagement: number;
  reach: number;
  color: string;
}

export interface MemberActivity {
  member: string;
  activity: number;
  trend: 'up' | 'down' | 'stable';
  color: string;
}

export interface FeatureUsage {
  feature: string;
  usage: number;
  enabled: boolean;
  color: string;
}

// ===== DATOS MOCK =====
const generateMockMetrics = (): DashboardMetrics => ({
  overview: {
    totalUsers: 12547,
    activeUsers: 3421,
    totalSynthesis: 89234,
    familyLove: 98.5,
    revenue: 45678,
    growth: 23.4
  },
  tts: {
    totalSynthesis: 89234,
    averageQuality: 4.8,
    popularVoices: [
      { name: 'Nurys', count: 25430, percentage: 28.5, familyMember: 'Nurys', color: '#FF6B9D' },
      { name: 'Arya', count: 21234, percentage: 23.8, familyMember: 'Arya', color: '#B19CD9' },
      { name: 'Diwell', count: 18567, percentage: 20.8, familyMember: 'Diwell', color: '#4ECDC4' },
      { name: 'Yosi', count: 15432, percentage: 17.3, familyMember: 'Yosi', color: '#45B7D1' },
      { name: 'Angelina', count: 8571, percentage: 9.6, familyMember: 'Angelina', color: '#FFB6C1' }
    ],
    processingTime: 2.3,
    errorRate: 0.8,
    dailyUsage: [1200, 1450, 1320, 1680, 1890, 2100, 1950]
  },
  social: {
    totalPosts: 5678,
    platforms: [
      { name: 'Twitter', posts: 1234, engagement: 89, reach: 45000, color: '#1DA1F2' },
      { name: 'Facebook', posts: 987, engagement: 76, reach: 32000, color: '#4267B2' },
      { name: 'Instagram', posts: 876, engagement: 92, reach: 28000, color: '#E4405F' },
      { name: 'LinkedIn', posts: 543, engagement: 67, reach: 15000, color: '#0077B5' },
      { name: 'TikTok', posts: 432, engagement: 95, reach: 67000, color: '#FF0050' }
    ],
    engagement: 84.2,
    reach: 187000,
    autoPostSuccess: 96.7,
    cookieCompliance: 100
  },
  family: {
    voiceUsage: {
      nurys: 28.5,
      arya: 23.8,
      diwell: 20.8,
      yosi: 17.3,
      angelina: 9.6
    },
    favoriteVoice: 'Nurys',
    familyInteractions: 15678,
    loveScore: 98.5,
    memberActivity: [
      { member: 'Nurys', activity: 95, trend: 'up', color: '#FF6B9D' },
      { member: 'Arya', activity: 88, trend: 'stable', color: '#B19CD9' },
      { member: 'Diwell', activity: 92, trend: 'up', color: '#4ECDC4' },
      { member: 'Yosi', activity: 76, trend: 'down', color: '#45B7D1' }
    ]
  },
  system: {
    uptime: 99.9,
    performance: 94.2,
    security: 98.7,
    alerts: 2,
    features: [
      { feature: 'TTS Core', usage: 100, enabled: true, color: '#4CAF50' },
      { feature: 'Voice Cloning', usage: 67, enabled: true, color: '#FF9800' },
      { feature: 'Auto-Post', usage: 45, enabled: true, color: '#2196F3' },
      { feature: 'URL Encryption', usage: 23, enabled: false, color: '#9C27B0' }
    ]
  },
  realtime: {
    currentUsers: 234,
    activeSynthesis: 12,
    systemLoad: 34.5,
    responseTime: 1.2,
    lastUpdate: new Date()
  }
});

// ===== COMPONENTE PRINCIPAL =====
export const RealtimeMetricsDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<DashboardMetrics>(generateMockMetrics());
  const [selectedTimeRange, setSelectedTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  // ===== EFECTOS =====
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        updateMetrics();
      }, 5000); // Actualizar cada 5 segundos

      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // ===== FUNCIONES =====
  const updateMetrics = useCallback(() => {
    // Simular actualización de métricas en tiempo real
    setMetrics(prev => ({
      ...prev,
      realtime: {
        ...prev.realtime,
        currentUsers: prev.realtime.currentUsers + Math.floor(Math.random() * 10 - 5),
        activeSynthesis: Math.max(0, prev.realtime.activeSynthesis + Math.floor(Math.random() * 6 - 3)),
        systemLoad: Math.max(0, Math.min(100, prev.realtime.systemLoad + Math.random() * 10 - 5)),
        responseTime: Math.max(0.1, prev.realtime.responseTime + Math.random() * 0.4 - 0.2),
        lastUpdate: new Date()
      },
      overview: {
        ...prev.overview,
        activeUsers: Math.max(0, prev.overview.activeUsers + Math.floor(Math.random() * 20 - 10)),
        totalSynthesis: prev.overview.totalSynthesis + Math.floor(Math.random() * 5)
      }
    }));
    setLastUpdate(new Date());
  }, []);

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // ===== RENDER =====
  return (
    <div style={{
      padding: '20px',
      background: 'linear-gradient(135deg, rgba(255, 107, 157, 0.02) 0%, rgba(78, 205, 196, 0.02) 100%)',
      minHeight: '100vh'
    }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '30px',
          padding: '20px',
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          borderRadius: '16px',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}
      >
        <div>
          <h1 style={{
            margin: 0,
            fontSize: '2rem',
            fontWeight: '700',
            background: 'linear-gradient(45deg, #FF6B9D, #4ECDC4)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            📊 Dashboard SoloYLibre
          </h1>
          <p style={{
            margin: '4px 0 0 0',
            color: '#666',
            fontSize: '0.9rem'
          }}>
            Métricas familiares en tiempo real • Última actualización: {lastUpdate.toLocaleTimeString()}
          </p>
        </div>

        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
          {/* Time Range Selector */}
          <div style={{ display: 'flex', gap: '4px' }}>
            {(['1h', '24h', '7d', '30d'] as const).map((range) => (
              <motion.button
                key={range}
                style={{
                  padding: '8px 16px',
                  borderRadius: '8px',
                  border: 'none',
                  background: selectedTimeRange === range 
                    ? 'linear-gradient(135deg, #FF6B9D, #4ECDC4)'
                    : 'rgba(255, 255, 255, 0.1)',
                  color: selectedTimeRange === range ? 'white' : '#666',
                  fontSize: '0.8rem',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedTimeRange(range)}
              >
                {range}
              </motion.button>
            ))}
          </div>

          {/* Auto Refresh Toggle */}
          <motion.button
            style={{
              padding: '8px 16px',
              borderRadius: '8px',
              border: 'none',
              background: autoRefresh 
                ? 'linear-gradient(135deg, #4CAF50, #8BC34A)'
                : 'rgba(255, 255, 255, 0.1)',
              color: autoRefresh ? 'white' : '#666',
              fontSize: '0.8rem',
              fontWeight: '600',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '4px'
            }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <motion.span
              animate={{ rotate: autoRefresh ? 360 : 0 }}
              transition={{ duration: 1, repeat: autoRefresh ? Infinity : 0, ease: 'linear' }}
            >
              🔄
            </motion.span>
            Auto Refresh
          </motion.button>
        </div>
      </motion.div>

      {/* Overview Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }}
      >
        {[
          { 
            title: 'Usuarios Totales', 
            value: formatNumber(metrics.overview.totalUsers), 
            icon: '👥', 
            color: '#FF6B9D',
            trend: '+12%'
          },
          { 
            title: 'Usuarios Activos', 
            value: formatNumber(metrics.overview.activeUsers), 
            icon: '🟢', 
            color: '#4ECDC4',
            trend: '+8%'
          },
          { 
            title: 'Síntesis TTS', 
            value: formatNumber(metrics.overview.totalSynthesis), 
            icon: '🎤', 
            color: '#45B7D1',
            trend: '+23%'
          },
          { 
            title: 'Amor Familiar', 
            value: `${metrics.overview.familyLove}%`, 
            icon: '💖', 
            color: '#FFB6C1',
            trend: '+2%'
          },
          { 
            title: 'Ingresos', 
            value: formatCurrency(metrics.overview.revenue), 
            icon: '💰', 
            color: '#FFEAA7',
            trend: '+34%'
          },
          { 
            title: 'Crecimiento', 
            value: `${metrics.overview.growth}%`, 
            icon: '📈', 
            color: '#DDA0DD',
            trend: '+5%'
          }
        ].map((card, index) => (
          <AnimatedCard
            key={card.title}
            familyTheme={true}
            glassEffect={true}
            hoverEffect="lift"
          >
            <div style={{ textAlign: 'center' }}>
              <FloatingElement direction="up" duration={2 + index * 0.3} amplitude={3}>
                <div style={{ fontSize: '2.5rem', marginBottom: '8px' }}>
                  {card.icon}
                </div>
              </FloatingElement>
              
              <motion.div
                style={{
                  fontSize: '1.8rem',
                  fontWeight: '700',
                  color: card.color,
                  marginBottom: '4px'
                }}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2 + index * 0.1, type: 'spring' }}
              >
                {card.value}
              </motion.div>
              
              <div style={{
                fontSize: '0.9rem',
                color: '#666',
                fontWeight: '500',
                marginBottom: '4px'
              }}>
                {card.title}
              </div>
              
              <div style={{
                fontSize: '0.8rem',
                color: '#4CAF50',
                fontWeight: '600'
              }}>
                {card.trend}
              </div>
            </div>
          </AnimatedCard>
        ))}
      </motion.div>

      {/* Charts Section */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {/* Voice Usage Chart */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <AnimatedChart
            data={metrics.tts.popularVoices.map(voice => ({
              id: voice.name,
              label: voice.name,
              value: voice.count,
              color: voice.color,
              familyMember: voice.familyMember
            }))}
            type="pie"
            title="🎭 Uso de Voces Familiares"
            subtitle="Distribución de síntesis por voz"
            familyTheme={true}
            height={350}
            width={450}
          />
        </motion.div>

        {/* Social Media Performance */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <AnimatedChart
            data={metrics.social.platforms.map(platform => ({
              id: platform.name,
              label: platform.name,
              value: platform.engagement,
              color: platform.color,
              trend: 'up'
            }))}
            type="bar"
            title="📱 Rendimiento Social Media"
            subtitle="Engagement por plataforma"
            familyTheme={true}
            height={350}
            width={450}
          />
        </motion.div>
      </div>

      {/* Real-time Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <AnimatedCard
          title="⚡ Métricas en Tiempo Real"
          subtitle="Actualización automática cada 5 segundos"
          familyTheme={true}
          glassEffect={true}
        >
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
            gap: '20px',
            marginTop: '20px'
          }}>
            {[
              {
                label: 'Usuarios Conectados',
                value: metrics.realtime.currentUsers,
                icon: '🟢',
                color: '#4CAF50'
              },
              {
                label: 'Síntesis Activas',
                value: metrics.realtime.activeSynthesis,
                icon: '🎤',
                color: '#FF6B9D'
              },
              {
                label: 'Carga del Sistema',
                value: `${metrics.realtime.systemLoad.toFixed(1)}%`,
                icon: '⚡',
                color: '#FFC107'
              },
              {
                label: 'Tiempo Respuesta',
                value: `${metrics.realtime.responseTime.toFixed(1)}s`,
                icon: '⏱️',
                color: '#2196F3'
              }
            ].map((metric, index) => (
              <div key={metric.label} style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '1.5rem', marginBottom: '8px' }}>
                  {metric.icon}
                </div>
                <motion.div
                  style={{
                    fontSize: '1.5rem',
                    fontWeight: '700',
                    color: metric.color,
                    marginBottom: '4px'
                  }}
                  key={metric.value} // Re-animate when value changes
                  initial={{ scale: 1.2, opacity: 0.7 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {metric.value}
                </motion.div>
                <div style={{
                  fontSize: '0.8rem',
                  color: '#666',
                  fontWeight: '500'
                }}>
                  {metric.label}
                </div>
              </div>
            ))}
          </div>
        </AnimatedCard>
      </motion.div>

      {/* System Health */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        style={{ marginTop: '20px' }}
      >
        <AnimatedCard
          title="🛡️ Salud del Sistema"
          subtitle="Monitoreo continuo de componentes críticos"
          familyTheme={true}
          glassEffect={true}
        >
          <div style={{
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            marginTop: '20px',
            flexWrap: 'wrap',
            gap: '20px'
          }}>
            {[
              { label: 'Uptime', value: metrics.system.uptime, color: '#4CAF50' },
              { label: 'Performance', value: metrics.system.performance, color: '#2196F3' },
              { label: 'Security', value: metrics.system.security, color: '#FF9800' },
              { label: 'Family Love', value: metrics.family.loveScore, color: '#E91E63' }
            ].map((health, index) => (
              <div key={health.label} style={{ textAlign: 'center' }}>
                <ProgressRing
                  progress={health.value}
                  size={100}
                  strokeWidth={8}
                  color={health.color}
                  animated={true}
                  showPercentage={true}
                  familyMember={health.label}
                />
              </div>
            ))}
          </div>
        </AnimatedCard>
      </motion.div>
    </div>
  );
};

// Export por defecto
export default RealtimeMetricsDashboard;
