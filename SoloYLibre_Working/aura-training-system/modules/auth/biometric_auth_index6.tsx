/**
 * 🔐 SoloYLibre Biometric Authentication System - Index 6: Autenticación Biométrica
 * Sistema de autenticación con huella dactilar, 2FA, verificación email y tracking
 * 💖 Dedicado con amor a nuestra familia
 * Version: 1.0.0
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// ===== INTERFACES =====
export interface BiometricAuthConfig {
  enabled: boolean;
  fingerprintEnabled: boolean;
  faceIdEnabled: boolean;
  voiceEnabled: boolean;
  fallbackToPassword: boolean;
  maxAttempts: number;
  lockoutDuration: number; // minutos
}

export interface TwoFactorConfig {
  enabled: boolean;
  method: 'sms' | 'email' | 'authenticator' | 'biometric';
  backupCodes: string[];
  trustedDevices: TrustedDevice[];
}

export interface EmailVerificationConfig {
  enabled: boolean;
  requireVerification: boolean;
  verificationExpiry: number; // horas
  resendCooldown: number; // minutos
  maxResendAttempts: number;
}

export interface EmailTrackingConfig {
  enabled: boolean;
  trackOpens: boolean;
  trackClicks: boolean;
  trackLocation: boolean;
  retentionDays: number;
}

export interface TrustedDevice {
  id: string;
  name: string;
  fingerprint: string;
  lastUsed: Date;
  location: string;
  userAgent: string;
  trusted: boolean;
}

export interface AuthAttempt {
  id: string;
  userId: string;
  method: 'password' | 'biometric' | '2fa' | 'email';
  success: boolean;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  location?: string;
  failureReason?: string;
}

export interface BiometricData {
  type: 'fingerprint' | 'face' | 'voice';
  template: string; // Encrypted biometric template
  quality: number;
  createdAt: Date;
  lastUsed: Date;
  deviceId: string;
}

// ===== CONFIGURACIÓN POR DEFECTO =====
const DEFAULT_BIOMETRIC_CONFIG: BiometricAuthConfig = {
  enabled: true,
  fingerprintEnabled: true,
  faceIdEnabled: true,
  voiceEnabled: false,
  fallbackToPassword: true,
  maxAttempts: 3,
  lockoutDuration: 15
};

const DEFAULT_2FA_CONFIG: TwoFactorConfig = {
  enabled: true,
  method: 'authenticator',
  backupCodes: [],
  trustedDevices: []
};

const DEFAULT_EMAIL_CONFIG: EmailVerificationConfig = {
  enabled: true,
  requireVerification: true,
  verificationExpiry: 24,
  resendCooldown: 5,
  maxResendAttempts: 3
};

const DEFAULT_TRACKING_CONFIG: EmailTrackingConfig = {
  enabled: true,
  trackOpens: true,
  trackClicks: true,
  trackLocation: false,
  retentionDays: 90
};

// ===== COMPONENTE PRINCIPAL =====
export const BiometricAuthSystem: React.FC = () => {
  const [biometricConfig, setBiometricConfig] = useState<BiometricAuthConfig>(DEFAULT_BIOMETRIC_CONFIG);
  const [twoFactorConfig, setTwoFactorConfig] = useState<TwoFactorConfig>(DEFAULT_2FA_CONFIG);
  const [emailConfig, setEmailConfig] = useState<EmailVerificationConfig>(DEFAULT_EMAIL_CONFIG);
  const [trackingConfig, setTrackingConfig] = useState<EmailTrackingConfig>(DEFAULT_TRACKING_CONFIG);
  
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [authStep, setAuthStep] = useState<'biometric' | '2fa' | 'email' | 'complete'>('biometric');
  const [biometricSupported, setBiometricSupported] = useState(false);
  const [authAttempts, setAuthAttempts] = useState<AuthAttempt[]>([]);

  // ===== EFECTOS =====
  useEffect(() => {
    checkBiometricSupport();
    loadAuthConfig();
  }, []);

  // ===== FUNCIONES DE DETECCIÓN =====
  const checkBiometricSupport = useCallback(async () => {
    try {
      // Verificar soporte WebAuthn
      if (window.PublicKeyCredential) {
        const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
        setBiometricSupported(available);
        
        // Verificar tipos específicos de biometría
        if (available) {
          await detectBiometricTypes();
        }
      }
    } catch (error) {
      console.error('Error checking biometric support:', error);
      setBiometricSupported(false);
    }
  }, []);

  const detectBiometricTypes = async () => {
    try {
      // Detectar capacidades del dispositivo
      const capabilities = {
        fingerprint: false,
        faceId: false,
        voice: false
      };

      // Verificar si es dispositivo móvil
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      
      if (isMobile) {
        // En dispositivos móviles, asumir soporte de huella dactilar
        capabilities.fingerprint = true;
        
        // Detectar Face ID (principalmente iOS)
        if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
          capabilities.faceId = true;
        }
      }

      // Verificar micrófono para autenticación por voz
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          capabilities.voice = true;
          stream.getTracks().forEach(track => track.stop());
        } catch (error) {
          capabilities.voice = false;
        }
      }

      setBiometricConfig(prev => ({
        ...prev,
        fingerprintEnabled: capabilities.fingerprint,
        faceIdEnabled: capabilities.faceId,
        voiceEnabled: capabilities.voice
      }));

    } catch (error) {
      console.error('Error detecting biometric types:', error);
    }
  };

  // ===== AUTENTICACIÓN BIOMÉTRICA =====
  const authenticateWithBiometric = useCallback(async (type: 'fingerprint' | 'face' | 'voice') => {
    setIsAuthenticating(true);
    
    try {
      let result = false;
      
      switch (type) {
        case 'fingerprint':
          result = await authenticateFingerprint();
          break;
        case 'face':
          result = await authenticateFaceId();
          break;
        case 'voice':
          result = await authenticateVoice();
          break;
      }

      if (result) {
        logAuthAttempt('biometric', true);
        setAuthStep('2fa');
      } else {
        logAuthAttempt('biometric', false, 'Biometric authentication failed');
      }

    } catch (error) {
      console.error('Biometric authentication error:', error);
      logAuthAttempt('biometric', false, error.message);
    } finally {
      setIsAuthenticating(false);
    }
  }, []);

  const authenticateFingerprint = async (): Promise<boolean> => {
    try {
      // Usar WebAuthn para autenticación con huella dactilar
      const credential = await navigator.credentials.create({
        publicKey: {
          challenge: new Uint8Array(32),
          rp: {
            name: "SoloYLibre Family",
            id: window.location.hostname,
          },
          user: {
            id: new Uint8Array(16),
            name: "<EMAIL>",
            displayName: "SoloYLibre User",
          },
          pubKeyCredParams: [{alg: -7, type: "public-key"}],
          authenticatorSelection: {
            authenticatorAttachment: "platform",
            userVerification: "required"
          },
          timeout: 60000,
          attestation: "direct"
        }
      });

      return credential !== null;
    } catch (error) {
      console.error('Fingerprint authentication failed:', error);
      return false;
    }
  };

  const authenticateFaceId = async (): Promise<boolean> => {
    try {
      // Simular Face ID (en producción usar APIs nativas)
      return new Promise((resolve) => {
        setTimeout(() => {
          // Simular éxito/fallo basado en probabilidad
          resolve(Math.random() > 0.1); // 90% éxito
        }, 2000);
      });
    } catch (error) {
      console.error('Face ID authentication failed:', error);
      return false;
    }
  };

  const authenticateVoice = async (): Promise<boolean> => {
    try {
      // Implementar autenticación por voz
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      return new Promise((resolve) => {
        // Simular procesamiento de voz
        setTimeout(() => {
          stream.getTracks().forEach(track => track.stop());
          resolve(Math.random() > 0.2); // 80% éxito
        }, 3000);
      });
    } catch (error) {
      console.error('Voice authentication failed:', error);
      return false;
    }
  };

  // ===== AUTENTICACIÓN 2FA =====
  const verify2FA = useCallback(async (code: string) => {
    try {
      // Verificar código 2FA
      const isValid = await validate2FACode(code);
      
      if (isValid) {
        logAuthAttempt('2fa', true);
        setAuthStep('email');
      } else {
        logAuthAttempt('2fa', false, 'Invalid 2FA code');
      }
    } catch (error) {
      console.error('2FA verification error:', error);
      logAuthAttempt('2fa', false, error.message);
    }
  }, []);

  const validate2FACode = async (code: string): Promise<boolean> => {
    // Simular validación de código 2FA
    return new Promise((resolve) => {
      setTimeout(() => {
        // Código válido si tiene 6 dígitos
        resolve(code.length === 6 && /^\d+$/.test(code));
      }, 1000);
    });
  };

  // ===== VERIFICACIÓN DE EMAIL =====
  const sendVerificationEmail = useCallback(async (email: string) => {
    try {
      const verificationToken = generateVerificationToken();
      
      await fetch('/api/v1/auth/send-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email,
          token: verificationToken,
          tracking: trackingConfig.enabled
        })
      });

      return verificationToken;
    } catch (error) {
      console.error('Error sending verification email:', error);
      throw error;
    }
  }, [trackingConfig]);

  const verifyEmail = useCallback(async (token: string) => {
    try {
      const response = await fetch('/api/v1/auth/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      if (response.ok) {
        logAuthAttempt('email', true);
        setAuthStep('complete');
        return true;
      } else {
        logAuthAttempt('email', false, 'Invalid verification token');
        return false;
      }
    } catch (error) {
      console.error('Email verification error:', error);
      logAuthAttempt('email', false, error.message);
      return false;
    }
  }, []);

  // ===== FUNCIONES AUXILIARES =====
  const generateVerificationToken = (): string => {
    return Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  };

  const logAuthAttempt = (method: string, success: boolean, failureReason?: string) => {
    const attempt: AuthAttempt = {
      id: `attempt-${Date.now()}`,
      userId: 'current-user', // En producción obtener del contexto
      method: method as any,
      success,
      timestamp: new Date(),
      ipAddress: 'unknown', // En producción obtener IP real
      userAgent: navigator.userAgent,
      failureReason
    };

    setAuthAttempts(prev => [attempt, ...prev.slice(0, 9)]);
  };

  const loadAuthConfig = async () => {
    try {
      // Cargar configuración del usuario
      const response = await fetch('/api/v1/auth/config');
      if (response.ok) {
        const config = await response.json();
        setBiometricConfig(config.biometric || DEFAULT_BIOMETRIC_CONFIG);
        setTwoFactorConfig(config.twoFactor || DEFAULT_2FA_CONFIG);
        setEmailConfig(config.email || DEFAULT_EMAIL_CONFIG);
        setTrackingConfig(config.tracking || DEFAULT_TRACKING_CONFIG);
      }
    } catch (error) {
      console.error('Error loading auth config:', error);
    }
  };

  // ===== RENDER =====
  return (
    <div style={{
      padding: '20px',
      maxWidth: '500px',
      margin: '0 auto',
      background: 'linear-gradient(135deg, rgba(255, 107, 157, 0.05) 0%, rgba(78, 205, 196, 0.05) 100%)',
      borderRadius: '20px',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.2)'
    }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        style={{ textAlign: 'center', marginBottom: '30px' }}
      >
        <h2 style={{
          margin: '0 0 8px 0',
          fontSize: '1.8rem',
          fontWeight: '700',
          background: 'linear-gradient(45deg, #FF6B9D, #4ECDC4)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          🔐 Autenticación SoloYLibre
        </h2>
        <p style={{
          margin: 0,
          color: '#666',
          fontSize: '0.9rem'
        }}>
          Acceso seguro con amor familiar
        </p>
      </motion.div>

      {/* Authentication Steps */}
      <AnimatePresence mode="wait">
        {authStep === 'biometric' && (
          <BiometricAuthStep
            key="biometric"
            config={biometricConfig}
            supported={biometricSupported}
            isAuthenticating={isAuthenticating}
            onAuthenticate={authenticateWithBiometric}
          />
        )}

        {authStep === '2fa' && (
          <TwoFactorStep
            key="2fa"
            config={twoFactorConfig}
            onVerify={verify2FA}
          />
        )}

        {authStep === 'email' && (
          <EmailVerificationStep
            key="email"
            config={emailConfig}
            onSendEmail={sendVerificationEmail}
            onVerify={verifyEmail}
          />
        )}

        {authStep === 'complete' && (
          <AuthCompleteStep key="complete" />
        )}
      </AnimatePresence>

      {/* Auth Attempts Log */}
      {authAttempts.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          style={{
            marginTop: '30px',
            padding: '16px',
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '12px',
            backdropFilter: 'blur(5px)'
          }}
        >
          <h4 style={{
            margin: '0 0 12px 0',
            fontSize: '0.9rem',
            color: '#666'
          }}>
            📋 Intentos Recientes
          </h4>
          <div style={{ maxHeight: '150px', overflowY: 'auto' }}>
            {authAttempts.slice(0, 5).map((attempt) => (
              <div
                key={attempt.id}
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '6px 0',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                  fontSize: '0.8rem'
                }}
              >
                <span style={{
                  color: attempt.success ? '#4CAF50' : '#F44336'
                }}>
                  {attempt.success ? '✅' : '❌'} {attempt.method}
                </span>
                <span style={{ color: '#666' }}>
                  {attempt.timestamp.toLocaleTimeString()}
                </span>
              </div>
            ))}
          </div>
        </motion.div>
      )}
    </div>
  );
};

// ===== COMPONENTE PASO BIOMÉTRICO =====
const BiometricAuthStep: React.FC<{
  config: BiometricAuthConfig;
  supported: boolean;
  isAuthenticating: boolean;
  onAuthenticate: (type: 'fingerprint' | 'face' | 'voice') => void;
}> = ({ config, supported, isAuthenticating, onAuthenticate }) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      style={{ textAlign: 'center' }}
    >
      <h3 style={{
        margin: '0 0 20px 0',
        fontSize: '1.2rem',
        color: '#333'
      }}>
        🔒 Autenticación Biométrica
      </h3>

      {!supported ? (
        <div style={{
          padding: '20px',
          background: 'rgba(255, 193, 7, 0.1)',
          borderRadius: '12px',
          color: '#F57C00',
          marginBottom: '20px'
        }}>
          ⚠️ Autenticación biométrica no disponible en este dispositivo
        </div>
      ) : (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
          gap: '16px',
          marginBottom: '20px'
        }}>
          {config.fingerprintEnabled && (
            <BiometricButton
              type="fingerprint"
              icon="👆"
              label="Huella"
              isAuthenticating={isAuthenticating}
              onClick={() => onAuthenticate('fingerprint')}
            />
          )}

          {config.faceIdEnabled && (
            <BiometricButton
              type="face"
              icon="👤"
              label="Face ID"
              isAuthenticating={isAuthenticating}
              onClick={() => onAuthenticate('face')}
            />
          )}

          {config.voiceEnabled && (
            <BiometricButton
              type="voice"
              icon="🎤"
              label="Voz"
              isAuthenticating={isAuthenticating}
              onClick={() => onAuthenticate('voice')}
            />
          )}
        </div>
      )}

      {config.fallbackToPassword && (
        <button
          style={{
            padding: '12px 24px',
            background: 'transparent',
            border: '2px solid rgba(255, 255, 255, 0.3)',
            borderRadius: '8px',
            color: '#666',
            fontSize: '0.9rem',
            cursor: 'pointer'
          }}
        >
          🔑 Usar contraseña
        </button>
      )}
    </motion.div>
  );
};

// ===== BOTÓN BIOMÉTRICO =====
const BiometricButton: React.FC<{
  type: string;
  icon: string;
  label: string;
  isAuthenticating: boolean;
  onClick: () => void;
}> = ({ type, icon, label, isAuthenticating, onClick }) => {
  return (
    <motion.button
      style={{
        padding: '20px',
        background: 'linear-gradient(135deg, #FF6B9D, #4ECDC4)',
        border: 'none',
        borderRadius: '12px',
        color: 'white',
        cursor: 'pointer',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '8px'
      }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      disabled={isAuthenticating}
    >
      <motion.div
        style={{ fontSize: '2rem' }}
        animate={isAuthenticating ? {
          scale: [1, 1.2, 1],
          rotate: [0, 10, -10, 0]
        } : {}}
        transition={{ duration: 1, repeat: isAuthenticating ? Infinity : 0 }}
      >
        {icon}
      </motion.div>
      <span style={{ fontSize: '0.8rem', fontWeight: '600' }}>
        {label}
      </span>
    </motion.button>
  );
};

// ===== COMPONENTE 2FA =====
const TwoFactorStep: React.FC<{
  config: TwoFactorConfig;
  onVerify: (code: string) => void;
}> = ({ config, onVerify }) => {
  const [code, setCode] = useState('');

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      style={{ textAlign: 'center' }}
    >
      <h3 style={{
        margin: '0 0 20px 0',
        fontSize: '1.2rem',
        color: '#333'
      }}>
        🔐 Verificación en Dos Pasos
      </h3>

      <input
        type="text"
        value={code}
        onChange={(e) => setCode(e.target.value)}
        placeholder="Código de 6 dígitos"
        maxLength={6}
        style={{
          width: '100%',
          padding: '16px',
          fontSize: '1.2rem',
          textAlign: 'center',
          border: '2px solid rgba(255, 255, 255, 0.3)',
          borderRadius: '12px',
          background: 'rgba(255, 255, 255, 0.1)',
          color: '#333',
          marginBottom: '20px'
        }}
      />

      <button
        onClick={() => onVerify(code)}
        disabled={code.length !== 6}
        style={{
          width: '100%',
          padding: '16px',
          background: code.length === 6 
            ? 'linear-gradient(135deg, #FF6B9D, #4ECDC4)'
            : 'rgba(255, 255, 255, 0.1)',
          border: 'none',
          borderRadius: '12px',
          color: 'white',
          fontSize: '1rem',
          fontWeight: '600',
          cursor: code.length === 6 ? 'pointer' : 'not-allowed'
        }}
      >
        ✅ Verificar Código
      </button>
    </motion.div>
  );
};

// ===== COMPONENTE VERIFICACIÓN EMAIL =====
const EmailVerificationStep: React.FC<{
  config: EmailVerificationConfig;
  onSendEmail: (email: string) => Promise<string>;
  onVerify: (token: string) => Promise<boolean>;
}> = ({ config, onSendEmail, onVerify }) => {
  const [email, setEmail] = useState('');
  const [token, setToken] = useState('');
  const [emailSent, setEmailSent] = useState(false);

  const handleSendEmail = async () => {
    try {
      await onSendEmail(email);
      setEmailSent(true);
    } catch (error) {
      console.error('Error sending email:', error);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      style={{ textAlign: 'center' }}
    >
      <h3 style={{
        margin: '0 0 20px 0',
        fontSize: '1.2rem',
        color: '#333'
      }}>
        📧 Verificación de Email
      </h3>

      {!emailSent ? (
        <>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            style={{
              width: '100%',
              padding: '16px',
              fontSize: '1rem',
              border: '2px solid rgba(255, 255, 255, 0.3)',
              borderRadius: '12px',
              background: 'rgba(255, 255, 255, 0.1)',
              color: '#333',
              marginBottom: '20px'
            }}
          />

          <button
            onClick={handleSendEmail}
            disabled={!email}
            style={{
              width: '100%',
              padding: '16px',
              background: email 
                ? 'linear-gradient(135deg, #FF6B9D, #4ECDC4)'
                : 'rgba(255, 255, 255, 0.1)',
              border: 'none',
              borderRadius: '12px',
              color: 'white',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: email ? 'pointer' : 'not-allowed'
            }}
          >
            📤 Enviar Código
          </button>
        </>
      ) : (
        <>
          <div style={{
            padding: '16px',
            background: 'rgba(76, 175, 80, 0.1)',
            borderRadius: '12px',
            color: '#4CAF50',
            marginBottom: '20px'
          }}>
            ✅ Email enviado a {email}
          </div>

          <input
            type="text"
            value={token}
            onChange={(e) => setToken(e.target.value)}
            placeholder="Código de verificación"
            style={{
              width: '100%',
              padding: '16px',
              fontSize: '1rem',
              border: '2px solid rgba(255, 255, 255, 0.3)',
              borderRadius: '12px',
              background: 'rgba(255, 255, 255, 0.1)',
              color: '#333',
              marginBottom: '20px'
            }}
          />

          <button
            onClick={() => onVerify(token)}
            disabled={!token}
            style={{
              width: '100%',
              padding: '16px',
              background: token 
                ? 'linear-gradient(135deg, #FF6B9D, #4ECDC4)'
                : 'rgba(255, 255, 255, 0.1)',
              border: 'none',
              borderRadius: '12px',
              color: 'white',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: token ? 'pointer' : 'not-allowed'
            }}
          >
            ✅ Verificar Email
          </button>
        </>
      )}
    </motion.div>
  );
};

// ===== COMPONENTE AUTENTICACIÓN COMPLETA =====
const AuthCompleteStep: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      style={{ textAlign: 'center' }}
    >
      <motion.div
        style={{ fontSize: '4rem', marginBottom: '20px' }}
        animate={{
          scale: [1, 1.2, 1],
          rotate: [0, 10, -10, 0]
        }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        🎉
      </motion.div>

      <h3 style={{
        margin: '0 0 12px 0',
        fontSize: '1.5rem',
        color: '#4CAF50'
      }}>
        ¡Autenticación Exitosa!
      </h3>

      <p style={{
        margin: 0,
        color: '#666',
        fontSize: '0.9rem'
      }}>
        Bienvenido a la familia SoloYLibre 💖
      </p>
    </motion.div>
  );
};

// Export por defecto
export default BiometricAuthSystem;
