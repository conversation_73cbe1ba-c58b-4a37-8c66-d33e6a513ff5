/**
 * 💬 SoloYLibre Local Chat - Chat Local con Llamadas VoIP/Video
 * Sistema completo de chat local con capacidades de llamadas integradas
 * 💖 Dedicado con amor a nuestra familia
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Interfaces
interface ChatMessage {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'voice' | 'video' | 'call' | 'system';
  timestamp: Date;
  isRead: boolean;
  replyTo?: string;
  reactions: MessageReaction[];
  attachments: MessageAttachment[];
  callData?: CallData;
}

interface MessageReaction {
  emoji: string;
  userId: string;
  timestamp: Date;
}

interface MessageAttachment {
  id: string;
  name: string;
  url: string;
  type: 'image' | 'video' | 'audio' | 'document';
  size: number;
  thumbnail?: string;
}

interface CallData {
  type: 'voice' | 'video';
  duration: number;
  status: 'missed' | 'answered' | 'declined' | 'busy';
  quality: 'poor' | 'good' | 'excellent';
}

interface ChatRoom {
  id: string;
  name: string;
  participants: string[];
  lastMessage?: ChatMessage;
  unreadCount: number;
  isGroup: boolean;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: Date;
  isTyping: boolean;
}

interface CallState {
  isActive: boolean;
  type: 'voice' | 'video';
  contactId: string;
  contactName: string;
  status: 'connecting' | 'ringing' | 'connected' | 'ended';
  duration: number;
  isMuted: boolean;
  isVideoEnabled: boolean;
  quality: 'poor' | 'good' | 'excellent';
}

// Componente principal del Chat Local
const LocalChat: React.FC<{
  contacts: any[];
  isCallActive: boolean;
  onCallStateChange: (isActive: boolean) => void;
}> = ({ contacts, isCallActive, onCallStateChange }) => {
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [selectedRoom, setSelectedRoom] = useState<ChatRoom | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [callState, setCallState] = useState<CallState | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
  const localStreamRef = useRef<MediaStream | null>(null);

  // Scroll automático a último mensaje
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Inicializar chat rooms desde contactos
  useEffect(() => {
    const rooms: ChatRoom[] = contacts.map(contact => ({
      id: contact.id,
      name: contact.displayName,
      participants: [contact.id, 'current-user'],
      unreadCount: Math.floor(Math.random() * 5),
      isGroup: false,
      avatar: contact.avatar,
      isOnline: Math.random() > 0.5,
      lastSeen: new Date(Date.now() - Math.random() * 86400000),
      isTyping: false
    }));
    setChatRooms(rooms);
  }, [contacts]);

  // Filtrar chat rooms
  const filteredRooms = chatRooms.filter(room =>
    room.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="local-chat">
      <div className="chat-container">
        {/* Sidebar de conversaciones */}
        <div className="chat-sidebar">
          <div className="chat-header">
            <h3>💬 Chat Local</h3>
            <div className="chat-actions">
              <button className="new-chat-btn">➕</button>
              <button className="settings-btn">⚙️</button>
            </div>
          </div>
          
          <div className="chat-search">
            <input
              type="text"
              placeholder="Buscar conversaciones..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="chat-rooms-list">
            {filteredRooms.map(room => (
              <ChatRoomItem
                key={room.id}
                room={room}
                isSelected={selectedRoom?.id === room.id}
                onSelect={() => setSelectedRoom(room)}
              />
            ))}
          </div>
        </div>

        {/* Área principal del chat */}
        <div className="chat-main">
          {selectedRoom ? (
            <>
              <ChatHeader
                room={selectedRoom}
                onStartCall={(type) => startCall(selectedRoom, type)}
                onVideoCall={() => startCall(selectedRoom, 'video')}
              />
              
              <ChatMessages
                messages={messages}
                currentUserId="current-user"
                onReaction={(messageId, emoji) => addReaction(messageId, emoji)}
              />
              
              <ChatInput
                value={newMessage}
                onChange={setNewMessage}
                onSend={sendMessage}
                onTyping={setIsTyping}
                isTyping={isTyping}
              />
            </>
          ) : (
            <div className="no-chat-selected">
              <span className="no-chat-icon">💬</span>
              <h3>Selecciona una conversación</h3>
              <p>Elige un contacto para comenzar a chatear</p>
            </div>
          )}
        </div>
      </div>

      {/* Overlay de llamada */}
      <AnimatePresence>
        {callState && (
          <CallOverlay
            callState={callState}
            onEndCall={endCall}
            onToggleMute={toggleMute}
            onToggleVideo={toggleVideo}
            localVideoRef={localVideoRef}
            remoteVideoRef={remoteVideoRef}
          />
        )}
      </AnimatePresence>
    </div>
  );

  // Funciones de chat
  function sendMessage() {
    if (!newMessage.trim() || !selectedRoom) return;

    const message: ChatMessage = {
      id: Date.now().toString(),
      senderId: 'current-user',
      receiverId: selectedRoom.participants.find(p => p !== 'current-user') || '',
      content: newMessage,
      type: 'text',
      timestamp: new Date(),
      isRead: false,
      reactions: [],
      attachments: []
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');
  }

  function addReaction(messageId: string, emoji: string) {
    setMessages(prev => prev.map(msg => {
      if (msg.id === messageId) {
        const existingReaction = msg.reactions.find(r => r.userId === 'current-user');
        if (existingReaction) {
          return {
            ...msg,
            reactions: msg.reactions.map(r => 
              r.userId === 'current-user' ? { ...r, emoji } : r
            )
          };
        } else {
          return {
            ...msg,
            reactions: [...msg.reactions, {
              emoji,
              userId: 'current-user',
              timestamp: new Date()
            }]
          };
        }
      }
      return msg;
    }));
  }

  // Funciones de llamadas
  async function startCall(room: ChatRoom, type: 'voice' | 'video') {
    try {
      const constraints = {
        audio: true,
        video: type === 'video'
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      localStreamRef.current = stream;

      if (localVideoRef.current && type === 'video') {
        localVideoRef.current.srcObject = stream;
      }

      const newCallState: CallState = {
        isActive: true,
        type,
        contactId: room.id,
        contactName: room.name,
        status: 'connecting',
        duration: 0,
        isMuted: false,
        isVideoEnabled: type === 'video',
        quality: 'good'
      };

      setCallState(newCallState);
      onCallStateChange(true);

      // Simular conexión
      setTimeout(() => {
        setCallState(prev => prev ? { ...prev, status: 'connected' } : null);
        startCallTimer();
      }, 2000);

    } catch (error) {
      console.error('Error starting call:', error);
      alert('Error al iniciar la llamada. Verifica los permisos de micrófono/cámara.');
    }
  }

  function endCall() {
    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach(track => track.stop());
    }

    if (peerConnectionRef.current) {
      peerConnectionRef.current.close();
    }

    setCallState(null);
    onCallStateChange(false);
  }

  function toggleMute() {
    if (localStreamRef.current) {
      const audioTrack = localStreamRef.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setCallState(prev => prev ? { ...prev, isMuted: !audioTrack.enabled } : null);
      }
    }
  }

  function toggleVideo() {
    if (localStreamRef.current) {
      const videoTrack = localStreamRef.current.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setCallState(prev => prev ? { ...prev, isVideoEnabled: videoTrack.enabled } : null);
      }
    }
  }

  function startCallTimer() {
    const interval = setInterval(() => {
      setCallState(prev => {
        if (!prev || prev.status !== 'connected') {
          clearInterval(interval);
          return prev;
        }
        return { ...prev, duration: prev.duration + 1 };
      });
    }, 1000);
  }
};

// Componente de item de chat room
const ChatRoomItem: React.FC<{
  room: ChatRoom;
  isSelected: boolean;
  onSelect: () => void;
}> = ({ room, isSelected, onSelect }) => {
  
  const formatLastSeen = (date?: Date) => {
    if (!date) return '';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'Ahora';
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    return `${days}d`;
  };

  return (
    <motion.div
      className={`chat-room-item ${isSelected ? 'selected' : ''}`}
      onClick={onSelect}
      whileHover={{ x: 4 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="room-avatar">
        {room.avatar ? (
          <img src={room.avatar} alt={room.name} />
        ) : (
          <div className="avatar-placeholder">
            {room.name.charAt(0).toUpperCase()}
          </div>
        )}
        
        <div className={`online-indicator ${room.isOnline ? 'online' : 'offline'}`} />
      </div>
      
      <div className="room-info">
        <div className="room-header">
          <h4 className="room-name">{room.name}</h4>
          <span className="last-seen">
            {room.isOnline ? '🟢' : formatLastSeen(room.lastSeen)}
          </span>
        </div>
        
        <div className="room-preview">
          {room.isTyping ? (
            <span className="typing-indicator">✍️ Escribiendo...</span>
          ) : room.lastMessage ? (
            <span className="last-message">{room.lastMessage.content}</span>
          ) : (
            <span className="no-messages">Sin mensajes</span>
          )}
        </div>
      </div>
      
      {room.unreadCount > 0 && (
        <div className="unread-badge">{room.unreadCount}</div>
      )}
    </motion.div>
  );
};

// Header del chat
const ChatHeader: React.FC<{
  room: ChatRoom;
  onStartCall: (type: 'voice' | 'video') => void;
  onVideoCall: () => void;
}> = ({ room, onStartCall, onVideoCall }) => {
  
  return (
    <div className="chat-header-main">
      <div className="contact-info">
        <div className="contact-avatar">
          {room.avatar ? (
            <img src={room.avatar} alt={room.name} />
          ) : (
            <div className="avatar-placeholder">
              {room.name.charAt(0).toUpperCase()}
            </div>
          )}
          <div className={`online-indicator ${room.isOnline ? 'online' : 'offline'}`} />
        </div>
        
        <div className="contact-details">
          <h3>{room.name}</h3>
          <p className="status">
            {room.isTyping ? '✍️ Escribiendo...' : 
             room.isOnline ? '🟢 En línea' : 
             `Última vez: ${room.lastSeen?.toLocaleTimeString()}`}
          </p>
        </div>
      </div>
      
      <div className="chat-actions">
        <button 
          className="call-btn voice"
          onClick={() => onStartCall('voice')}
          title="Llamada de voz"
        >
          📞
        </button>
        <button 
          className="call-btn video"
          onClick={() => onStartCall('video')}
          title="Videollamada"
        >
          📹
        </button>
        <button className="info-btn" title="Información">
          ℹ️
        </button>
      </div>
    </div>
  );
};

// Mensajes del chat
const ChatMessages: React.FC<{
  messages: ChatMessage[];
  currentUserId: string;
  onReaction: (messageId: string, emoji: string) => void;
}> = ({ messages, currentUserId, onReaction }) => {
  
  return (
    <div className="chat-messages">
      {messages.map(message => (
        <ChatMessageItem
          key={message.id}
          message={message}
          isOwn={message.senderId === currentUserId}
          onReaction={onReaction}
        />
      ))}
      <div ref={messagesEndRef} />
    </div>
  );
};

// Item de mensaje
const ChatMessageItem: React.FC<{
  message: ChatMessage;
  isOwn: boolean;
  onReaction: (messageId: string, emoji: string) => void;
}> = ({ message, isOwn, onReaction }) => {
  const [showReactions, setShowReactions] = useState(false);
  
  const reactionEmojis = ['👍', '❤️', '😂', '😮', '😢', '😡'];
  
  return (
    <motion.div
      className={`message-item ${isOwn ? 'own' : 'other'}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="message-content">
        <div className="message-bubble">
          {message.type === 'text' && (
            <p>{message.content}</p>
          )}
          
          {message.type === 'call' && message.callData && (
            <div className="call-message">
              <span className="call-icon">
                {message.callData.type === 'video' ? '📹' : '📞'}
              </span>
              <span className="call-info">
                {message.callData.status === 'missed' ? 'Llamada perdida' :
                 message.callData.status === 'answered' ? `Llamada ${message.callData.duration}s` :
                 'Llamada no contestada'}
              </span>
            </div>
          )}
          
          <div className="message-time">
            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
        
        <button 
          className="reaction-btn"
          onClick={() => setShowReactions(!showReactions)}
        >
          😊
        </button>
        
        {showReactions && (
          <div className="reaction-picker">
            {reactionEmojis.map(emoji => (
              <button
                key={emoji}
                onClick={() => {
                  onReaction(message.id, emoji);
                  setShowReactions(false);
                }}
              >
                {emoji}
              </button>
            ))}
          </div>
        )}
      </div>
      
      {message.reactions.length > 0 && (
        <div className="message-reactions">
          {message.reactions.map((reaction, index) => (
            <span key={index} className="reaction">
              {reaction.emoji}
            </span>
          ))}
        </div>
      )}
    </motion.div>
  );
};

// Input del chat
const ChatInput: React.FC<{
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onTyping: (isTyping: boolean) => void;
  isTyping: boolean;
}> = ({ value, onChange, onSend, onTyping, isTyping }) => {
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSend();
    }
  };
  
  const emojis = ['😀', '😂', '❤️', '👍', '👎', '😮', '😢', '😡', '🎉', '🔥'];
  
  return (
    <div className="chat-input-container">
      <div className="input-actions">
        <button className="attach-btn">📎</button>
        <button 
          className="emoji-btn"
          onClick={() => setShowEmojiPicker(!showEmojiPicker)}
        >
          😊
        </button>
      </div>
      
      <div className="input-field">
        <textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Escribe un mensaje..."
          rows={1}
          className="message-input"
        />
        
        {showEmojiPicker && (
          <div className="emoji-picker">
            {emojis.map(emoji => (
              <button
                key={emoji}
                onClick={() => {
                  onChange(value + emoji);
                  setShowEmojiPicker(false);
                }}
              >
                {emoji}
              </button>
            ))}
          </div>
        )}
      </div>
      
      <button 
        className="send-btn"
        onClick={onSend}
        disabled={!value.trim()}
      >
        📤
      </button>
    </div>
  );
};

// Overlay de llamada
const CallOverlay: React.FC<{
  callState: CallState;
  onEndCall: () => void;
  onToggleMute: () => void;
  onToggleVideo: () => void;
  localVideoRef: React.RefObject<HTMLVideoElement>;
  remoteVideoRef: React.RefObject<HTMLVideoElement>;
}> = ({ callState, onEndCall, onToggleMute, onToggleVideo, localVideoRef, remoteVideoRef }) => {
  
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <motion.div
      className="call-overlay"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <div className="call-container">
        <div className="call-header">
          <h3>{callState.contactName}</h3>
          <p className="call-status">
            {callState.status === 'connecting' ? 'Conectando...' :
             callState.status === 'ringing' ? 'Llamando...' :
             callState.status === 'connected' ? formatDuration(callState.duration) :
             'Llamada terminada'}
          </p>
        </div>
        
        {callState.type === 'video' && (
          <div className="video-container">
            <video
              ref={remoteVideoRef}
              className="remote-video"
              autoPlay
              playsInline
            />
            <video
              ref={localVideoRef}
              className="local-video"
              autoPlay
              playsInline
              muted
            />
          </div>
        )}
        
        <div className="call-controls">
          <button
            className={`control-btn mute ${callState.isMuted ? 'active' : ''}`}
            onClick={onToggleMute}
          >
            {callState.isMuted ? '🔇' : '🔊'}
          </button>
          
          {callState.type === 'video' && (
            <button
              className={`control-btn video ${!callState.isVideoEnabled ? 'active' : ''}`}
              onClick={onToggleVideo}
            >
              {callState.isVideoEnabled ? '📹' : '📷'}
            </button>
          )}
          
          <button
            className="control-btn end-call"
            onClick={onEndCall}
          >
            📞
          </button>
        </div>
        
        <div className="call-quality">
          <span className={`quality-indicator ${callState.quality}`}>
            {callState.quality === 'excellent' ? '📶' :
             callState.quality === 'good' ? '📶' : '📶'}
          </span>
          <span>Calidad: {callState.quality}</span>
        </div>
      </div>
    </motion.div>
  );
};

export default LocalChat;
