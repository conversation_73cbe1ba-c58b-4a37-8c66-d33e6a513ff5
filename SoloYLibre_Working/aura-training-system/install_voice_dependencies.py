#!/usr/bin/env python3
"""
📦 Install Voice Dependencies - Instalador de Dependencias de Voz
Instala automáticamente todas las dependencias necesarias para el sistema de voz
💖 Desarrollado bajo la dirección de JoseTusabe - Head of Development Team
"""

import subprocess
import sys
import platform
import os

def install_package(package_name):
    """Instalar paquete de Python"""
    try:
        print(f"📦 Instalando {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} instalado exitosamente")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando {package_name}: {e}")
        return False

def install_system_dependencies():
    """Instalar dependencias del sistema según la plataforma"""
    system = platform.system().lower()
    
    print(f"🖥️ Sistema detectado: {system}")
    
    if system == "darwin":  # macOS
        print("🍎 Instalando dependencias para macOS...")
        try:
            # Verificar si Homebrew está instalado
            subprocess.run(["brew", "--version"], check=True, capture_output=True)
            print("✅ Homebrew detectado")
            
            # Instalar PortAudio para PyAudio
            print("📦 Instalando PortAudio...")
            subprocess.run(["brew", "install", "portaudio"], check=True)
            print("✅ PortAudio instalado")
            
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️ Homebrew no encontrado. Instalando manualmente...")
            print("💡 Instala Homebrew desde: https://brew.sh")
            print("💡 Luego ejecuta: brew install portaudio")
    
    elif system == "linux":
        print("🐧 Instalando dependencias para Linux...")
        try:
            # Detectar distribución
            with open("/etc/os-release", "r") as f:
                os_info = f.read().lower()
            
            if "ubuntu" in os_info or "debian" in os_info:
                print("📦 Instalando para Ubuntu/Debian...")
                subprocess.run(["sudo", "apt-get", "update"], check=True)
                subprocess.run(["sudo", "apt-get", "install", "-y", "portaudio19-dev", "python3-pyaudio"], check=True)
            elif "fedora" in os_info or "centos" in os_info or "rhel" in os_info:
                print("📦 Instalando para Fedora/CentOS/RHEL...")
                subprocess.run(["sudo", "dnf", "install", "-y", "portaudio-devel"], check=True)
            else:
                print("⚠️ Distribución no reconocida. Instala portaudio manualmente.")
                
        except Exception as e:
            print(f"❌ Error instalando dependencias del sistema: {e}")
    
    elif system == "windows":
        print("🪟 Sistema Windows detectado")
        print("💡 Las dependencias se instalarán automáticamente con pip")

def main():
    """Función principal de instalación"""
    print("📦🎤 INSTALADOR DE DEPENDENCIAS DE VOZ PARA JEYKO 🎤📦")
    print("═══════════════════════════════════════════════════════════")
    print("💖 Desarrollado bajo la dirección de JoseTusabe - Head of Development Team")
    print()
    
    # Lista de dependencias de Python
    python_dependencies = [
        "SpeechRecognition",
        "pyttsx3",
        "pyaudio",
        "requests"
    ]
    
    # Instalar dependencias del sistema
    print("🔧 Instalando dependencias del sistema...")
    install_system_dependencies()
    print()
    
    # Instalar dependencias de Python
    print("🐍 Instalando dependencias de Python...")
    success_count = 0
    
    for package in python_dependencies:
        if install_package(package):
            success_count += 1
        print()
    
    # Resumen de instalación
    print("📊 RESUMEN DE INSTALACIÓN:")
    print("═══════════════════════════════════════════════════════════")
    print(f"✅ Paquetes instalados exitosamente: {success_count}/{len(python_dependencies)}")
    
    if success_count == len(python_dependencies):
        print("🎉 ¡Todas las dependencias instaladas correctamente!")
        print()
        print("🚀 PRÓXIMOS PASOS:")
        print("   1. Conecta tu micrófono")
        print("   2. Ejecuta: python3 jeyko_voice_system.py")
        print("   3. O ejecuta: python3 jeyko_voice_chat_complete.py")
        print()
        print("🎤 ¡JEYKO está listo para escucharte, Jose!")
    else:
        print("⚠️ Algunas dependencias no se pudieron instalar")
        print("💡 Intenta instalar manualmente:")
        for package in python_dependencies:
            print(f"   pip3 install {package}")
    
    print()
    print("🔧 SOLUCIÓN DE PROBLEMAS:")
    print("═══════════════════════════════════════════════════════════")
    print("❌ Si PyAudio falla en macOS:")
    print("   brew install portaudio")
    print("   pip3 install pyaudio")
    print()
    print("❌ Si hay problemas de permisos:")
    print("   • Habilita permisos de micrófono en Configuración del Sistema")
    print("   • En macOS: Configuración > Seguridad > Privacidad > Micrófono")
    print()
    print("❌ Si el reconocimiento no funciona:")
    print("   • Verifica conexión a internet (usa Google Speech API)")
    print("   • Prueba con diferentes micrófonos")
    print("   • Habla claramente y en español")

if __name__ == "__main__":
    main()
