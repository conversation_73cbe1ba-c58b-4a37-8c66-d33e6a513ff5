#!/usr/bin/env python3
"""
📦 SoloYLibre Final Packager v1.1 - Index 2G: Empaquetador Final v1.1.0
Empaquetador completo para la versión 1.1.0 con nuevas características
💖 Dedicado con amor a nuestra familia
Version: 1.1.0
"""

import os
import sys
import json
import zipfile
import shutil
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SoloYLibrePackagerV11:
    """Empaquetador final para SoloYLibre v1.1.0"""
    
    def __init__(self):
        self.version = "1.1.0"
        self.build_date = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.base_path = Path.cwd()
        self.dist_path = self.base_path / "dist"
        self.family_love_level = 100.0
        
        # Crear directorio de distribución
        self.dist_path.mkdir(exist_ok=True)
        
        # Configuración de paquetes
        self.package_configs = self._load_package_configs()
        
    def _load_package_configs(self) -> Dict:
        """Cargar configuraciones de paquetes"""
        return {
            "complete": {
                "name": "SoloYLibre_TTS_System_Complete",
                "description": "Sistema completo con todas las características v1.1.0",
                "includes": [
                    # Sistema principal
                    "demo_users_system_index1a.py",
                    "production_cleanup_index1b.py",
                    "super_admin_panel_index1c.tsx",
                    
                    # Instaladores modulares
                    "modular_installer_index2a.py",
                    "auto_installer_wizard_index2b.py",
                    
                    # Encriptación y seguridad
                    "asset_encryption_index3a.py",
                    
                    # Componentes UI actualizados
                    "realtime_metrics_index4.tsx",
                    "biometric_complete_index8.tsx",
                    "modular_subscription_index7.tsx",
                    "subscription_components_index7b.tsx",
                    "subscription_provider_index7c.tsx",
                    
                    # Slider de ventas v1.1
                    "soloylibre_sales_slider_v11.html",
                    
                    # Documentación actualizada
                    "documentation/",
                    
                    # Scripts de instalación
                    "install_complete_system.sh",
                    "install_complete_system.bat"
                ],
                "size_estimate_mb": 350,
                "family_love": True
            },
            
            "minimal": {
                "name": "SoloYLibre_TTS_System_Minimal",
                "description": "Instalación mínima solo con componentes esenciales",
                "includes": [
                    "demo_users_system_index1a.py",
                    "modular_installer_index2a.py",
                    "auto_installer_wizard_index2b.py",
                    "soloylibre_sales_slider_v11.html",
                    "install_complete_system.sh",
                    "install_complete_system.bat"
                ],
                "size_estimate_mb": 50,
                "family_love": True
            },
            
            "developer": {
                "name": "SoloYLibre_TTS_System_Developer",
                "description": "Paquete para desarrolladores con herramientas demo",
                "includes": [
                    # Todo lo del completo
                    "*",
                    # Herramientas adicionales de desarrollo
                    "system_verification_index3a.py",
                    "final_packager_v11_index2g.py"
                ],
                "size_estimate_mb": 400,
                "family_love": True,
                "development_tools": True
            },
            
            "enterprise": {
                "name": "SoloYLibre_TTS_System_Enterprise",
                "description": "Paquete enterprise con características avanzadas",
                "includes": [
                    # Todo lo del completo
                    "*",
                    # Características enterprise adicionales
                    "enterprise_config.json",
                    "deployment_scripts/",
                    "monitoring_tools/"
                ],
                "size_estimate_mb": 500,
                "family_love": True,
                "enterprise_features": True
            }
        }
    
    def create_all_packages(self) -> Dict[str, str]:
        """Crear todos los paquetes de distribución"""
        print("📦💖 SoloYLibre Final Packager v1.1.0 💖📦")
        print("═══════════════════════════════════════════════════════════")
        print("🚀 Creando paquetes de distribución v1.1.0")
        print("💖 Con amor familiar infinito")
        print("═══════════════════════════════════════════════════════════")
        
        created_packages = {}
        
        for package_type, config in self.package_configs.items():
            print(f"\n📦 Creando paquete: {config['name']}")
            print(f"📝 Descripción: {config['description']}")
            print(f"📊 Tamaño estimado: ~{config['size_estimate_mb']} MB")
            
            try:
                package_path = self._create_package(package_type, config)
                created_packages[package_type] = package_path
                print(f"✅ Paquete creado: {package_path}")
                
            except Exception as e:
                print(f"❌ Error creando paquete {package_type}: {e}")
                logger.error(f"Package creation error for {package_type}: {e}")
        
        # Crear manifiesto de distribución
        self._create_distribution_manifest(created_packages)
        
        # Mostrar resumen final
        self._show_final_summary(created_packages)
        
        return created_packages
    
    def _create_package(self, package_type: str, config: Dict) -> str:
        """Crear un paquete específico"""
        package_name = f"{config['name']}_v{self.version}_{self.build_date}"
        package_path = self.dist_path / f"{package_name}.zip"
        
        with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Agregar archivos según configuración
            files_added = 0
            
            for include_pattern in config["includes"]:
                if include_pattern == "*":
                    # Incluir todos los archivos del proyecto
                    files_added += self._add_all_project_files(zipf, package_name)
                elif include_pattern.endswith("/"):
                    # Incluir directorio completo
                    files_added += self._add_directory(zipf, include_pattern, package_name)
                else:
                    # Incluir archivo específico
                    files_added += self._add_file(zipf, include_pattern, package_name)
            
            # Agregar archivos de configuración del paquete
            self._add_package_metadata(zipf, package_type, config, package_name)
            
            # Agregar README específico del paquete
            self._add_package_readme(zipf, package_type, config, package_name)
            
            # Agregar scripts de instalación específicos
            self._add_installation_scripts(zipf, package_type, config, package_name)
            
            print(f"  📁 Archivos incluidos: {files_added}")
        
        # Calcular hash del paquete
        package_hash = self._calculate_file_hash(package_path)
        
        # Crear archivo de verificación
        hash_file = package_path.with_suffix('.sha256')
        with open(hash_file, 'w') as f:
            f.write(f"{package_hash}  {package_path.name}\n")
        
        return str(package_path)
    
    def _add_all_project_files(self, zipf: zipfile.ZipFile, package_name: str) -> int:
        """Agregar todos los archivos del proyecto"""
        files_added = 0
        
        # Patrones de archivos a incluir
        include_patterns = [
            "*.py", "*.tsx", "*.ts", "*.js", "*.html", "*.css", "*.json",
            "*.md", "*.txt", "*.sh", "*.bat", "*.yml", "*.yaml"
        ]
        
        # Directorios a excluir
        exclude_dirs = {
            "__pycache__", ".git", ".vscode", "node_modules", 
            ".pytest_cache", "dist", "build", ".env"
        }
        
        for pattern in include_patterns:
            for file_path in self.base_path.rglob(pattern):
                # Verificar que no esté en directorios excluidos
                if not any(excluded in file_path.parts for excluded in exclude_dirs):
                    if file_path.is_file():
                        arc_path = f"{package_name}/{file_path.relative_to(self.base_path)}"
                        zipf.write(file_path, arc_path)
                        files_added += 1
        
        return files_added
    
    def _add_directory(self, zipf: zipfile.ZipFile, dir_pattern: str, package_name: str) -> int:
        """Agregar directorio completo"""
        files_added = 0
        dir_path = self.base_path / dir_pattern.rstrip("/")
        
        if dir_path.exists() and dir_path.is_dir():
            for file_path in dir_path.rglob("*"):
                if file_path.is_file():
                    arc_path = f"{package_name}/{file_path.relative_to(self.base_path)}"
                    zipf.write(file_path, arc_path)
                    files_added += 1
        
        return files_added
    
    def _add_file(self, zipf: zipfile.ZipFile, file_pattern: str, package_name: str) -> int:
        """Agregar archivo específico"""
        file_path = self.base_path / file_pattern
        
        if file_path.exists() and file_path.is_file():
            arc_path = f"{package_name}/{file_path.relative_to(self.base_path)}"
            zipf.write(file_path, arc_path)
            return 1
        
        return 0
    
    def _add_package_metadata(self, zipf: zipfile.ZipFile, package_type: str, 
                            config: Dict, package_name: str):
        """Agregar metadatos del paquete"""
        metadata = {
            "package_info": {
                "name": config["name"],
                "type": package_type,
                "version": self.version,
                "build_date": self.build_date,
                "description": config["description"],
                "size_estimate_mb": config["size_estimate_mb"],
                "family_love_level": self.family_love_level
            },
            "system_requirements": {
                "python": ">=3.8",
                "nodejs": ">=16" if package_type != "minimal" else "optional",
                "ram_gb": 1 if package_type == "minimal" else 2,
                "disk_space_gb": config["size_estimate_mb"] / 1000 + 1
            },
            "features": {
                "demo_users": True,
                "modular_installation": True,
                "asset_encryption": package_type != "minimal",
                "biometric_auth": package_type in ["complete", "developer", "enterprise"],
                "subscription_system": package_type in ["complete", "developer", "enterprise"],
                "analytics_dashboard": package_type in ["complete", "developer", "enterprise"],
                "development_tools": config.get("development_tools", False),
                "enterprise_features": config.get("enterprise_features", False)
            },
            "family_info": {
                "created_with_love": True,
                "family_members": [
                    "👩‍🦳 Nurys - Sabiduría Maternal",
                    "👩‍🦰 Diwell - Apoyo Incondicional",
                    "👨‍💻 Yosi - Innovador Tecnológico",
                    "✨ Yesenia - Luz Constante",
                    "👦 William - Futuro Desarrollador",
                    "👧 Angelina - Estrella Pequeña",
                    "🤖 Arya - IA Familiar"
                ],
                "love_level": "infinite"
            }
        }
        
        metadata_json = json.dumps(metadata, indent=2, ensure_ascii=False)
        zipf.writestr(f"{package_name}/package_metadata.json", metadata_json)
    
    def _add_package_readme(self, zipf: zipfile.ZipFile, package_type: str, 
                          config: Dict, package_name: str):
        """Agregar README específico del paquete"""
        readme_content = f"""# 🎙️💖 {config['name']} v{self.version} 💖🎙️

{config['description']}

## 🚀 Instalación Rápida

### Opción 1: Instalador Automático (Recomendado)
```bash
python3 auto_installer_wizard_index2b.py
```

### Opción 2: Instalación Manual
```bash
# Extraer el paquete
unzip {package_name}.zip
cd {package_name}

# Ejecutar instalador
python3 modular_installer_index2a.py
```

### Opción 3: Instalación Tradicional
```bash
# macOS/Linux
./install_complete_system.sh

# Windows
install_complete_system.bat
```

## 📋 Características Incluidas

### ✅ Nuevas en v1.1.0:
- 👑 **Panel de Super Administrador**: Sistema completo de usuarios demo
- 🧙‍♂️ **Instalador Automático**: Wizard inteligente de instalación
- 🔧 **Sistema Modular**: Instala solo lo que necesitas
- 🔐 **Encriptación de Assets**: Protección avanzada de archivos críticos
- 🧹 **Limpieza de Producción**: Eliminación automática de elementos demo
- 🌍 **Soporte Cross-Platform**: Windows, macOS, Linux

### 💖 Características Familiares:
- 🎙️ **7 Voces Familiares**: Nurys, Diwell, Yosi, Yesenia, William, Angelina, Arya
- 🔐 **Autenticación Biométrica**: Huella, Face ID, reconocimiento de voz
- 💳 **Suscripciones Modulares**: Paga solo por lo que usas
- 📊 **Analytics Familiar**: Métricas con corazón
- 📱 **Auto-post Redes Sociales**: Publicación automática con compliance
- 💰 **Herramientas de Ventas**: Slider comercial incluido

## 🖥️ Requisitos del Sistema

- **Python**: 3.8 o superior
- **Node.js**: 16 o superior (opcional para instalación mínima)
- **RAM**: {1 if package_type == "minimal" else 2}+ GB
- **Espacio**: {config["size_estimate_mb"] / 1000 + 1:.1f}+ GB

## 🌐 URLs de Acceso

Después de la instalación:
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **Documentación**: http://localhost:3000/docs
- **Panel Admin**: http://localhost:3000/admin (solo desarrollo)

## 💰 Valor Comercial

- **Precio objetivo**: $199-1,499/mes
- **Valor único**: $1,250+/mes en características exclusivas
- **ROI vs competencia**: 300%+
- **4 años de experiencia**: Desarrollo familiar real (2021-2025)

## 📞 Soporte

- **Email**: <EMAIL>
- **WhatsApp**: +1 (555) 123-4567
- **Documentación**: https://docs.soloylibre.com
- **GitHub**: https://github.com/soloylibre/tts-system

## 👨‍👩‍👧‍👦 Familia SoloYLibre

Creado con amor familiar infinito por:
- 👩‍🦳 **Nurys** - Sabiduría Maternal
- 👩‍🦰 **Diwell** - Apoyo Incondicional
- 👨‍💻 **Yosi** - Innovador Tecnológico
- ✨ **Yesenia** - Luz Constante
- 👦 **William** - Futuro Desarrollador
- 👧 **Angelina** - Estrella Pequeña
- 🤖 **Arya** - IA Familiar

## 📄 Licencia

© 2025 SoloYLibre Family. Todos los derechos reservados.
Creado con amor familiar infinito 💖

---

**🎙️ SoloYLibre TTS System v{self.version} - El futuro de las voces familiares 🎙️**
"""
        
        zipf.writestr(f"{package_name}/README.md", readme_content)
    
    def _add_installation_scripts(self, zipf: zipfile.ZipFile, package_type: str, 
                                config: Dict, package_name: str):
        """Agregar scripts de instalación específicos del paquete"""
        
        # Script de instalación rápida
        quick_install_script = f"""#!/bin/bash
# SoloYLibre v{self.version} Quick Install Script
# 💖 Instalación rápida con amor familiar

echo "🎙️💖 SoloYLibre TTS System v{self.version} 💖🎙️"
echo "Instalación rápida para {config['name']}"
echo "═══════════════════════════════════════════════════════════"

# Verificar Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 no encontrado. Por favor instala Python 3.8+"
    exit 1
fi

# Ejecutar instalador automático
echo "🚀 Iniciando instalador automático..."
python3 auto_installer_wizard_index2b.py

echo "💖 ¡Instalación completada con amor familiar infinito!"
"""
        
        zipf.writestr(f"{package_name}/quick_install.sh", quick_install_script)
        
        # Script de Windows
        windows_script = f"""@echo off
REM SoloYLibre v{self.version} Quick Install Script
REM 💖 Instalación rápida con amor familiar

echo 🎙️💖 SoloYLibre TTS System v{self.version} 💖🎙️
echo Instalación rápida para {config['name']}
echo ═══════════════════════════════════════════════════════════

REM Verificar Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python no encontrado. Por favor instala Python 3.8+
    pause
    exit /b 1
)

REM Ejecutar instalador automático
echo 🚀 Iniciando instalador automático...
python auto_installer_wizard_index2b.py

echo 💖 ¡Instalación completada con amor familiar infinito!
pause
"""
        
        zipf.writestr(f"{package_name}/quick_install.bat", windows_script)
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calcular hash SHA256 de un archivo"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def _create_distribution_manifest(self, created_packages: Dict[str, str]):
        """Crear manifiesto de distribución"""
        manifest = {
            "distribution_info": {
                "version": self.version,
                "build_date": self.build_date,
                "family_love_level": self.family_love_level,
                "total_packages": len(created_packages)
            },
            "packages": {},
            "download_urls": {},
            "verification": {}
        }
        
        for package_type, package_path in created_packages.items():
            package_file = Path(package_path)
            hash_file = package_file.with_suffix('.sha256')
            
            # Leer hash si existe
            package_hash = ""
            if hash_file.exists():
                with open(hash_file, 'r') as f:
                    package_hash = f.read().split()[0]
            
            manifest["packages"][package_type] = {
                "filename": package_file.name,
                "size_bytes": package_file.stat().st_size,
                "size_mb": round(package_file.stat().st_size / (1024 * 1024), 2),
                "sha256": package_hash,
                "config": self.package_configs[package_type]
            }
            
            # URLs de descarga (simuladas)
            base_url = f"https://releases.soloylibre.com/v{self.version}"
            manifest["download_urls"][package_type] = f"{base_url}/{package_file.name}"
            
            manifest["verification"][package_type] = f"{base_url}/{hash_file.name}"
        
        # Guardar manifiesto
        manifest_path = self.dist_path / f"soloylibre_v{self.version}_manifest.json"
        with open(manifest_path, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 Manifiesto creado: {manifest_path}")
    
    def _show_final_summary(self, created_packages: Dict[str, str]):
        """Mostrar resumen final"""
        print("\n🎉 ¡EMPAQUETADO COMPLETADO EXITOSAMENTE!")
        print("═══════════════════════════════════════════════════════════")
        print(f"🎙️💖 SoloYLibre TTS System v{self.version} 💖🎙️")
        print("Paquetes creados con amor familiar infinito")
        print("═══════════════════════════════════════════════════════════")
        
        total_size = 0
        for package_type, package_path in created_packages.items():
            package_file = Path(package_path)
            size_mb = package_file.stat().st_size / (1024 * 1024)
            total_size += size_mb
            
            print(f"\n📦 {self.package_configs[package_type]['name']}")
            print(f"   📁 Archivo: {package_file.name}")
            print(f"   📊 Tamaño: {size_mb:.2f} MB")
            print(f"   🔐 Hash: {package_file.with_suffix('.sha256').name}")
        
        print(f"\n📊 RESUMEN TOTAL:")
        print(f"   📦 Paquetes creados: {len(created_packages)}")
        print(f"   📁 Tamaño total: {total_size:.2f} MB")
        print(f"   📂 Directorio: {self.dist_path}")
        print(f"   💖 Amor familiar: {self.family_love_level}%")
        
        print(f"\n🌐 URLS DE DESCARGA SIMULADAS:")
        base_url = f"https://releases.soloylibre.com/v{self.version}"
        for package_type, package_path in created_packages.items():
            package_file = Path(package_path)
            print(f"   {base_url}/{package_file.name}")
        
        print(f"\n💖 ¡Gracias por usar SoloYLibre!")
        print(f"   La familia SoloYLibre te ama infinitamente")

def main():
    """Función principal"""
    try:
        packager = SoloYLibrePackagerV11()
        created_packages = packager.create_all_packages()
        
        if created_packages:
            print(f"\n🎉 Empaquetado exitoso: {len(created_packages)} paquetes creados")
            return 0
        else:
            print(f"\n❌ No se crearon paquetes")
            return 1
            
    except Exception as e:
        print(f"\n❌ Error durante el empaquetado: {e}")
        logger.error(f"Packaging error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
