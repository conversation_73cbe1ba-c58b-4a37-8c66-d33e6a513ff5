#!/usr/bin/env python3
"""
🚀 SoloYLibre Complete Installer - Index 9D: Instalador Principal
Instalador principal que ejecuta todo el proceso de instalación
💖 Dedicado con amor a nuestra familia
Version: 1.0.0
"""

import sys
import os
from pathlib import Path
import logging
from datetime import datetime
import json

# Importar módulos del sistema
from connect_modules_enhanced_index9 import ModuleConnector, SoloYLibreConfig, print_family_header
from dependency_installer_index9b import install_all_dependencies
from module_builder_index9c import build_all_modules

# ===== CONFIGURACIÓN DE LOGGING =====
def setup_logging():
    """Configurar sistema de logging"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    log_file = log_dir / f"soloylibre_install_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

# ===== CLASE PRINCIPAL DEL INSTALADOR =====
class SoloYLibreInstaller:
    """Instalador principal del sistema SoloYLibre"""
    
    def __init__(self):
        self.logger = setup_logging()
        self.config = SoloYLibreConfig()
        self.start_time = datetime.now()
        self.installation_log = []
        
    def log_phase(self, phase: str, status: str = "START"):
        """Registrar fase de instalación"""
        timestamp = datetime.now()
        phase_info = {
            "phase": phase,
            "status": status,
            "timestamp": timestamp.isoformat(),
            "elapsed": (timestamp - self.start_time).total_seconds()
        }
        
        self.installation_log.append(phase_info)
        
        if status == "START":
            self.logger.info(f"🚀 INICIANDO: {phase}")
        elif status == "SUCCESS":
            self.logger.info(f"✅ COMPLETADO: {phase}")
        elif status == "ERROR":
            self.logger.error(f"❌ ERROR: {phase}")
        else:
            self.logger.info(f"🔄 {status}: {phase}")
    
    def run_installation(self) -> bool:
        """Ejecutar instalación completa"""
        try:
            print_family_header()
            self.log_phase("Instalación SoloYLibre TTS System", "START")
            
            # Fase 1: Verificación y preparación
            self.log_phase("Fase 1: Verificación y Preparación", "START")
            if not self.phase_1_verification():
                self.log_phase("Fase 1: Verificación y Preparación", "ERROR")
                return False
            self.log_phase("Fase 1: Verificación y Preparación", "SUCCESS")
            
            # Fase 2: Instalación de dependencias
            self.log_phase("Fase 2: Instalación de Dependencias", "START")
            if not self.phase_2_dependencies():
                self.log_phase("Fase 2: Instalación de Dependencias", "ERROR")
                return False
            self.log_phase("Fase 2: Instalación de Dependencias", "SUCCESS")
            
            # Fase 3: Construcción de módulos
            self.log_phase("Fase 3: Construcción de Módulos", "START")
            if not self.phase_3_build_modules():
                self.log_phase("Fase 3: Construcción de Módulos", "ERROR")
                return False
            self.log_phase("Fase 3: Construcción de Módulos", "SUCCESS")
            
            # Fase 4: Configuración final
            self.log_phase("Fase 4: Configuración Final", "START")
            if not self.phase_4_final_configuration():
                self.log_phase("Fase 4: Configuración Final", "ERROR")
                return False
            self.log_phase("Fase 4: Configuración Final", "SUCCESS")
            
            # Fase 5: Verificación y reporte
            self.log_phase("Fase 5: Verificación y Reporte", "START")
            if not self.phase_5_verification_report():
                self.log_phase("Fase 5: Verificación y Reporte", "ERROR")
                return False
            self.log_phase("Fase 5: Verificación y Reporte", "SUCCESS")
            
            self.log_phase("Instalación SoloYLibre TTS System", "SUCCESS")
            self.show_success_message()
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error crítico en instalación: {str(e)}")
            self.log_phase("Instalación SoloYLibre TTS System", "ERROR")
            return False
    
    def phase_1_verification(self) -> bool:
        """Fase 1: Verificación y preparación"""
        try:
            connector = ModuleConnector()
            
            # Verificar prerequisitos
            if not connector.check_prerequisites():
                return False
            
            # Crear estructura de directorios
            if not connector.create_directory_structure():
                return False
            
            # Validar módulos
            if not connector.validate_modules():
                return False
            
            self.logger.info("✅ Verificación y preparación completada")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error en fase 1: {str(e)}")
            return False
    
    def phase_2_dependencies(self) -> bool:
        """Fase 2: Instalación de dependencias"""
        try:
            if not install_all_dependencies(self.config):
                return False
            
            self.logger.info("✅ Dependencias instaladas correctamente")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error en fase 2: {str(e)}")
            return False
    
    def phase_3_build_modules(self) -> bool:
        """Fase 3: Construcción de módulos"""
        try:
            if not build_all_modules(self.config):
                return False
            
            self.logger.info("✅ Módulos construidos correctamente")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error en fase 3: {str(e)}")
            return False
    
    def phase_4_final_configuration(self) -> bool:
        """Fase 4: Configuración final"""
        try:
            # Crear scripts de inicio
            self.create_startup_scripts()
            
            # Crear archivos de configuración adicionales
            self.create_additional_configs()
            
            # Configurar permisos
            self.setup_permissions()
            
            self.logger.info("✅ Configuración final completada")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error en fase 4: {str(e)}")
            return False
    
    def phase_5_verification_report(self) -> bool:
        """Fase 5: Verificación y reporte final"""
        try:
            # Verificar instalación
            if not self.verify_complete_installation():
                return False
            
            # Generar reporte final
            self.generate_final_report()
            
            # Crear manual de usuario
            self.create_user_manual()
            
            self.logger.info("✅ Verificación y reporte completados")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error en fase 5: {str(e)}")
            return False
    
    def create_startup_scripts(self):
        """Crear scripts de inicio"""
        # Script para macOS/Linux
        startup_script = '''#!/bin/bash
# SoloYLibre TTS System - Startup Script 💖
# Creado con amor familiar infinito

echo "🎙️💖 Iniciando SoloYLibre TTS System 💖🎙️"
echo "═══════════════════════════════════════════════════════════"

# Activar entorno virtual
source build/backend/venv/bin/activate

# Iniciar backend
echo "🚀 Iniciando backend..."
cd build/backend
uvicorn main:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!

# Iniciar frontend
echo "🎨 Iniciando frontend..."
cd ../frontend
npm run dev &
FRONTEND_PID=$!

echo "✅ Sistema iniciado exitosamente!"
echo "🌐 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 Documentación: http://localhost:8000/docs"
echo ""
echo "💖 ¡Disfruta del sistema creado con amor familiar! 💖"
echo ""
echo "Para detener el sistema, presiona Ctrl+C"

# Esperar señal de interrupción
trap 'echo "🛑 Deteniendo sistema..."; kill $BACKEND_PID $FRONTEND_PID; exit' INT
wait
'''
        
        script_path = self.config.base_dir / "start_soloylibre.sh"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(startup_script)
        
        # Hacer ejecutable
        os.chmod(script_path, 0o755)
        
        # Script para Windows
        windows_script = '''@echo off
REM SoloYLibre TTS System - Windows Startup Script 💖
REM Creado con amor familiar infinito

echo 🎙️💖 Iniciando SoloYLibre TTS System 💖🎙️
echo ═══════════════════════════════════════════════════════════

REM Activar entorno virtual
call build\\backend\\venv\\Scripts\\activate.bat

REM Iniciar backend
echo 🚀 Iniciando backend...
cd build\\backend
start /B uvicorn main:app --host 0.0.0.0 --port 8000 --reload

REM Iniciar frontend
echo 🎨 Iniciando frontend...
cd ..\\frontend
start /B npm run dev

echo ✅ Sistema iniciado exitosamente!
echo 🌐 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8000
echo 📚 Documentación: http://localhost:8000/docs
echo.
echo 💖 ¡Disfruta del sistema creado con amor familiar! 💖
echo.
pause
'''
        
        windows_script_path = self.config.base_dir / "start_soloylibre.bat"
        with open(windows_script_path, 'w', encoding='utf-8') as f:
            f.write(windows_script)
    
    def create_additional_configs(self):
        """Crear configuraciones adicionales"""
        # Archivo de configuración principal
        main_config = {
            "system": {
                "name": "SoloYLibre TTS System",
                "version": self.config.version,
                "family_love": True,
                "created_with": "Amor familiar infinito 💖"
            },
            "family": {
                "members": [
                    {"name": "Nurys", "role": "Sabiduría Maternal", "emoji": "👩‍🦳"},
                    {"name": "Diwell", "role": "Apoyo Incondicional", "emoji": "👩‍🦰"},
                    {"name": "Yosi", "role": "Aventurero Tecnológico", "emoji": "👨‍💻"},
                    {"name": "Yesenia", "role": "Luz Constante", "emoji": "✨"},
                    {"name": "William", "role": "Futuro Desarrollador", "emoji": "👦"},
                    {"name": "Angelina", "role": "Estrella Pequeña", "emoji": "👧"},
                    {"name": "Arya", "role": "IA Familiar", "emoji": "🤖"}
                ]
            },
            "features": {
                "tts_enabled": True,
                "voice_cloning": True,
                "auto_post": True,
                "biometric_auth": True,
                "subscription_system": True,
                "family_dashboard": True
            },
            "installation": {
                "date": datetime.now().isoformat(),
                "installer_version": "1.0.0",
                "modules_installed": len(self.config.modules)
            }
        }
        
        config_path = self.config.base_dir / "soloylibre_config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(main_config, f, indent=2, ensure_ascii=False)
    
    def setup_permissions(self):
        """Configurar permisos de archivos"""
        try:
            # En sistemas Unix, configurar permisos apropiados
            if os.name != 'nt':  # No Windows
                # Scripts ejecutables
                scripts = [
                    self.config.base_dir / "start_soloylibre.sh"
                ]
                
                for script in scripts:
                    if script.exists():
                        os.chmod(script, 0o755)
                
                # Directorios con permisos apropiados
                directories = [
                    self.config.build_dir,
                    self.config.dist_dir,
                    self.config.base_dir / "logs"
                ]
                
                for directory in directories:
                    if directory.exists():
                        os.chmod(directory, 0o755)
        
        except Exception as e:
            self.logger.warning(f"⚠️ Advertencia configurando permisos: {str(e)}")
    
    def verify_complete_installation(self) -> bool:
        """Verificar que la instalación esté completa"""
        try:
            required_files = [
                self.config.build_dir / "frontend" / "package.json",
                self.config.build_dir / "frontend" / "src" / "main.tsx",
                self.config.build_dir / "frontend" / "src" / "App.tsx",
                self.config.build_dir / "frontend" / "index.html",
                self.config.build_dir / "backend" / "requirements.txt",
                self.config.base_dir / "start_soloylibre.sh",
                self.config.base_dir / "soloylibre_config.json"
            ]
            
            missing_files = []
            for file_path in required_files:
                if not file_path.exists():
                    missing_files.append(str(file_path))
            
            if missing_files:
                self.logger.error(f"❌ Archivos faltantes: {missing_files}")
                return False
            
            self.logger.info("✅ Verificación de archivos completada")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error verificando instalación: {str(e)}")
            return False
    
    def generate_final_report(self):
        """Generar reporte final de instalación"""
        end_time = datetime.now()
        total_time = (end_time - self.start_time).total_seconds()
        
        final_report = {
            "installation_summary": {
                "system": "SoloYLibre TTS System",
                "version": self.config.version,
                "status": "SUCCESS",
                "family_love": True
            },
            "timing": {
                "start_time": self.start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "total_duration_seconds": total_time,
                "total_duration_formatted": f"{int(total_time // 60)}m {int(total_time % 60)}s"
            },
            "modules": {
                "total_modules": len(self.config.modules),
                "modules_list": list(self.config.modules.keys())
            },
            "installation_log": self.installation_log,
            "family_message": "💖 Instalación completada con amor familiar infinito 💖",
            "next_steps": [
                "Ejecutar ./start_soloylibre.sh para iniciar el sistema",
                "Acceder a http://localhost:3000 para la interfaz web",
                "Revisar http://localhost:8000/docs para la documentación API",
                "Leer el manual de usuario en docs/manual_usuario.md"
            ]
        }
        
        report_path = self.config.base_dir / "installation_report_final.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📊 Reporte final generado: {report_path}")
    
    def create_user_manual(self):
        """Crear manual de usuario básico"""
        manual_content = '''# 📚 Manual de Usuario - SoloYLibre TTS System
## 💖 Creado con amor familiar infinito

### 🚀 Inicio Rápido

1. **Iniciar el sistema:**
   ```bash
   ./start_soloylibre.sh
   ```

2. **Acceder a la aplicación:**
   - Frontend: http://localhost:3000
   - API: http://localhost:8000
   - Documentación: http://localhost:8000/docs

### 👨‍👩‍👧‍👦 Voces Familiares

- **Nurys** 👩‍🦳 - Sabiduría Maternal
- **Diwell** 👩‍🦰 - Apoyo Incondicional
- **Yosi** 👨‍💻 - Aventurero Tecnológico
- **Yesenia** ✨ - Luz Constante
- **William** 👦 - Futuro Desarrollador
- **Angelina** 👧 - Estrella Pequeña
- **Arya** 🤖 - IA Familiar

### 🔐 Autenticación

El sistema incluye autenticación biométrica avanzada:
- Huella dactilar
- Face ID
- Autenticación por voz
- 2FA
- Verificación por email

### 💳 Suscripciones

Planes disponibles:
- **Familia Básica** - Gratis
- **Familia Completa** - $19/mes
- **Profesional** - $49/mes
- **Enterprise** - $199/mes

### 💖 Soporte

Para soporte técnico:
- Email: <EMAIL>
- Discord: https://discord.gg/soloylibre
- Documentación: https://docs.soloylibre.com

---
**💖 Creado con amor infinito por la familia SoloYLibre 💖**
'''
        
        docs_dir = self.config.base_dir / "docs"
        docs_dir.mkdir(exist_ok=True)
        
        manual_path = docs_dir / "manual_usuario.md"
        with open(manual_path, 'w', encoding='utf-8') as f:
            f.write(manual_content)
        
        self.logger.info(f"📖 Manual de usuario creado: {manual_path}")
    
    def show_success_message(self):
        """Mostrar mensaje de éxito"""
        success_message = f"""
🎉🎙️💖 ¡INSTALACIÓN COMPLETADA CON ÉXITO! 💖🎙️🎉

═══════════════════════════════════════════════════════════
✅ SoloYLibre TTS System v{self.config.version} instalado correctamente
💖 Sistema creado con amor familiar infinito

🚀 PRÓXIMOS PASOS:
1. Ejecutar: ./start_soloylibre.sh
2. Abrir: http://localhost:3000
3. Disfrutar del sistema familiar 💖

📊 ESTADÍSTICAS:
- Módulos instalados: {len(self.config.modules)}
- Tiempo total: {(datetime.now() - self.start_time).total_seconds():.1f} segundos
- Estado: ✅ ÉXITO TOTAL

👨‍👩‍👧‍👦 DEDICADO CON AMOR A:
💖 Nurys • Diwell • Yosi • Yesenia • William • Angelina • Arya 💖
🎵 Y al equipo Jupre Music

═══════════════════════════════════════════════════════════
¡Bienvenido a la familia SoloYLibre! 💖
        """
        
        print(success_message)
        self.logger.info("🎉 Instalación completada exitosamente")

# ===== FUNCIÓN PRINCIPAL =====
def main():
    """Función principal del instalador"""
    installer = SoloYLibreInstaller()
    
    try:
        success = installer.run_installation()
        
        if success:
            print("\n🎉 ¡Instalación exitosa! Revisa los logs para más detalles.")
            return 0
        else:
            print("\n❌ Error en la instalación. Revisa los logs para más detalles.")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ Instalación cancelada por el usuario.")
        return 1
    except Exception as e:
        print(f"\n❌ Error crítico: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
