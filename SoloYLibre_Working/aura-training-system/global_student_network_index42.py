#!/usr/bin/env python3
"""
🌍 Global Student Network - Index 42: Red Mundial de Estudiantes
Plataforma social educativa que conecta estudiantes de todo el mundo
💖 Desarrollado bajo la dirección de JoseT<PERSON> - Head of Development Team
🌍 Global Student Network - Worldwide Educational Social Platform
Version: 1.0.0 - Cross-Cultural Learning & Global Student Collaboration
"""

import random
from datetime import datetime, timedelta
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GlobalStudentNetwork:
    """
    🌍 Red Mundial de Estudiantes

    CARACTERÍSTICAS REVOLUCIONARIAS:
    - Conexión estudiantil intercultural
    - Grupos de estudio globales
    - Intercambio de conocimientos
    - Mentorías entre pares
    - Proyectos colaborativos mundiales
    - Eventos académicos virtuales
    """

    def __init__(self):
        self.system_name = "Global Student Network"
        self.version = "1.0.0"
        self.developer = "<PERSON><PERSON><PERSON><PERSON> - Head of Development Team"

        # ESTUDIANTES REGISTRADOS
        self.registered_students = {
            "arya_rd": {
                "student_id": "arya_rd",
                "name": "Arya Encarnación",
                "age": 16,
                "country": "República Dominicana",
                "city": "Santo Domingo",
                "languages": ["español", "english"],
                "interests": ["ciencias", "investigación", "tecnología"],
                "academic_level": "high_school",
                "specializations": ["biology", "physics", "chemistry"],
                "cultural_background": "Caribbean_Latin",
                "learning_style": "curious_analytical",
                "timezone": "GMT-4",
                "online_status": "active",
                "study_groups": [],
                "friends": [],
                "mentoring": {"as_mentor": [], "as_mentee": []},
                "projects": [],
                "achievements": [],
                "join_date": datetime.now().isoformat()
            },

            "alex_usa": {
                "student_id": "alex_usa",
                "name": "Alex Johnson",
                "age": 17,
                "country": "United States",
                "city": "New York",
                "languages": ["english", "spanish"],
                "interests": ["technology", "entrepreneurship", "innovation"],
                "academic_level": "high_school",
                "specializations": ["computer_science", "business", "mathematics"],
                "cultural_background": "American_Urban",
                "learning_style": "practical_fast_paced",
                "timezone": "GMT-5",
                "online_status": "active",
                "study_groups": [],
                "friends": [],
                "mentoring": {"as_mentor": [], "as_mentee": []},
                "projects": [],
                "achievements": [],
                "join_date": datetime.now().isoformat()
            },

            "marie_france": {
                "student_id": "marie_france",
                "name": "Marie Dubois",
                "age": 16,
                "country": "France",
                "city": "Paris",
                "languages": ["français", "english", "español"],
                "interests": ["arts", "literature", "philosophy"],
                "academic_level": "lycée",
                "specializations": ["fine_arts", "literature", "history"],
                "cultural_background": "French_European",
                "learning_style": "intellectual_reflective",
                "timezone": "GMT+1",
                "online_status": "active",
                "study_groups": [],
                "friends": [],
                "mentoring": {"as_mentor": [], "as_mentee": []},
                "projects": [],
                "achievements": [],
                "join_date": datetime.now().isoformat()
            },

            "yuki_japan": {
                "student_id": "yuki_japan",
                "name": "Yuki Tanaka",
                "age": 15,
                "country": "Japan",
                "city": "Tokyo",
                "languages": ["日本語", "english"],
                "interests": ["technology", "robotics", "precision"],
                "academic_level": "high_school",
                "specializations": ["robotics", "engineering", "mathematics"],
                "cultural_background": "Japanese_Asian",
                "learning_style": "disciplined_methodical",
                "timezone": "GMT+9",
                "online_status": "active",
                "study_groups": [],
                "friends": [],
                "mentoring": {"as_mentor": [], "as_mentee": []},
                "projects": [],
                "achievements": [],
                "join_date": datetime.now().isoformat()
            },

            "carlos_brazil": {
                "student_id": "carlos_brazil",
                "name": "Carlos Silva",
                "age": 17,
                "country": "Brazil",
                "city": "São Paulo",
                "languages": ["português", "english", "español"],
                "interests": ["sports", "community", "leadership"],
                "academic_level": "ensino_médio",
                "specializations": ["sports_science", "sociology", "leadership"],
                "cultural_background": "Brazilian_Latin",
                "learning_style": "social_energetic",
                "timezone": "GMT-3",
                "online_status": "active",
                "study_groups": [],
                "friends": [],
                "mentoring": {"as_mentor": [], "as_mentee": []},
                "projects": [],
                "achievements": [],
                "join_date": datetime.now().isoformat()
            }
        }

        # GRUPOS DE ESTUDIO GLOBALES
        self.study_groups = {}

        # PROYECTOS COLABORATIVOS
        self.collaborative_projects = {}

        # EVENTOS ACADÉMICOS
        self.academic_events = {}

        # SISTEMA DE MATCHING
        self.matching_algorithm = {
            "compatibility_factors": [
                "shared_interests",
                "complementary_skills",
                "language_overlap",
                "timezone_compatibility",
                "cultural_exchange_potential"
            ],
            "matching_threshold": 0.7
        }

        # REGISTRO DE ACTIVIDADES
        self.network_activities = {}
        self.friendship_connections = {}
        self.mentoring_relationships = {}

        logger.info(f"🌍 {self.system_name} inicializado - {self.developer}")

    def create_study_group(self, creator_id, subject, description, max_members=8):
        """Crear grupo de estudio global"""
        if creator_id not in self.registered_students:
            return {"error": f"Estudiante {creator_id} no encontrado"}

        creator = self.registered_students[creator_id]
        group_id = f"group_{len(self.study_groups) + 1}"

        study_group = {
            "group_id": group_id,
            "name": f"Global {subject} Study Group",
            "subject": subject,
            "description": description,
            "creator_id": creator_id,
            "creator_name": creator["name"],
            "creator_country": creator["country"],
            "members": [creator_id],
            "max_members": max_members,
            "languages": creator["languages"],
            "creation_date": datetime.now().isoformat(),
            "study_sessions": [],
            "shared_resources": [],
            "cultural_diversity_score": 1,
            "active_status": "recruiting"
        }

        self.study_groups[group_id] = study_group
        creator["study_groups"].append(group_id)

        return {
            "success": True,
            "group_id": group_id,
            "group_name": study_group["name"],
            "subject": subject,
            "creator": creator["name"],
            "creator_country": creator["country"],
            "max_members": max_members,
            "languages": creator["languages"]
        }

    def join_study_group(self, student_id, group_id):
        """Unirse a grupo de estudio"""
        if student_id not in self.registered_students:
            return {"error": f"Estudiante {student_id} no encontrado"}

        if group_id not in self.study_groups:
            return {"error": f"Grupo {group_id} no encontrado"}

        student = self.registered_students[student_id]
        group = self.study_groups[group_id]

        if len(group["members"]) >= group["max_members"]:
            return {"error": "Grupo lleno"}

        if student_id in group["members"]:
            return {"error": "Ya eres miembro de este grupo"}

        # Agregar estudiante al grupo
        group["members"].append(student_id)
        student["study_groups"].append(group_id)

        # Actualizar diversidad cultural
        countries = set(self.registered_students[member]["country"] for member in group["members"])
        group["cultural_diversity_score"] = len(countries)

        # Actualizar idiomas del grupo
        all_languages = set()
        for member_id in group["members"]:
            all_languages.update(self.registered_students[member_id]["languages"])
        group["languages"] = list(all_languages)

        return {
            "success": True,
            "group_name": group["name"],
            "new_member": student["name"],
            "member_country": student["country"],
            "total_members": len(group["members"]),
            "cultural_diversity": group["cultural_diversity_score"],
            "group_languages": group["languages"]
        }

    def find_study_partners(self, student_id, subject_interest):
        """Encontrar compañeros de estudio compatibles"""
        if student_id not in self.registered_students:
            return {"error": f"Estudiante {student_id} no encontrado"}

        student = self.registered_students[student_id]
        compatible_partners = []

        for other_id, other_student in self.registered_students.items():
            if other_id == student_id:
                continue

            compatibility_score = self._calculate_compatibility(student, other_student, subject_interest)

            if compatibility_score >= self.matching_algorithm["matching_threshold"]:
                compatible_partners.append({
                    "student_id": other_id,
                    "name": other_student["name"],
                    "country": other_student["country"],
                    "compatibility_score": round(compatibility_score, 2),
                    "shared_interests": list(set(student["interests"]) & set(other_student["interests"])),
                    "shared_languages": list(set(student["languages"]) & set(other_student["languages"])),
                    "cultural_background": other_student["cultural_background"],
                    "timezone": other_student["timezone"]
                })

        # Ordenar por compatibilidad
        compatible_partners.sort(key=lambda x: x["compatibility_score"], reverse=True)

        return {
            "student": student["name"],
            "subject_interest": subject_interest,
            "compatible_partners": compatible_partners[:5],  # Top 5
            "total_matches": len(compatible_partners),
            "matching_algorithm": "SoloYLibre_Global_Compatibility"
        }

    def create_friendship(self, student_1_id, student_2_id):
        """Crear amistad entre estudiantes"""
        if student_1_id not in self.registered_students or student_2_id not in self.registered_students:
            return {"error": "Uno o ambos estudiantes no encontrados"}

        student_1 = self.registered_students[student_1_id]
        student_2 = self.registered_students[student_2_id]

        friendship_id = f"friendship_{len(self.friendship_connections) + 1}"

        friendship_data = {
            "friendship_id": friendship_id,
            "student_1": student_1_id,
            "student_2": student_2_id,
            "student_1_name": student_1["name"],
            "student_2_name": student_2["name"],
            "countries": [student_1["country"], student_2["country"]],
            "cultural_exchange": f"{student_1['cultural_background']} + {student_2['cultural_background']}",
            "shared_languages": list(set(student_1["languages"]) & set(student_2["languages"])),
            "friendship_date": datetime.now().isoformat(),
            "interaction_count": 0,
            "status": "active"
        }

        # Actualizar listas de amigos
        student_1["friends"].append(student_2_id)
        student_2["friends"].append(student_1_id)

        self.friendship_connections[friendship_id] = friendship_data

        return {
            "success": True,
            "friendship_id": friendship_id,
            "friends": [student_1["name"], student_2["name"]],
            "countries": friendship_data["countries"],
            "cultural_exchange": friendship_data["cultural_exchange"],
            "shared_languages": friendship_data["shared_languages"]
        }

    def _calculate_compatibility(self, student_1, student_2, subject_interest):
        """Calcular compatibilidad entre estudiantes"""
        score = 0.0

        # Intereses compartidos (30%)
        shared_interests = set(student_1["interests"]) & set(student_2["interests"])
        if subject_interest in shared_interests:
            score += 0.3
        elif shared_interests:
            score += 0.15

        # Idiomas compartidos (25%)
        shared_languages = set(student_1["languages"]) & set(student_2["languages"])
        if shared_languages:
            score += 0.25 * (len(shared_languages) / max(len(student_1["languages"]), len(student_2["languages"])))

        # Nivel académico compatible (20%)
        if student_1["academic_level"] == student_2["academic_level"]:
            score += 0.2

        # Diversidad cultural (15%)
        if student_1["cultural_background"] != student_2["cultural_background"]:
            score += 0.15  # Bonus por diversidad

        # Compatibilidad de zona horaria (10%)
        timezone_1 = int(student_1["timezone"].split("GMT")[1])
        timezone_2 = int(student_2["timezone"].split("GMT")[1])
        timezone_diff = abs(timezone_1 - timezone_2)

        if timezone_diff <= 3:
            score += 0.1
        elif timezone_diff <= 6:
            score += 0.05

        return min(score, 1.0)

    def schedule_global_event(self, organizer_id, event_type, title, description):
        """Programar evento académico global"""
        if organizer_id not in self.registered_students:
            return {"error": f"Organizador {organizer_id} no encontrado"}

        organizer = self.registered_students[organizer_id]
        event_id = f"event_{len(self.academic_events) + 1}"

        # Calcular horario que funcione para múltiples zonas horarias
        optimal_time = self._calculate_optimal_time()

        event_data = {
            "event_id": event_id,
            "title": title,
            "description": description,
            "event_type": event_type,
            "organizer_id": organizer_id,
            "organizer_name": organizer["name"],
            "organizer_country": organizer["country"],
            "scheduled_time": optimal_time,
            "duration_minutes": 90,
            "participants": [],
            "max_participants": 50,
            "languages": ["english", "español"],  # Idiomas principales
            "cultural_focus": "global_diversity",
            "registration_open": True,
            "creation_date": datetime.now().isoformat()
        }

        self.academic_events[event_id] = event_data

        return {
            "success": True,
            "event_id": event_id,
            "title": title,
            "organizer": organizer["name"],
            "scheduled_time": optimal_time,
            "max_participants": 50,
            "languages": event_data["languages"]
        }

    def _calculate_optimal_time(self):
        """Calcular horario óptimo para evento global"""
        # Horario que funciona razonablemente para múltiples zonas
        base_time = datetime.now() + timedelta(days=7)  # Una semana adelante
        optimal_hour = 14  # 2 PM UTC como base

        optimal_time = base_time.replace(hour=optimal_hour, minute=0, second=0, microsecond=0)
        return optimal_time.isoformat()

    def get_student_profile(self, student_id):
        """Obtener perfil completo del estudiante"""
        if student_id not in self.registered_students:
            return {"error": f"Estudiante {student_id} no encontrado"}

        student = self.registered_students[student_id]

        # Calcular estadísticas de red
        study_groups_count = len(student["study_groups"])
        friends_count = len(student["friends"])

        # Calcular diversidad cultural de amigos
        friend_countries = set()
        for friend_id in student["friends"]:
            if friend_id in self.registered_students:
                friend_countries.add(self.registered_students[friend_id]["country"])

        return {
            "student_id": student_id,
            "name": student["name"],
            "country": student["country"],
            "city": student["city"],
            "age": student["age"],
            "languages": student["languages"],
            "interests": student["interests"],
            "specializations": student["specializations"],
            "cultural_background": student["cultural_background"],
            "learning_style": student["learning_style"],
            "network_stats": {
                "study_groups": study_groups_count,
                "friends": friends_count,
                "friend_countries": len(friend_countries),
                "cultural_diversity_score": len(friend_countries) * 10
            },
            "online_status": student["online_status"],
            "join_date": student["join_date"]
        }

    def get_network_statistics(self):
        """Obtener estadísticas de la red global"""
        total_students = len(self.registered_students)
        total_study_groups = len(self.study_groups)
        total_friendships = len(self.friendship_connections)
        total_events = len(self.academic_events)

        # Calcular diversidad
        countries = set(student["country"] for student in self.registered_students.values())
        languages = set(lang for student in self.registered_students.values() for lang in student["languages"])
        cultural_backgrounds = set(student["cultural_background"] for student in self.registered_students.values())

        # Calcular métricas de actividad
        active_students = len([s for s in self.registered_students.values() if s["online_status"] == "active"])

        return {
            "system_name": self.system_name,
            "version": self.version,
            "developer": self.developer,
            "network_size": {
                "total_students": total_students,
                "active_students": active_students,
                "study_groups": total_study_groups,
                "friendships": total_friendships,
                "academic_events": total_events
            },
            "global_diversity": {
                "countries_represented": len(countries),
                "languages_spoken": len(languages),
                "cultural_backgrounds": len(cultural_backgrounds),
                "country_list": list(countries),
                "language_list": list(languages)
            },
            "engagement_metrics": {
                "activity_rate": (active_students / total_students) * 100 if total_students > 0 else 0,
                "average_friends_per_student": total_friendships * 2 / total_students if total_students > 0 else 0,
                "cultural_exchange_connections": total_friendships
            },
            "unique_features": [
                "Conexión estudiantil intercultural",
                "Algoritmo de compatibilidad global",
                "Grupos de estudio multiculturales",
                "Eventos académicos sincronizados",
                "Mentorías entre pares globales",
                "Red social educativa mundial"
            ]
        }

def main():
    """Función principal de prueba"""
    print("🌍🚀 Global Student Network Test 🚀🌍")
    print("═══════════════════════════════════════════════════════════")

    # Crear red de estudiantes
    student_network = GlobalStudentNetwork()

    # Crear grupo de estudio
    print("📚 Creando grupo de estudio global...")

    science_group = student_network.create_study_group(
        "arya_rd",
        "Advanced Biology",
        "Grupo para estudiar biología avanzada con perspectiva global"
    )
    print(f"  🧬 Grupo creado: {science_group['success']}")
    print(f"    📖 Nombre: {science_group['group_name']}")
    print(f"    👩‍🎓 Creadora: {science_group['creator']} ({science_group['creator_country']})")
    print(f"    🗣️ Idiomas: {', '.join(science_group['languages'])}")

    # Unir estudiantes al grupo
    print(f"\n👥 Uniendo estudiantes al grupo...")

    alex_join = student_network.join_study_group("alex_usa", science_group['group_id'])
    print(f"  🇺🇸 Alex se unió: {alex_join['success']}")
    print(f"    🌍 Diversidad cultural: {alex_join['cultural_diversity']} países")
    print(f"    🗣️ Idiomas del grupo: {', '.join(alex_join['group_languages'])}")

    yuki_join = student_network.join_study_group("yuki_japan", science_group['group_id'])
    print(f"  🇯🇵 Yuki se unió: {yuki_join['success']}")
    print(f"    🌍 Diversidad cultural: {yuki_join['cultural_diversity']} países")

    # Encontrar compañeros de estudio
    print(f"\n🔍 Encontrando compañeros de estudio para Marie...")

    marie_partners = student_network.find_study_partners("marie_france", "arts")
    print(f"  🎨 Compañeros encontrados: {marie_partners['total_matches']}")
    if marie_partners['compatible_partners']:
        top_partner = marie_partners['compatible_partners'][0]
        print(f"    🏆 Mejor match: {top_partner['name']} ({top_partner['country']})")
        print(f"    📊 Compatibilidad: {top_partner['compatibility_score']}")
        print(f"    🤝 Intereses compartidos: {', '.join(top_partner['shared_interests'])}")

    # Crear amistades
    print(f"\n🤝 Creando amistades globales...")

    friendship_1 = student_network.create_friendship("arya_rd", "yuki_japan")
    print(f"  👭 Amistad RD-Japan: {friendship_1['success']}")
    print(f"    🌍 Intercambio cultural: {friendship_1['cultural_exchange']}")
    print(f"    🗣️ Idiomas compartidos: {', '.join(friendship_1['shared_languages'])}")

    friendship_2 = student_network.create_friendship("alex_usa", "carlos_brazil")
    print(f"  👬 Amistad USA-Brazil: {friendship_2['success']}")
    print(f"    🌎 Países: {', '.join(friendship_2['countries'])}")

    # Programar evento global
    print(f"\n🎓 Programando evento académico global...")

    global_event = student_network.schedule_global_event(
        "marie_france",
        "cultural_symposium",
        "Global Arts & Science Symposium",
        "Evento que conecta arte y ciencia desde perspectivas culturales"
    )
    print(f"  🌍 Evento programado: {global_event['success']}")
    print(f"    📅 Título: {global_event['title']}")
    print(f"    👩‍🏫 Organizadora: {global_event['organizer']}")
    print(f"    👥 Capacidad: {global_event['max_participants']} participantes")

    # Perfil de estudiante
    print(f"\n👤 Perfil de Arya:")
    arya_profile = student_network.get_student_profile("arya_rd")
    print(f"  🌍 País: {arya_profile['country']}")
    print(f"  🗣️ Idiomas: {', '.join(arya_profile['languages'])}")
    print(f"  🎯 Intereses: {', '.join(arya_profile['interests'])}")
    print(f"  📚 Grupos de estudio: {arya_profile['network_stats']['study_groups']}")
    print(f"  👥 Amigos: {arya_profile['network_stats']['friends']}")
    print(f"  🌍 Países de amigos: {arya_profile['network_stats']['friend_countries']}")
    print(f"  📊 Score diversidad: {arya_profile['network_stats']['cultural_diversity_score']}")

    # Estadísticas de la red
    stats = student_network.get_network_statistics()
    print(f"\n📊 Estadísticas Red Global:")
    print(f"  👥 Estudiantes totales: {stats['network_size']['total_students']}")
    print(f"  ⚡ Estudiantes activos: {stats['network_size']['active_students']}")
    print(f"  📚 Grupos de estudio: {stats['network_size']['study_groups']}")
    print(f"  🤝 Amistades: {stats['network_size']['friendships']}")
    print(f"  🎓 Eventos académicos: {stats['network_size']['academic_events']}")
    print(f"  🌍 Países representados: {stats['global_diversity']['countries_represented']}")
    print(f"  🗣️ Idiomas hablados: {stats['global_diversity']['languages_spoken']}")
    print(f"  📊 Tasa actividad: {stats['engagement_metrics']['activity_rate']:.1f}%")

    print(f"\n🚀 Características únicas:")
    for feature in stats['unique_features'][:3]:
        print(f"  ✨ {feature}")

    print(f"\n🎯 ¡Red Mundial de Estudiantes funcionando perfectamente!")

if __name__ == "__main__":
    main()