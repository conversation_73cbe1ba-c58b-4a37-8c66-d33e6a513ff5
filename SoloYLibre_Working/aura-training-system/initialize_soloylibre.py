#!/usr/bin/env python3
"""
🚀 SoloYLibre System Initializer
Script de inicialización automática del sistema integrado
💖 Generado automáticamente con amor familiar
"""

import sys
import json
import logging
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def initialize_soloylibre_system():
    """Inicializar sistema SoloYLibre completo"""
    print("🎙️💖 Inicializando SoloYLibre TTS System v1.1.0 💖🎙️")
    print("═══════════════════════════════════════════════════════════")
    
    # Cargar configuración
    config_path = Path("soloylibre_integrated_config.json")
    if not config_path.exists():
        logger.error("Configuración no encontrada")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # Inicializar módulos en secuencia
    startup_sequence = config.get("startup_sequence", [])
    
    for module_name in startup_sequence:
        if module_name in config["modules"]:
            print(f"🔗 Inicializando: {module_name}")
            try:
                # Importar y ejecutar módulo
                module_files = config["modules"][module_name]["files"]
                for file_name in module_files:
                    if file_name.endswith(".py"):
                        module_path = file_name.replace(".py", "").replace("/", ".")
                        __import__(module_path)
                        logger.info(f"Módulo {module_name} inicializado")
            except Exception as e:
                logger.error(f"Error inicializando {module_name}: {e}")
    
    print("✅ Sistema SoloYLibre inicializado con amor familiar infinito")
    return True

if __name__ == "__main__":
    success = initialize_soloylibre_system()
    sys.exit(0 if success else 1)
