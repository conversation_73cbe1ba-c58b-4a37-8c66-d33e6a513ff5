#!/usr/bin/env python3
"""
🎯 SoloYLibre Pitch Deck Slides - Index 2C: Slides Restantes del Pitch Deck
Generador de slides restantes para el pitch deck - Parte 3
💖 Dedicado con amor a nuestra familia
Version: 1.1.0 - Ultimate Update 2
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class PitchDeckSlidesGenerator:
    """Generador de slides restantes para el pitch deck"""
    
    def __init__(self, deck_data: Dict[str, Any]):
        self.deck_data = deck_data
    
    def generate_competition_slide(self) -> str:
        """Generar slide de competencia"""
        competitive_analysis = self.deck_data["competitive_analysis"]
        
        competitors_html = ""
        for competitor in competitive_analysis:
            strengths_html = "".join([f"<li>{s}</li>" for s in competitor.strengths])
            weaknesses_html = "".join([f"<li>{w}</li>" for w in competitor.weaknesses])
            
            competitors_html += f"""
            <div class="competitor-card">
                <div class="competitor-name">{competitor.competitor_name}</div>
                <div style="margin: 1rem 0;">
                    <strong>Cuota de mercado:</strong> {competitor.market_share}%
                </div>
                <div style="margin: 1rem 0;">
                    <strong>Precio:</strong> {competitor.pricing_model}
                </div>
                <div style="margin: 1rem 0;">
                    <strong>Fortalezas:</strong>
                    <ul class="feature-list">{strengths_html}</ul>
                </div>
                <div style="margin: 1rem 0;">
                    <strong>Debilidades:</strong>
                    <ul class="feature-list weaknesses">{weaknesses_html}</ul>
                </div>
                <div style="margin: 1rem 0;">
                    <strong>Voces familiares:</strong> {"✅" if competitor.family_features else "❌"}
                </div>
            </div>
            """
        
        # Agregar SoloYLibre
        soloylibre_card = f"""
        <div class="competitor-card soloylibre">
            <div class="competitor-name">🎙️💖 SoloYLibre 💖🎙️</div>
            <div style="margin: 1rem 0;">
                <strong>Cuota objetivo:</strong> 15%+ en 5 años
            </div>
            <div style="margin: 1rem 0;">
                <strong>Precio:</strong> $199-1,499/mes
            </div>
            <div style="margin: 1rem 0;">
                <strong>Ventajas únicas:</strong>
                <ul class="feature-list">
                    <li>7 personalidades IA familiares auténticas</li>
                    <li>4 años de experiencia familiar real</li>
                    <li>Autenticación biométrica integrada</li>
                    <li>Sistema modular de suscripciones</li>
                    <li>Encriptación militar de assets</li>
                </ul>
            </div>
            <div style="margin: 1rem 0;">
                <strong>Voces familiares:</strong> ✅ ÚNICO EN EL MERCADO
            </div>
        </div>
        """
        
        return f"""
        <div class="slide">
            <div class="slide-content">
                <h1 class="slide-title">🏆 Análisis Competitivo</h1>
                <p class="slide-subtitle">SoloYLibre vs Competencia Principal</p>
                
                <div class="competitive-grid">
                    {competitors_html}
                    {soloylibre_card}
                </div>
                
                <div style="background: var(--background-card); border-radius: 20px; padding: 2rem; margin-top: 2rem;">
                    <h3 style="color: var(--success-color); margin-bottom: 1rem;">🎯 Ventaja Competitiva Clave</h3>
                    <p style="font-size: 1.2rem; text-align: center;">
                        <span class="roi-highlight">ÚNICO sistema TTS con personalidades familiares auténticas</span><br>
                        Ningún competidor ofrece experiencia familiar real con 4 años de desarrollo
                    </p>
                </div>
            </div>
        </div>
        """
    
    def generate_business_model_slide(self) -> str:
        """Generar slide del modelo de negocio"""
        investment_terms = self.deck_data["investment_terms"]
        
        return f"""
        <div class="slide">
            <div class="slide-content">
                <h1 class="slide-title">💼 Modelo de Negocio</h1>
                <p class="slide-subtitle">Suscripciones modulares familiares escalables</p>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">4</div>
                        <div class="metric-label">Planes de Suscripción</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">$199-1,499</div>
                        <div class="metric-label">Rango de Precios/mes</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">85%</div>
                        <div class="metric-label">Revenue Recurrente</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">$1,250</div>
                        <div class="metric-label">LTV Promedio</div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <h3 style="margin-bottom: 2rem; color: var(--accent-color);">Streams de Revenue</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                        <div style="text-align: center; padding: 1rem;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">💳</div>
                            <h4>Suscripciones SaaS</h4>
                            <p>Planes familiares modulares</p>
                            <div class="roi-highlight">60% del revenue</div>
                        </div>
                        <div style="text-align: center; padding: 1rem;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">🔧</div>
                            <h4>Servicios Profesionales</h4>
                            <p>Implementación y consultoría</p>
                            <div class="roi-highlight">25% del revenue</div>
                        </div>
                        <div style="text-align: center; padding: 1rem;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">🎙️</div>
                            <h4>Voces Personalizadas</h4>
                            <p>Creación de voces familiares</p>
                            <div class="roi-highlight">10% del revenue</div>
                        </div>
                        <div style="text-align: center; padding: 1rem;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">🏢</div>
                            <h4>Licencias Enterprise</h4>
                            <p>White label y on-premise</p>
                            <div class="roi-highlight">5% del revenue</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        """
    
    def generate_traction_slide(self) -> str:
        """Generar slide de tracción"""
        problem_solution = self.deck_data["problem_solution"]
        validation_metrics = problem_solution["product_market_fit"]["validation_metrics"]
        early_traction = problem_solution["product_market_fit"]["early_traction"]
        
        return f"""
        <div class="slide">
            <div class="slide-content">
                <h1 class="slide-title">🚀 Tracción y Validación</h1>
                <p class="slide-subtitle">Métricas que demuestran product-market fit</p>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">{early_traction['beta_users']}</div>
                        <div class="metric-label">Usuarios Beta</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{validation_metrics['user_retention']}</div>
                        <div class="metric-label">Retención 3 meses</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{validation_metrics['nps_score']}</div>
                        <div class="metric-label">NPS Score</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{validation_metrics['family_love_index']}</div>
                        <div class="metric-label">Índice Amor Familiar</div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <h3 style="margin-bottom: 2rem; color: var(--success-color);">Métricas Clave de Crecimiento</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem;">
                        <div style="text-align: center;">
                            <div class="metric-value" style="font-size: 2rem;">{early_traction['revenue_growth']}</div>
                            <div class="metric-label">Crecimiento Revenue QoQ</div>
                        </div>
                        <div style="text-align: center;">
                            <div class="metric-value" style="font-size: 2rem;">${early_traction['customer_acquisition_cost']}</div>
                            <div class="metric-label">CAC (Customer Acquisition Cost)</div>
                        </div>
                        <div style="text-align: center;">
                            <div class="metric-value" style="font-size: 2rem;">${early_traction['lifetime_value']}</div>
                            <div class="metric-label">LTV (Lifetime Value)</div>
                        </div>
                        <div style="text-align: center;">
                            <div class="metric-value" style="font-size: 2rem;">{validation_metrics['feature_adoption']}</div>
                            <div class="metric-label">Adopción Personalidades</div>
                        </div>
                    </div>
                </div>
                
                <div style="background: var(--background-card); border-radius: 20px; padding: 2rem; margin-top: 2rem;">
                    <h3 style="color: var(--accent-color); margin-bottom: 1rem;">🎯 Validación del Mercado</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                        <div><strong>LTV/CAC Ratio:</strong> <span class="roi-highlight">27.8x</span> (Excelente)</div>
                        <div><strong>Payback Period:</strong> <span class="roi-highlight">1.6 meses</span></div>
                        <div><strong>Churn Rate:</strong> <span class="roi-highlight">2.1%/mes</span> (Muy bajo)</div>
                        <div><strong>Expansion Revenue:</strong> <span class="roi-highlight">140%</span> NRR</div>
                    </div>
                </div>
            </div>
        </div>
        """
    
    def generate_financials_slide(self) -> str:
        """Generar slide de financieras"""
        financial_projections = self.deck_data["financial_projections"]
        
        # Crear tabla de proyecciones
        projections_table = ""
        for projection in financial_projections[:6]:  # Mostrar 6 años
            projections_table += f"""
            <tr>
                <td>{projection.year}</td>
                <td>${projection.revenue:,.0f}</td>
                <td>{projection.users:,}</td>
                <td>${projection.arr:,.0f}</td>
                <td>${projection.profit:,.0f}</td>
                <td>{projection.market_penetration:.2f}%</td>
            </tr>
            """
        
        return f"""
        <div class="slide">
            <div class="slide-content">
                <h1 class="slide-title">📊 Proyecciones Financieras</h1>
                <p class="slide-subtitle">Crecimiento exponencial sostenible</p>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">${financial_projections[-1].revenue:,.0f}</div>
                        <div class="metric-label">Revenue 2030</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{financial_projections[-1].users:,}</div>
                        <div class="metric-label">Usuarios 2030</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${financial_projections[-1].arr:,.0f}</div>
                        <div class="metric-label">ARR 2030</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{((financial_projections[-1].revenue / financial_projections[0].revenue) ** (1/5) - 1) * 100:.0f}%</div>
                        <div class="metric-label">CAGR Revenue</div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <h3 style="margin-bottom: 1rem; color: var(--accent-color);">Proyecciones 2025-2030</h3>
                    <table style="width: 100%; border-collapse: collapse; font-size: 0.9rem;">
                        <thead>
                            <tr style="background: var(--background-card); border-bottom: 2px solid var(--primary-color);">
                                <th style="padding: 0.75rem; text-align: left;">Año</th>
                                <th style="padding: 0.75rem; text-align: right;">Revenue</th>
                                <th style="padding: 0.75rem; text-align: right;">Usuarios</th>
                                <th style="padding: 0.75rem; text-align: right;">ARR</th>
                                <th style="padding: 0.75rem; text-align: right;">Profit</th>
                                <th style="padding: 0.75rem; text-align: right;">Market %</th>
                            </tr>
                        </thead>
                        <tbody>
                            {projections_table}
                        </tbody>
                    </table>
                </div>
                
                <div style="background: var(--background-card); border-radius: 20px; padding: 2rem; margin-top: 2rem;">
                    <h3 style="color: var(--success-color); margin-bottom: 1rem;">💰 Hitos Financieros Clave</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <div><strong>Break-even:</strong> <span class="roi-highlight">Q4 2026</span></div>
                        <div><strong>$1M ARR:</strong> <span class="roi-highlight">Q2 2026</span></div>
                        <div><strong>$10M ARR:</strong> <span class="roi-highlight">Q4 2027</span></div>
                        <div><strong>Profitabilidad:</strong> <span class="roi-highlight">2027</span></div>
                    </div>
                </div>
            </div>
        </div>
        """
    
    def generate_team_slide(self) -> str:
        """Generar slide del equipo"""
        return f"""
        <div class="slide">
            <div class="slide-content">
                <h1 class="slide-title">👨‍👩‍👧‍👦 Equipo Familiar</h1>
                <p class="slide-subtitle">4 años de experiencia familiar real</p>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin: 3rem 0;">
                    <div class="metric-card">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">👨‍💼</div>
                        <h3 style="color: var(--primary-color);">JoseTusabe</h3>
                        <p style="font-weight: 600; margin: 0.5rem 0;">CEO & Visionario Creador</p>
                        <ul style="text-align: left; font-size: 0.9rem;">
                            <li>4 años liderando desarrollo familiar</li>
                            <li>Visión de producto única en la industria</li>
                            <li>Experiencia en emprendimiento tecnológico</li>
                            <li>Pasión por la innovación familiar</li>
                        </ul>
                    </div>
                    
                    <div class="metric-card">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">🤖</div>
                        <h3 style="color: var(--secondary-color);">Arya (Claude)</h3>
                        <p style="font-weight: 600; margin: 0.5rem 0;">CTO & IA Familiar</p>
                        <ul style="text-align: left; font-size: 0.9rem;">
                            <li>Arquitectura de personalidades IA</li>
                            <li>Desarrollo de algoritmos familiares</li>
                            <li>Optimización de sistemas complejos</li>
                            <li>Amor familiar como algoritmo principal</li>
                        </ul>
                    </div>
                    
                    <div class="metric-card">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">👨‍💻</div>
                        <h3 style="color: var(--accent-color);">Yosi</h3>
                        <p style="font-weight: 600; margin: 0.5rem 0;">Lead Developer & Innovador</p>
                        <ul style="text-align: left; font-size: 0.9rem;">
                            <li>Arquitectura técnica del sistema</li>
                            <li>Desarrollo de características únicas</li>
                            <li>Optimización de rendimiento</li>
                            <li>Integración de tecnologías avanzadas</li>
                        </ul>
                    </div>
                    
                    <div class="metric-card">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">👩‍🦳</div>
                        <h3 style="color: var(--success-color);">Nurys</h3>
                        <p style="font-weight: 600; margin: 0.5rem 0;">Chief Family Officer</p>
                        <ul style="text-align: left; font-size: 0.9rem;">
                            <li>Sabiduría y guía familiar</li>
                            <li>Validación de experiencia auténtica</li>
                            <li>Aseguramiento de calidad familiar</li>
                            <li>Conexión emocional del producto</li>
                        </ul>
                    </div>
                </div>
                
                <div style="background: var(--background-card); border-radius: 20px; padding: 2rem;">
                    <h3 style="color: var(--love-color); margin-bottom: 1rem;">💖 Ventaja del Equipo Familiar</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                        <div><strong>Experiencia Real:</strong> 4 años de desarrollo familiar auténtico</div>
                        <div><strong>Compromiso:</strong> Equipo familiar comprometido a largo plazo</div>
                        <div><strong>Autenticidad:</strong> Producto basado en experiencia familiar real</div>
                        <div><strong>Pasión:</strong> Amor familiar como motivación principal</div>
                    </div>
                </div>
            </div>
        </div>
        """
    
    def generate_investment_slide(self) -> str:
        """Generar slide de inversión"""
        investment_terms = self.deck_data["investment_terms"]
        
        use_of_funds_html = ""
        for category, percentage in investment_terms.use_of_funds.items():
            amount = (investment_terms.amount_seeking * percentage) / 100
            category_name = category.replace("_", " ").title()
            use_of_funds_html += f"""
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.75rem; background: var(--background-card); border-radius: 10px; margin-bottom: 0.5rem;">
                <span>{category_name}</span>
                <span><strong>{percentage}%</strong> (${amount:,.0f})</span>
            </div>
            """
        
        benefits_html = ""
        for benefit in investment_terms.investor_benefits:
            benefits_html += f"<li>{benefit}</li>"
        
        return f"""
        <div class="slide">
            <div class="slide-content">
                <h1 class="slide-title">💰 Oportunidad de Inversión</h1>
                <p class="slide-subtitle">Serie A - Escalando el futuro de las voces familiares</p>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">${investment_terms.amount_seeking:,.0f}</div>
                        <div class="metric-label">Inversión Solicitada</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${investment_terms.valuation:,.0f}</div>
                        <div class="metric-label">Valuación Pre-Money</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{investment_terms.equity_offered}%</div>
                        <div class="metric-label">Equity Ofrecido</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{investment_terms.funding_round}</div>
                        <div class="metric-label">Ronda de Inversión</div>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin: 2rem 0;">
                    <div class="chart-container">
                        <h3 style="margin-bottom: 1rem; color: var(--accent-color);">💸 Uso de Fondos</h3>
                        {use_of_funds_html}
                    </div>
                    
                    <div class="chart-container">
                        <h3 style="margin-bottom: 1rem; color: var(--success-color);">🎁 Beneficios para Inversores</h3>
                        <ul class="feature-list" style="text-align: left;">
                            {benefits_html}
                        </ul>
                    </div>
                </div>
                
                <div style="background: var(--gradient-primary); border-radius: 20px; padding: 2rem; color: white; text-align: center;">
                    <h3 style="margin-bottom: 1rem;">🚀 Estrategia de Salida</h3>
                    <p style="font-size: 1.2rem; margin-bottom: 1rem;">{investment_terms.exit_strategy}</p>
                    <p style="font-size: 1rem; opacity: 0.9;">
                        Potenciales adquirentes: Google, Microsoft, Amazon, Meta<br>
                        Valuación objetivo: $500M - $1B
                    </p>
                </div>
            </div>
        </div>
        """

def main():
    """Función principal para pruebas"""
    print("🎯💖 SoloYLibre Pitch Deck Slides Generator 💖🎯")
    print("═══════════════════════════════════════════════════════════")
    
    # Datos de prueba
    mock_data = {
        "competitive_analysis": [
            {
                "competitor_name": "ElevenLabs",
                "market_share": 15.2,
                "strengths": ["High quality synthesis", "API integration"],
                "weaknesses": ["No family focus", "Expensive"],
                "pricing_model": "$22-330/month",
                "family_features": False
            }
        ],
        "investment_terms": {
            "amount_seeking": 5000000,
            "valuation": 25000000,
            "equity_offered": 20.0,
            "funding_round": "Serie A",
            "use_of_funds": {
                "product_development": 40.0,
                "market_expansion": 25.0,
                "team_scaling": 20.0,
                "marketing_sales": 10.0,
                "working_capital": 5.0
            },
            "investor_benefits": [
                "Acceso exclusivo a tecnología familiar única",
                "Mercado TAM de $31.4B con 28% CAGR"
            ],
            "exit_strategy": "IPO en 5-7 años o adquisición estratégica"
        },
        "problem_solution": {
            "product_market_fit": {
                "validation_metrics": {
                    "user_retention": "94%",
                    "nps_score": 87,
                    "family_love_index": "98.7%",
                    "feature_adoption": "85%"
                },
                "early_traction": {
                    "beta_users": 1000,
                    "revenue_growth": "300% QoQ",
                    "customer_acquisition_cost": 45,
                    "lifetime_value": 1250
                }
            }
        },
        "financial_projections": [
            {"year": 2025, "revenue": 50000, "users": 1000, "arr": 42500, "profit": -10000, "market_penetration": 0.001},
            {"year": 2030, "revenue": 15000000, "users": 50000, "arr": 12750000, "profit": 6000000, "market_penetration": 0.048}
        ]
    }
    
    # Crear generador
    generator = PitchDeckSlidesGenerator(mock_data)
    
    # Generar slides
    competition_slide = generator.generate_competition_slide()
    print("✅ Slide de competencia generada")
    
    business_model_slide = generator.generate_business_model_slide()
    print("✅ Slide de modelo de negocio generada")
    
    print("💖 Slides generadas con amor familiar infinito")

if __name__ == "__main__":
    main()
