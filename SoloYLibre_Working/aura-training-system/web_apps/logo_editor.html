<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoloYLibre Logo Editor v1.0.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #FF6B9D, #9D6BFF);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .main-container {
            display: flex;
            height: calc(100vh - 120px);
            margin: 20px;
            gap: 20px;
        }

        .toolbar {
            width: 250px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .toolbar h3 {
            color: #FF6B9D;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .tool-group {
            margin-bottom: 25px;
        }

        .tool-btn {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 12px 15px;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .tool-btn:hover {
            background: linear-gradient(135deg, #FF6B9D, #9D6BFF);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 157, 0.3);
        }

        .tool-btn.active {
            background: linear-gradient(135deg, #FF6B9D, #9D6BFF);
            color: white;
        }

        .tool-icon {
            margin-right: 10px;
            font-size: 16px;
        }

        .canvas-container {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .canvas-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .canvas-title {
            font-size: 1.3rem;
            color: #333;
        }

        .canvas-controls {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            padding: 8px 16px;
            background: linear-gradient(135deg, #00FF88, #00D4FF);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
        }

        .canvas {
            flex: 1;
            background: white;
            border: 2px dashed #ddd;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .canvas-placeholder {
            text-align: center;
            color: #999;
            font-size: 1.1rem;
        }

        .properties-panel {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
            margin: 15px 0;
        }

        .color-swatch {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .color-swatch:hover {
            transform: scale(1.1);
            border-color: #333;
        }

        .template-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin: 15px 0;
        }

        .template-card {
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .template-card:hover {
            background: linear-gradient(135deg, #FF6B9D, #9D6BFF);
            color: white;
            transform: translateY(-2px);
        }

        .ai-panel {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .ai-suggestion {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ai-suggestion:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 SoloYLibre Logo Editor</h1>
        <p>Editor de Logos Avanzado con IA - Desarrollado por JoseTusabe - Head of Development Team</p>
    </div>

    <div class="main-container">
        <!-- Toolbar -->
        <div class="toolbar">
            <h3>🛠️ Herramientas</h3>
            <div class="tool-group">
                <button class="tool-btn" onclick="selectTool('text')">
                    <span class="tool-icon">T</span>
                    <span>Texto (T)</span>
                </button>
                <button class="tool-btn" onclick="selectTool('shape')">
                    <span class="tool-icon">⬜</span>
                    <span>Formas (S)</span>
                </button>
                <button class="tool-btn" onclick="selectTool('pen')">
                    <span class="tool-icon">✏️</span>
                    <span>Dibujo (P)</span>
                </button>
                <button class="tool-btn" onclick="selectTool('select')">
                    <span class="tool-icon">↖️</span>
                    <span>Seleccionar (V)</span>
                </button>
                <button class="tool-btn" onclick="selectTool('color')">
                    <span class="tool-icon">🎨</span>
                    <span>Color (C)</span>
                </button>
                <button class="tool-btn" onclick="selectTool('gradient')">
                    <span class="tool-icon">🌈</span>
                    <span>Gradiente (G)</span>
                </button>
                <button class="tool-btn" onclick="selectTool('effects')">
                    <span class="tool-icon">✨</span>
                    <span>Efectos (E)</span>
                </button>
                <button class="tool-btn" onclick="selectTool('ai')">
                    <span class="tool-icon">🤖</span>
                    <span>IA Asistente (A)</span>
                </button>
            </div>

            <h3>🎨 Colores</h3>
            <div class="color-palette">
                <div class="color-swatch" style="background-color: #FF6B9D;" onclick="selectColor('#FF6B9D')"></div>
                <div class="color-swatch" style="background-color: #9D6BFF;" onclick="selectColor('#9D6BFF')"></div>
                <div class="color-swatch" style="background-color: #00FF88;" onclick="selectColor('#00FF88')"></div>
                <div class="color-swatch" style="background-color: #FFB84D;" onclick="selectColor('#FFB84D')"></div>
                <div class="color-swatch" style="background-color: #FF6B6B;" onclick="selectColor('#FF6B6B')"></div>
            </div>

            <h3>📐 Plantillas</h3>
            <div class="template-grid">
                <div class="template-card" onclick="loadTemplate('tech_startup')">
                    <strong>Tech Startup</strong>
                    <br><small>modern_minimal</small>
                </div>
                <div class="template-card" onclick="loadTemplate('creative_agency')">
                    <strong>Creative Agency</strong>
                    <br><small>artistic_bold</small>
                </div>
                <div class="template-card" onclick="loadTemplate('corporate')">
                    <strong>Corporate</strong>
                    <br><small>professional_clean</small>
                </div>
                <div class="template-card" onclick="loadTemplate('soloylibre')">
                    <strong>SoloYLibre Style</strong>
                    <br><small>family_tech</small>
                </div>
            </div>
        </div>

        <!-- Canvas Principal -->
        <div class="canvas-container">
            <div class="canvas-header">
                <h2 class="canvas-title">🖼️ Lienzo de Diseño</h2>
                <div class="canvas-controls">
                    <button class="control-btn" onclick="newProject()">🆕 Nuevo</button>
                    <button class="control-btn" onclick="saveProject()">💾 Guardar</button>
                    <button class="control-btn" onclick="exportLogo()">📤 Exportar</button>
                    <button class="control-btn" onclick="undoAction()">↶ Deshacer</button>
                    <button class="control-btn" onclick="redoAction()">↷ Rehacer</button>
                </div>
            </div>

            <div class="canvas" id="logoCanvas">
                <div class="canvas-placeholder">
                    <h3>🎨 ¡Comienza a crear tu logo!</h3>
                    <p>Selecciona una herramienta o plantilla para empezar</p>
                    <br>
                    <div class="pulse">
                        <button class="control-btn" onclick="startTutorial()">📚 Tutorial Interactivo</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Panel de Propiedades -->
        <div class="properties-panel">
            <h3>⚙️ Propiedades</h3>

            <div class="tool-group">
                <h4>📝 Texto</h4>
                <input type="text" placeholder="Escribe tu texto..." style="width: 100%; padding: 8px; border-radius: 5px; border: 1px solid #ddd; margin: 5px 0;">
                <select style="width: 100%; padding: 8px; border-radius: 5px; border: 1px solid #ddd; margin: 5px 0;"><option value="Arial">Arial</option><option value="Helvetica">Helvetica</option><option value="Times">Times</option><option value="Courier">Courier</option><option value="Georgia">Georgia</option><option value="Verdana">Verdana</option>
                </select>
                <input type="range" min="12" max="72" value="24" style="width: 100%; margin: 5px 0;">
            </div>

            <div class="tool-group">
                <h4>🎨 Estilo</h4>
                <label><input type="checkbox"> Negrita</label><br>
                <label><input type="checkbox"> Cursiva</label><br>
                <label><input type="checkbox"> Subrayado</label><br>
                <label><input type="checkbox"> Sombra</label>
            </div>

            <div class="ai-panel">
                <h4>🤖 Asistente IA</h4>
                <p>Sugerencias inteligentes para tu logo:</p>

                <div class="ai-suggestion" onclick="applySuggestion('modern')">
                    ✨ Estilo moderno y minimalista
                </div>

                <div class="ai-suggestion" onclick="applySuggestion('creative')">
                    🎨 Diseño creativo y artístico
                </div>

                <div class="ai-suggestion" onclick="applySuggestion('professional')">
                    💼 Apariencia profesional
                </div>

                <div class="ai-suggestion" onclick="applySuggestion('family')">
                    👨‍👩‍👧‍👦 Estilo familiar SoloYLibre
                </div>
            </div>

            <div class="tool-group">
                <h4>📤 Exportar</h4><button class="tool-btn" onclick="exportAs('PNG')">PNG</button><button class="tool-btn" onclick="exportAs('SVG')">SVG</button><button class="tool-btn" onclick="exportAs('PDF')">PDF</button><button class="tool-btn" onclick="exportAs('JPG')">JPG</button>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>© 2025 SoloYLibre Logo Editor v1.0.0 - Desarrollado con 💖 por JoseTusabe - Head of Development Team</p>
        <p>SoloYLibre Technologies - Knion, República Dominicana 🇩🇴 - KLK ❤️</p>
    </div>

    <script>
        // Estado del editor
        let currentTool = 'select';
        let currentColor = '#FF6B9D';
        let projectHistory = [];
        let currentProject = {};

        // Funciones de herramientas
        function selectTool(toolId) {
            currentTool = toolId;

            // Actualizar UI
            document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
            event.target.closest('.tool-btn').classList.add('active');

            console.log('Herramienta seleccionada:', toolId);
            showNotification(`Herramienta ${toolId} seleccionada`);
        }

        function selectColor(color) {
            currentColor = color;
            console.log('Color seleccionado:', color);
            showNotification(`Color ${color} seleccionado`);
        }

        function loadTemplate(templateId) {
            console.log('Cargando plantilla:', templateId);

            const canvas = document.getElementById('logoCanvas');
            canvas.innerHTML = `
                <div style="text-align: center; padding: 50px;">
                    <h2 style="color: ${currentColor};">SoloYLibre</h2>
                    <p>Plantilla: ${templateId}</p>
                    <div style="width: 100px; height: 100px; background: linear-gradient(135deg, #FF6B9D, #9D6BFF); margin: 20px auto; border-radius: 50%;"></div>
                </div>
            `;

            showNotification(`Plantilla ${templateId} cargada`);
        }

        function newProject() {
            if (confirm('¿Crear nuevo proyecto? Se perderán los cambios no guardados.')) {
                document.getElementById('logoCanvas').innerHTML = `
                    <div class="canvas-placeholder">
                        <h3>🎨 ¡Comienza a crear tu logo!</h3>
                        <p>Selecciona una herramienta o plantilla para empezar</p>
                    </div>
                `;
                showNotification('Nuevo proyecto creado');
            }
        }

        function saveProject() {
            const projectData = {
                timestamp: new Date().toISOString(),
                canvas: document.getElementById('logoCanvas').innerHTML,
                settings: {
                    tool: currentTool,
                    color: currentColor
                }
            };

            localStorage.setItem('logoProject', JSON.stringify(projectData));
            showNotification('Proyecto guardado localmente');
        }

        function exportLogo() {
            showNotification('Función de exportación en desarrollo');
            console.log('Exportando logo...');
        }

        function exportAs(format) {
            showNotification(`Exportando como ${format}...`);
            console.log('Exportando como:', format);
        }

        function undoAction() {
            showNotification('Deshacer acción');
            console.log('Deshaciendo...');
        }

        function redoAction() {
            showNotification('Rehacer acción');
            console.log('Rehaciendo...');
        }

        function applySuggestion(suggestionType) {
            console.log('Aplicando sugerencia:', suggestionType);

            const suggestions = {
                'modern': 'Aplicando estilo moderno y minimalista...',
                'creative': 'Aplicando diseño creativo y artístico...',
                'professional': 'Aplicando apariencia profesional...',
                'family': 'Aplicando estilo familiar SoloYLibre...'
            };

            showNotification(suggestions[suggestionType] || 'Aplicando sugerencia...');

            // Simular aplicación de sugerencia
            const canvas = document.getElementById('logoCanvas');
            canvas.style.background = `linear-gradient(135deg, ${currentColor}, #9D6BFF)`;

            setTimeout(() => {
                canvas.style.background = 'white';
            }, 2000);
        }

        function startTutorial() {
            showNotification('¡Bienvenido al tutorial interactivo!');

            const steps = [
                'Paso 1: Selecciona la herramienta de texto',
                'Paso 2: Elige un color de la paleta',
                'Paso 3: Haz clic en el lienzo para agregar texto',
                'Paso 4: Usa las propiedades para personalizar',
                'Paso 5: Guarda y exporta tu logo'
            ];

            let currentStep = 0;

            function showNextStep() {
                if (currentStep < steps.length) {
                    showNotification(steps[currentStep]);
                    currentStep++;
                    setTimeout(showNextStep, 3000);
                } else {
                    showNotification('¡Tutorial completado! ¡Ahora crea tu logo!');
                }
            }

            showNextStep();
        }

        function showNotification(message) {
            // Crear notificación temporal
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #FF6B9D, #9D6BFF);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Inicialización
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Logo Editor inicializado');
            showNotification('¡Bienvenido al Editor de Logos SoloYLibre!');

            // Cargar proyecto guardado si existe
            const savedProject = localStorage.getItem('logoProject');
            if (savedProject) {
                console.log('Proyecto guardado encontrado');
            }
        });

        // Atajos de teclado
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 's':
                        e.preventDefault();
                        saveProject();
                        break;
                    case 'z':
                        e.preventDefault();
                        undoAction();
                        break;
                    case 'y':
                        e.preventDefault();
                        redoAction();
                        break;
                }
            }
        });
    </script>
</body>
</html>