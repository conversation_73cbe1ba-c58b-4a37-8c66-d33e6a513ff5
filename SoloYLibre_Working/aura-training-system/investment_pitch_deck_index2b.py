#!/usr/bin/env python3
"""
🎯 SoloYLibre Investment Pitch Deck - Index 2B: Pitch Deck Generator - Módulo 2
Generador de pitch deck HTML interactivo para inversores
💖 Dedicado con amor a nuestra familia
Version: 1.1.0 - Ultimate Update 2
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from investment_proposal_index2a import InvestmentProposalGenerator

logger = logging.getLogger(__name__)

class PitchDeckGenerator:
    """Generador de pitch deck interactivo para SoloYLibre"""
    
    def __init__(self):
        self.proposal_generator = InvestmentProposalGenerator()
        self.deck_data = self._prepare_deck_data()
        
    def _prepare_deck_data(self) -> Dict[str, Any]:
        """Preparar datos para el pitch deck"""
        executive_summary = self.proposal_generator.generate_executive_summary()
        problem_solution = self.proposal_generator.generate_problem_solution_fit()
        roi_projections = self.proposal_generator.calculate_roi_projections()
        
        return {
            "executive_summary": executive_summary,
            "problem_solution": problem_solution,
            "roi_projections": roi_projections,
            "market_data": self.proposal_generator.market_data,
            "competitive_analysis": self.proposal_generator.competitive_analysis,
            "financial_projections": self.proposal_generator.financial_projections,
            "investment_terms": self.proposal_generator.investment_terms
        }
    
    def generate_html_pitch_deck(self) -> str:
        """Generar pitch deck HTML completo"""
        html_content = f"""<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯💖 SoloYLibre Investment Pitch Deck 💖🎯</title>
    <style>
        {self._generate_css_styles()}
    </style>
</head>
<body>
    <div class="pitch-deck-container">
        {self._generate_navigation()}
        {self._generate_slide_content()}
        {self._generate_controls()}
    </div>
    
    <script>
        {self._generate_javascript()}
    </script>
</body>
</html>"""
        
        return html_content
    
    def _generate_css_styles(self) -> str:
        """Generar estilos CSS para el pitch deck"""
        return """
        /* Variables CSS para pitch deck */
        :root {
            --primary-color: #FF6B9D;
            --secondary-color: #9D6BFF;
            --accent-color: #6BFFF0;
            --success-color: #00FF88;
            --warning-color: #FFB84D;
            --background-dark: #0A0A0F;
            --background-card: rgba(255, 255, 255, 0.05);
            --text-primary: #FFFFFF;
            --text-secondary: rgba(255, 255, 255, 0.8);
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            --gradient-success: linear-gradient(135deg, var(--success-color), var(--accent-color));
            --shadow-glow: 0 8px 32px rgba(255, 107, 157, 0.4);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0A0A0F, #1A0A1F, #0F1A2A);
            color: var(--text-primary);
            overflow: hidden;
        }

        .pitch-deck-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 4rem;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .slide.prev {
            transform: translateX(-100%);
        }

        .slide-content {
            max-width: 1200px;
            width: 100%;
            text-align: center;
        }

        .slide-title {
            font-size: 4rem;
            font-weight: 900;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 2rem;
            animation: slideInUp 0.8s ease-out;
        }

        .slide-subtitle {
            font-size: 1.5rem;
            color: var(--text-secondary);
            margin-bottom: 3rem;
            animation: slideInUp 0.8s ease-out 0.2s both;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .metric-card {
            background: var(--background-card);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 107, 157, 0.3);
            animation: slideInUp 0.8s ease-out 0.4s both;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-glow);
        }

        .metric-value {
            font-size: 3rem;
            font-weight: 900;
            background: var(--gradient-success);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            font-size: 1.1rem;
            color: var(--text-secondary);
        }

        .chart-container {
            background: var(--background-card);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
            border: 1px solid rgba(255, 107, 157, 0.3);
        }

        .competitive-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .competitor-card {
            background: var(--background-card);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .competitor-card.soloylibre {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-glow);
        }

        .competitor-name {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .soloylibre .competitor-name {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .feature-list {
            list-style: none;
            margin: 1rem 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
        }

        .feature-list li:before {
            content: "✅";
            margin-right: 0.5rem;
        }

        .feature-list.weaknesses li:before {
            content: "❌";
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-dot.active {
            background: var(--primary-color);
            transform: scale(1.5);
        }

        .controls {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .control-btn {
            background: var(--gradient-primary);
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-glow);
        }

        .slide-number {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: var(--background-card);
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid rgba(255, 107, 157, 0.3);
            z-index: 1000;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .family-signature {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            font-size: 0.9rem;
            color: var(--text-secondary);
            z-index: 1000;
        }

        .roi-highlight {
            background: var(--gradient-success);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 900;
            font-size: 1.2em;
        }

        .investment-cta {
            background: var(--gradient-primary);
            border: none;
            border-radius: 50px;
            padding: 1.5rem 3rem;
            color: white;
            font-size: 1.3rem;
            font-weight: 700;
            cursor: pointer;
            margin-top: 2rem;
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }

        .investment-cta:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-glow);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @media (max-width: 768px) {
            .slide-title {
                font-size: 2.5rem;
            }
            
            .slide {
                padding: 2rem;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
        """
    
    def _generate_navigation(self) -> str:
        """Generar navegación del pitch deck"""
        slides = [
            "Portada", "Problema", "Solución", "Mercado", "Competencia", 
            "Modelo de Negocio", "Tracción", "Financieras", "Equipo", 
            "Inversión", "ROI", "Próximos Pasos"
        ]
        
        nav_dots = ""
        for i, slide in enumerate(slides):
            active_class = "active" if i == 0 else ""
            nav_dots += f'<div class="nav-dot {active_class}" data-slide="{i}" title="{slide}"></div>'
        
        return f"""
        <div class="navigation">
            {nav_dots}
        </div>
        <div class="slide-number">
            <span id="current-slide">1</span> / <span id="total-slides">{len(slides)}</span>
        </div>
        <div class="family-signature">
            💖 Creado con amor familiar infinito
        </div>
        """
    
    def _generate_slide_content(self) -> str:
        """Generar contenido de todas las slides"""
        slides_html = ""
        
        # Slide 1: Portada
        slides_html += self._generate_title_slide()
        
        # Slide 2: Problema
        slides_html += self._generate_problem_slide()
        
        # Slide 3: Solución
        slides_html += self._generate_solution_slide()
        
        # Slide 4: Mercado
        slides_html += self._generate_market_slide()
        
        # Slide 5: Competencia
        slides_html += self._generate_competition_slide()
        
        # Slide 6: Modelo de Negocio
        slides_html += self._generate_business_model_slide()
        
        # Slide 7: Tracción
        slides_html += self._generate_traction_slide()
        
        # Slide 8: Financieras
        slides_html += self._generate_financials_slide()
        
        # Slide 9: Equipo
        slides_html += self._generate_team_slide()
        
        # Slide 10: Inversión
        slides_html += self._generate_investment_slide()
        
        # Slide 11: ROI
        slides_html += self._generate_roi_slide()
        
        # Slide 12: Próximos Pasos
        slides_html += self._generate_next_steps_slide()
        
        return slides_html
    
    def _generate_title_slide(self) -> str:
        """Generar slide de portada"""
        exec_summary = self.deck_data["executive_summary"]
        
        return f"""
        <div class="slide active">
            <div class="slide-content">
                <h1 class="slide-title">🎙️💖 SoloYLibre 💖🎙️</h1>
                <p class="slide-subtitle">{exec_summary['company_overview']['tagline']}</p>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">{exec_summary['market_opportunity']['projected_2030']}</div>
                        <div class="metric-label">Mercado TAM 2030</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{exec_summary['market_opportunity']['growth_rate']}</div>
                        <div class="metric-label">CAGR</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{exec_summary['investment_ask']['amount']}</div>
                        <div class="metric-label">Inversión Solicitada</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{exec_summary['company_overview']['family_love_factor']}%</div>
                        <div class="metric-label">Amor Familiar</div>
                    </div>
                </div>
                
                <p style="font-size: 1.2rem; color: var(--text-secondary); margin-top: 2rem;">
                    El primer sistema TTS con alma familiar del mundo<br>
                    4 años de experiencia familiar real • 7 personalidades únicas • Tecnología propietaria
                </p>
            </div>
        </div>
        """
    
    def _generate_problem_slide(self) -> str:
        """Generar slide del problema"""
        problem_data = self.deck_data["problem_solution"]["problem_statement"]
        
        pain_points_html = ""
        for point in problem_data["pain_points"]:
            pain_points_html += f"<li>{point}</li>"
        
        return f"""
        <div class="slide">
            <div class="slide-content">
                <h1 class="slide-title">🚨 El Problema</h1>
                <p class="slide-subtitle">{problem_data['description']}</p>
                
                <div class="chart-container">
                    <h3 style="margin-bottom: 2rem; color: var(--primary-color);">Puntos de Dolor Identificados</h3>
                    <ul class="feature-list" style="text-align: left; max-width: 800px; margin: 0 auto;">
                        {pain_points_html}
                    </ul>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">{problem_data['market_validation']['surveys_conducted']}</div>
                        <div class="metric-label">Encuestas Realizadas</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{problem_data['market_validation']['pain_point_confirmation']}</div>
                        <div class="metric-label">Confirman el Problema</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{problem_data['market_validation']['willingness_to_pay_premium']}</div>
                        <div class="metric-label">Pagarían Premium</div>
                    </div>
                </div>
            </div>
        </div>
        """
    
    def _generate_solution_slide(self) -> str:
        """Generar slide de la solución"""
        solution_data = self.deck_data["problem_solution"]["solution_overview"]
        
        features_html = ""
        for feature in solution_data["key_features"]:
            features_html += f"<li>{feature}</li>"
        
        return f"""
        <div class="slide">
            <div class="slide-content">
                <h1 class="slide-title">💡 Nuestra Solución</h1>
                <p class="slide-subtitle">{solution_data['core_innovation']}</p>
                
                <div class="chart-container">
                    <h3 style="margin-bottom: 2rem; color: var(--success-color);">Características Clave</h3>
                    <ul class="feature-list" style="text-align: left; max-width: 800px; margin: 0 auto;">
                        {features_html}
                    </ul>
                </div>
                
                <div style="background: var(--background-card); border-radius: 20px; padding: 2rem; margin-top: 2rem;">
                    <h3 style="color: var(--accent-color); margin-bottom: 1rem;">Stack Tecnológico</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <div><strong>IA Engine:</strong> {solution_data['technology_stack']['ai_engine']}</div>
                        <div><strong>Síntesis:</strong> {solution_data['technology_stack']['voice_synthesis']}</div>
                        <div><strong>Seguridad:</strong> {solution_data['technology_stack']['security']}</div>
                        <div><strong>Arquitectura:</strong> {solution_data['technology_stack']['architecture']}</div>
                    </div>
                </div>
            </div>
        </div>
        """
    
    def _generate_market_slide(self) -> str:
        """Generar slide del mercado"""
        market_data = self.deck_data["market_data"]
        
        trends_html = ""
        for trend in market_data.market_trends:
            trends_html += f"<li>{trend}</li>"
        
        return f"""
        <div class="slide">
            <div class="slide-content">
                <h1 class="slide-title">📈 Oportunidad de Mercado</h1>
                <p class="slide-subtitle">Mercado TTS en crecimiento explosivo</p>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">${market_data.total_market_size_2025}B</div>
                        <div class="metric-label">Mercado Actual (2025)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${market_data.projected_market_size_2030}B</div>
                        <div class="metric-label">Proyección 2030</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{market_data.annual_growth_rate}%</div>
                        <div class="metric-label">CAGR</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${market_data.family_segment_opportunity}B</div>
                        <div class="metric-label">Segmento Familiar TAM</div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <h3 style="margin-bottom: 2rem; color: var(--accent-color);">Tendencias Clave del Mercado</h3>
                    <ul class="feature-list" style="text-align: left; max-width: 800px; margin: 0 auto;">
                        {trends_html}
                    </ul>
                </div>
            </div>
        </div>
        """
    
    def _generate_controls(self) -> str:
        """Generar controles del pitch deck"""
        return """
        <div class="controls">
            <button class="control-btn" id="prev-btn">← Anterior</button>
            <button class="control-btn" id="next-btn">Siguiente →</button>
        </div>
        """
    
    def _generate_javascript(self) -> str:
        """Generar JavaScript para interactividad"""
        return """
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const navDots = document.querySelectorAll('.nav-dot');
        const totalSlides = slides.length;
        
        document.getElementById('total-slides').textContent = totalSlides;
        
        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.remove('active', 'prev');
                if (i === index) {
                    slide.classList.add('active');
                } else if (i < index) {
                    slide.classList.add('prev');
                }
            });
            
            navDots.forEach((dot, i) => {
                dot.classList.toggle('active', i === index);
            });
            
            document.getElementById('current-slide').textContent = index + 1;
            currentSlide = index;
        }
        
        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            }
        }
        
        function prevSlide() {
            if (currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }
        
        // Event listeners
        document.getElementById('next-btn').addEventListener('click', nextSlide);
        document.getElementById('prev-btn').addEventListener('click', prevSlide);
        
        navDots.forEach((dot, index) => {
            dot.addEventListener('click', () => showSlide(index));
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                e.preventDefault();
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                e.preventDefault();
                prevSlide();
            }
        });
        
        // Auto-advance (opcional)
        let autoAdvance = false;
        if (autoAdvance) {
            setInterval(() => {
                if (currentSlide < totalSlides - 1) {
                    nextSlide();
                } else {
                    showSlide(0);
                }
            }, 10000); // 10 segundos por slide
        }
        """

def main():
    """Función principal para pruebas"""
    print("🎯💖 SoloYLibre Pitch Deck Generator 💖🎯")
    print("═══════════════════════════════════════════════════════════")
    
    # Crear generador de pitch deck
    generator = PitchDeckGenerator()
    
    # Generar HTML del pitch deck
    html_content = generator.generate_html_pitch_deck()
    
    # Guardar archivo
    output_path = Path("soloylibre_investment_pitch_deck.html")
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Pitch deck generado: {output_path}")
    print(f"🌐 Abrir en navegador para presentar a inversores")
    print(f"💖 Creado con amor familiar infinito")

if __name__ == "__main__":
    main()
