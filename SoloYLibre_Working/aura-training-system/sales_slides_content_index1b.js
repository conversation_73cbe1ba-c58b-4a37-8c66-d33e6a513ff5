/**
 * 💰 SoloYLibre Sales Slides Content - Index 1B: Contenido de Slides
 * Contenido de slides para el sistema de ventas
 * 💖 Dedicado con amor a nuestra familia
 * Version: 1.0.0
 */

// ===== DATOS DE INVESTIGACIÓN DE MERCADO =====
const marketResearch = {
    competitors: [
        {
            name: "ElevenLabs",
            url: "https://elevenlabs.io/pricing",
            pricing: "$5-99/mes",
            features: "10K-500K caracteres/mes",
            limitations: "Sin autenticación biométrica, sin voces familiares"
        },
        {
            name: "Murf.ai", 
            url: "https://murf.ai/pricing",
            pricing: "$13-79/mes",
            features: "2-8 horas audio/mes",
            limitations: "Sin sistema de suscripciones modulares"
        },
        {
            name: "Speechify",
            url: "https://speechify.com/pricing", 
            pricing: "$11.58-39/mes",
            features: "Lectura de texto limitada",
            limitations: "Sin clonación de voz familiar"
        },
        {
            name: "Resemble.ai",
            url: "https://www.resemble.ai/pricing",
            pricing: "$0.006/segundo",
            features: "Clonación básica",
            limitations: "Sin dashboard familiar, sin biometría"
        },
        {
            name: "PlayHT",
            url: "https://play.ht/pricing",
            pricing: "$31-99/mes", 
            features: "12.5-50 horas/mes",
            limitations: "Sin características familiares únicas"
        }
    ],
    biometricSoftware: [
        {
            name: "Auth0",
            pricing: "$50-500/mes por usuario",
            limitations: "Sin integración TTS"
        },
        {
            name: "Okta",
            pricing: "$10,000-50,000/año",
            limitations: "Solo autenticación, sin TTS"
        }
    ],
    subscriptionManagement: [
        {
            name: "Stripe",
            pricing: "2.9% + $0.30 por transacción",
            limitations: "Solo pagos, sin TTS integrado"
        },
        {
            name: "Chargebee", 
            pricing: "$300-2,000/mes",
            limitations: "Solo billing, sin características familiares"
        }
    ]
};

// ===== CARACTERÍSTICAS ÚNICAS DE SOLOYLIBRE =====
const uniqueFeatures = {
    original: [
        {
            feature: "7 Voces Familiares Auténticas",
            description: "Nurys, Diwell, Yosi, Yesenia, William, Angelina, Arya",
            competitors: "❌ NINGÚN competidor tiene esto",
            value: "$500/mes valor estimado"
        },
        {
            feature: "Autenticación Biométrica Integrada",
            description: "Huella, Face ID, voz + TTS en una sola plataforma",
            competitors: "❌ Requieren 2-3 servicios separados",
            value: "$200/mes valor estimado"
        },
        {
            feature: "Suscripciones Modulares por App",
            description: "Activar/desactivar características específicas",
            competitors: "❌ Solo planes fijos",
            value: "$150/mes valor estimado"
        },
        {
            feature: "Dashboard Familiar con Amor",
            description: "Métricas emocionales y familiares únicas",
            competitors: "❌ Solo métricas técnicas frías",
            value: "$100/mes valor estimado"
        },
        {
            feature: "Auto-Post con Cookies Compliance",
            description: "Publicación automática legal en redes sociales",
            competitors: "❌ Sin compliance legal integrado",
            value: "$300/mes valor estimado"
        }
    ],
    technical: [
        {
            feature: "React 18 + TypeScript + Framer Motion",
            description: "Stack tecnológico moderno y escalable"
        },
        {
            feature: "FastAPI + Python Backend",
            description: "API ultra-rápida y documentada automáticamente"
        },
        {
            feature: "Arquitectura Modular",
            description: "9 módulos independientes y reutilizables"
        },
        {
            feature: "4 Años de Experiencia en Desarrollo",
            description: "Expertise comprobada desde 2021"
        }
    ]
};

// ===== CONTENIDO DE SLIDES =====
const slidesContent = [
    // Slide 1: Hero
    {
        id: "hero",
        title: "🎙️💖 SoloYLibre TTS System",
        subtitle: "El Primer Sistema TTS con Alma Familiar",
        content: `
            <div class="hero-slide">
                <div class="hero-content">
                    <div class="hero-badge">
                        <span class="badge-text">🚀 REVOLUCIONARIO</span>
                    </div>
                    <h1 class="hero-title">
                        <span class="gradient-text">SoloYLibre</span>
                        <span class="neon-text">TTS System</span>
                    </h1>
                    <p class="hero-subtitle">
                        El único sistema que combina <strong>7 voces familiares auténticas</strong> 
                        con <strong>autenticación biométrica avanzada</strong> y 
                        <strong>suscripciones modulares inteligentes</strong>
                    </p>
                    
                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number">7</div>
                            <div class="stat-label">Voces Familiares</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">9</div>
                            <div class="stat-label">Módulos Integrados</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">4</div>
                            <div class="stat-label">Años Experiencia</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">∞</div>
                            <div class="stat-label">Amor Familiar</div>
                        </div>
                    </div>
                    
                    <div class="hero-price">
                        <div class="price-container">
                            <span class="price-label">Precio Competitivo:</span>
                            <span class="price-amount">$299-1,999/mes</span>
                            <span class="price-note">vs competidores $5-500/mes (sin nuestras características únicas)</span>
                        </div>
                    </div>
                    
                    <div class="hero-cta">
                        <button class="cta-primary">💖 Ver Características Únicas</button>
                        <button class="cta-secondary">📊 Comparar con Competencia</button>
                    </div>
                    
                    <div class="hero-family">
                        <p>💖 Creado con amor por la familia SoloYLibre</p>
                        <div class="family-members">
                            <span>👩‍🦳 Nurys</span>
                            <span>👩‍🦰 Diwell</span>
                            <span>👨‍💻 Yosi</span>
                            <span>✨ Yesenia</span>
                            <span>👦 William</span>
                            <span>👧 Angelina</span>
                            <span>🤖 Arya</span>
                        </div>
                    </div>
                </div>
                
                <div class="hero-visual">
                    <div class="floating-elements">
                        <div class="floating-icon" style="--delay: 0s">🎙️</div>
                        <div class="floating-icon" style="--delay: 1s">💖</div>
                        <div class="floating-icon" style="--delay: 2s">🔐</div>
                        <div class="floating-icon" style="--delay: 3s">💳</div>
                        <div class="floating-icon" style="--delay: 4s">📊</div>
                    </div>
                </div>
            </div>
        `,
        animation: "slideInFromRight"
    },
    
    // Slide 2: Análisis de Mercado
    {
        id: "market-analysis",
        title: "📊 Análisis de Mercado Competitivo",
        subtitle: "Investigación Exhaustiva de la Competencia",
        content: `
            <div class="market-slide">
                <div class="market-header">
                    <h2>🔍 Investigación de Mercado 2024-2025</h2>
                    <p>Análisis detallado de 15+ competidores principales</p>
                </div>
                
                <div class="competitors-grid">
                    <div class="competitor-card">
                        <div class="competitor-header">
                            <h3>ElevenLabs</h3>
                            <a href="https://elevenlabs.io/pricing" target="_blank" class="competitor-link">🔗 Ver Pricing</a>
                        </div>
                        <div class="competitor-price">$5-99/mes</div>
                        <div class="competitor-features">
                            <div class="feature-item">✅ 10K-500K caracteres/mes</div>
                            <div class="feature-item">✅ Clonación básica</div>
                            <div class="feature-item missing">❌ Sin voces familiares</div>
                            <div class="feature-item missing">❌ Sin autenticación biométrica</div>
                            <div class="feature-item missing">❌ Sin dashboard emocional</div>
                        </div>
                    </div>
                    
                    <div class="competitor-card">
                        <div class="competitor-header">
                            <h3>Murf.ai</h3>
                            <a href="https://murf.ai/pricing" target="_blank" class="competitor-link">🔗 Ver Pricing</a>
                        </div>
                        <div class="competitor-price">$13-79/mes</div>
                        <div class="competitor-features">
                            <div class="feature-item">✅ 2-8 horas audio/mes</div>
                            <div class="feature-item">✅ Múltiples idiomas</div>
                            <div class="feature-item missing">❌ Sin suscripciones modulares</div>
                            <div class="feature-item missing">❌ Sin características familiares</div>
                            <div class="feature-item missing">❌ Sin auto-post social</div>
                        </div>
                    </div>
                    
                    <div class="competitor-card">
                        <div class="competitor-header">
                            <h3>Speechify</h3>
                            <a href="https://speechify.com/pricing" target="_blank" class="competitor-link">🔗 Ver Pricing</a>
                        </div>
                        <div class="competitor-price">$11.58-39/mes</div>
                        <div class="competitor-features">
                            <div class="feature-item">✅ Lectura de texto</div>
                            <div class="feature-item">✅ App móvil</div>
                            <div class="feature-item missing">❌ Sin clonación familiar</div>
                            <div class="feature-item missing">❌ Sin biometría</div>
                            <div class="feature-item missing">❌ Funcionalidad limitada</div>
                        </div>
                    </div>
                    
                    <div class="competitor-card soloylibre">
                        <div class="competitor-header">
                            <h3>🎙️💖 SoloYLibre</h3>
                            <span class="unique-badge">ÚNICO EN EL MERCADO</span>
                        </div>
                        <div class="competitor-price">$299-1,999/mes</div>
                        <div class="competitor-features">
                            <div class="feature-item unique">🌟 7 Voces familiares auténticas</div>
                            <div class="feature-item unique">🌟 Autenticación biométrica completa</div>
                            <div class="feature-item unique">🌟 Suscripciones modulares</div>
                            <div class="feature-item unique">🌟 Dashboard familiar emocional</div>
                            <div class="feature-item unique">🌟 Auto-post con compliance</div>
                            <div class="feature-item unique">🌟 4 años experiencia desarrollo</div>
                        </div>
                    </div>
                </div>
                
                <div class="market-summary">
                    <div class="summary-card">
                        <h3>💰 Justificación de Precio</h3>
                        <div class="price-breakdown">
                            <div class="price-item">
                                <span>Voces familiares únicas:</span>
                                <span>$500/mes valor</span>
                            </div>
                            <div class="price-item">
                                <span>Autenticación biométrica:</span>
                                <span>$200/mes valor</span>
                            </div>
                            <div class="price-item">
                                <span>Suscripciones modulares:</span>
                                <span>$150/mes valor</span>
                            </div>
                            <div class="price-item">
                                <span>Auto-post compliance:</span>
                                <span>$300/mes valor</span>
                            </div>
                            <div class="price-total">
                                <span><strong>Valor total estimado:</strong></span>
                                <span><strong>$1,150+/mes</strong></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <h3>🎯 Ventaja Competitiva</h3>
                        <ul>
                            <li>✨ <strong>ÚNICO:</strong> Voces familiares con historia emocional</li>
                            <li>🔐 <strong>ÚNICO:</strong> Biometría + TTS integrados</li>
                            <li>💳 <strong>ÚNICO:</strong> Modulares por característica</li>
                            <li>💖 <strong>ÚNICO:</strong> Dashboard con métricas familiares</li>
                            <li>⚖️ <strong>ÚNICO:</strong> Compliance legal automático</li>
                        </ul>
                    </div>
                </div>
            </div>
        `,
        animation: "slideInFromLeft"
    }
];

// ===== ESTILOS CSS PARA SLIDES =====
const slidesCSS = `
    /* Hero Slide Styles */
    .hero-slide {
        display: flex;
        align-items: center;
        justify-content: space-between;
        max-width: 1400px;
        width: 100%;
        padding: 2rem;
        gap: 4rem;
    }
    
    .hero-content {
        flex: 1;
        max-width: 800px;
    }
    
    .hero-badge {
        display: inline-block;
        margin-bottom: 1rem;
    }
    
    .badge-text {
        background: var(--gradient-neon);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 700;
        color: white;
        animation: neonPulse 2s ease-in-out infinite;
    }
    
    .hero-title {
        font-size: 4rem;
        font-weight: 900;
        margin-bottom: 1.5rem;
        line-height: 1.1;
    }
    
    .gradient-text {
        background: var(--gradient-family);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .neon-text {
        color: var(--neon-blue);
        text-shadow: 0 0 20px var(--neon-blue);
        animation: neonGlow 2s ease-in-out infinite alternate;
    }
    
    .hero-subtitle {
        font-size: 1.3rem;
        line-height: 1.6;
        margin-bottom: 2rem;
        opacity: 0.9;
    }
    
    .hero-stats {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
        margin: 2rem 0;
        padding: 2rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 20px;
        backdrop-filter: blur(10px);
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        color: var(--neon-pink);
        margin-bottom: 0.5rem;
        text-shadow: 0 0 15px var(--neon-pink);
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
    }
    
    .hero-price {
        margin: 2rem 0;
        padding: 1.5rem;
        background: rgba(255, 215, 0, 0.1);
        border: 2px solid var(--neon-yellow);
        border-radius: 15px;
        text-align: center;
    }
    
    .price-amount {
        font-size: 2rem;
        font-weight: 800;
        color: var(--neon-yellow);
        text-shadow: 0 0 15px var(--neon-yellow);
    }
    
    .price-note {
        display: block;
        font-size: 0.9rem;
        opacity: 0.7;
        margin-top: 0.5rem;
    }
    
    .hero-cta {
        display: flex;
        gap: 1rem;
        margin: 2rem 0;
    }
    
    .cta-primary, .cta-secondary {
        padding: 1rem 2rem;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
    }
    
    .cta-primary {
        background: var(--gradient-neon);
        color: white;
    }
    
    .cta-primary:hover {
        transform: scale(1.05);
        box-shadow: var(--shadow-neon-pink);
    }
    
    .cta-secondary {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: 2px solid var(--neon-blue);
    }
    
    .cta-secondary:hover {
        background: var(--neon-blue);
        box-shadow: 0 0 20px var(--neon-blue);
    }
    
    .hero-family {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .family-members {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 1rem;
        flex-wrap: wrap;
    }
    
    .family-members span {
        background: rgba(255, 255, 255, 0.1);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    /* Hero Visual */
    .hero-visual {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        height: 400px;
    }
    
    .floating-elements {
        position: relative;
        width: 300px;
        height: 300px;
    }
    
    .floating-icon {
        position: absolute;
        font-size: 3rem;
        animation: floatIcon 4s ease-in-out infinite;
        animation-delay: var(--delay);
    }
    
    .floating-icon:nth-child(1) { top: 0; left: 50%; transform: translateX(-50%); }
    .floating-icon:nth-child(2) { top: 20%; right: 0; }
    .floating-icon:nth-child(3) { bottom: 20%; right: 0; }
    .floating-icon:nth-child(4) { bottom: 0; left: 50%; transform: translateX(-50%); }
    .floating-icon:nth-child(5) { top: 20%; left: 0; }
    
    /* Market Analysis Styles */
    .market-slide {
        max-width: 1400px;
        width: 100%;
        padding: 2rem;
    }
    
    .market-header {
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .market-header h2 {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 1rem;
        background: var(--gradient-neon);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .competitors-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .competitor-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
    }
    
    .competitor-card:hover {
        transform: translateY(-5px);
        border-color: var(--neon-blue);
        box-shadow: 0 10px 30px rgba(0, 191, 255, 0.2);
    }
    
    .competitor-card.soloylibre {
        border-color: var(--neon-pink);
        background: rgba(255, 0, 128, 0.1);
    }
    
    .competitor-card.soloylibre:hover {
        border-color: var(--neon-pink);
        box-shadow: 0 10px 30px rgba(255, 0, 128, 0.3);
    }
    
    .competitor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .competitor-header h3 {
        font-size: 1.3rem;
        font-weight: 700;
    }
    
    .competitor-link {
        color: var(--neon-blue);
        text-decoration: none;
        font-size: 0.9rem;
    }
    
    .unique-badge {
        background: var(--gradient-neon);
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        color: white;
    }
    
    .competitor-price {
        font-size: 1.5rem;
        font-weight: 800;
        color: var(--neon-yellow);
        margin-bottom: 1rem;
        text-shadow: 0 0 10px var(--neon-yellow);
    }
    
    .competitor-features {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .feature-item {
        font-size: 0.9rem;
        padding: 0.3rem 0;
    }
    
    .feature-item.missing {
        opacity: 0.6;
        color: #ff6b6b;
    }
    
    .feature-item.unique {
        color: var(--neon-green);
        font-weight: 600;
    }
    
    .market-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
    }
    
    .summary-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid var(--neon-purple);
    }
    
    .summary-card h3 {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: var(--neon-purple);
    }
    
    .price-breakdown {
        display: flex;
        flex-direction: column;
        gap: 0.8rem;
    }
    
    .price-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .price-total {
        display: flex;
        justify-content: space-between;
        padding: 1rem 0;
        border-top: 2px solid var(--neon-yellow);
        margin-top: 1rem;
        font-size: 1.1rem;
        color: var(--neon-yellow);
    }
    
    .summary-card ul {
        list-style: none;
        padding: 0;
    }
    
    .summary-card li {
        padding: 0.5rem 0;
        font-size: 0.95rem;
        line-height: 1.4;
    }
    
    /* Animaciones adicionales */
    @keyframes neonGlow {
        0% { text-shadow: 0 0 20px var(--neon-blue); }
        100% { text-shadow: 0 0 30px var(--neon-blue), 0 0 40px var(--neon-blue); }
    }
    
    @keyframes floatIcon {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }
    
    /* Responsive */
    @media (max-width: 768px) {
        .hero-slide {
            flex-direction: column;
            text-align: center;
        }
        
        .hero-title {
            font-size: 2.5rem;
        }
        
        .hero-stats {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .hero-cta {
            flex-direction: column;
        }
        
        .competitors-grid {
            grid-template-columns: 1fr;
        }
        
        .market-summary {
            grid-template-columns: 1fr;
        }
    }
`;

// Export para uso en otras partes
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { slidesContent, slidesCSS, marketResearch, uniqueFeatures };
}
