{"name": "queue-service", "version": "1.0.0", "description": "Background job processing and queue management service", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "worker": "node dist/worker.js", "test": "jest", "lint": "eslint src/**/*.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bull": "^4.12.0", "bull-board": "^2.1.3", "redis": "^4.6.12", "pg": "^8.11.3", "mongodb": "^6.3.0", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "aws-sdk": "^2.1498.0", "sharp": "^0.33.1", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "pdf-lib": "^1.17.1", "puppeteer": "^21.6.1", "node-cron": "^3.0.2", "agenda": "^5.0.0", "ioredis": "^5.3.2", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "prom-client": "^15.1.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.5", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/pg": "^8.10.9", "@types/nodemailer": "^6.4.14", "@types/fluent-ffmpeg": "^2.1.24", "@types/node-cron": "^3.0.11", "@types/compression": "^1.7.5", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "@types/jest": "^29.5.11"}}