{"name": "analytics-service", "version": "1.0.0", "description": "Advanced analytics and business intelligence service", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "lint": "eslint src/**/*.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "redis": "^4.6.12", "clickhouse": "^2.6.0", "node-cron": "^3.0.2", "mixpanel": "^0.17.0", "segment-analytics-node": "^1.0.0", "google-analytics-data": "^4.0.0", "facebook-business-sdk": "^18.0.0", "stripe": "^14.9.0", "mailchimp-api-v3": "^1.15.0", "twilio": "^4.19.0", "pusher": "^5.1.3", "chart.js": "^4.4.0", "d3": "^7.8.5", "moment": "^2.29.4", "lodash": "^4.17.21", "csv-parser": "^3.0.0", "json2csv": "^6.1.0", "prom-client": "^15.1.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.5", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/pg": "^8.10.9", "@types/lodash": "^4.14.202", "@types/node-cron": "^3.0.11", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "@types/jest": "^29.5.11"}}