{"name": "@soloylibre/dating-api", "version": "2.0.0", "description": "SoloYLibre Dating API - Backend service for dating application with matching algorithms", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "socket.io": "^4.7.4", "redis": "^4.6.11", "pg": "^8.11.3", "typeorm": "^0.3.17", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "axios": "^1.6.2", "uuid": "^9.0.1", "moment": "^2.29.4", "geolib": "^3.3.4", "ml-matrix": "^6.10.7", "simple-statistics": "^7.8.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/pg": "^8.10.9", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/jest": "^29.5.8", "typescript": "^5.3.3", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^6.0.2", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/server.ts"]}, "engines": {"node": ">=18.0.0"}}