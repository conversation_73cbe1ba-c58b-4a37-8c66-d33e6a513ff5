import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config/config';
import { redisUtils } from '../config/redis';
import { pool } from '../config/database';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    email: string;
    username: string;
    isAdmin: boolean;
    application: string;
    permissions: any;
  };
}

export async function authMiddleware(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Access token required' });
    }

    const token = authHeader.substring(7);

    try {
      const decoded = jwt.verify(token, config.jwt.secret) as any;
      
      // Check if user session exists in Redis
      const session = await redisUtils.getUserSession(decoded.userId);
      if (!session) {
        return res.status(401).json({ error: 'Session expired' });
      }

      // Verify user is still active
      const userResult = await pool.query(
        'SELECT id, email, username, is_admin, is_active FROM users WHERE id = $1',
        [decoded.userId]
      );

      if (userResult.rows.length === 0 || !userResult.rows[0].is_active) {
        return res.status(401).json({ error: 'User not found or inactive' });
      }

      const user = userResult.rows[0];

      // Extend session in Redis
      await redisUtils.setUserSession(decoded.userId, session, 15 * 60); // 15 minutes

      req.user = {
        id: user.id,
        email: user.email,
        username: user.username,
        isAdmin: user.is_admin,
        application: decoded.application,
        permissions: decoded.permissions
      };

      next();
    } catch (jwtError) {
      return res.status(401).json({ error: 'Invalid access token' });
    }
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

export function requirePermission(resource: string, action: string) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userPermissions = req.user.permissions;
    
    // Admin users have all permissions
    if (req.user.isAdmin) {
      return next();
    }

    // Check if user has the required permission
    if (!userPermissions[resource] || !userPermissions[resource].includes(action)) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        required: `${resource}:${action}`
      });
    }

    next();
  };
}

export function requireAdmin(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  if (!req.user.isAdmin) {
    return res.status(403).json({ error: 'Admin access required' });
  }

  next();
}

export function requireApplication(applications: string[]) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (!applications.includes(req.user.application)) {
      return res.status(403).json({ 
        error: 'Access denied for this application',
        allowedApplications: applications
      });
    }

    next();
  };
}

export async function optionalAuth(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return next(); // Continue without authentication
  }

  try {
    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, config.jwt.secret) as any;
    
    const session = await redisUtils.getUserSession(decoded.userId);
    if (session) {
      const userResult = await pool.query(
        'SELECT id, email, username, is_admin, is_active FROM users WHERE id = $1',
        [decoded.userId]
      );

      if (userResult.rows.length > 0 && userResult.rows[0].is_active) {
        const user = userResult.rows[0];
        req.user = {
          id: user.id,
          email: user.email,
          username: user.username,
          isAdmin: user.is_admin,
          application: decoded.application,
          permissions: decoded.permissions
        };
      }
    }
  } catch (error) {
    // Ignore authentication errors for optional auth
  }

  next();
}
