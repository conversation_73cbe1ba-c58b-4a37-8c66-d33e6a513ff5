import { Request } from 'express';
import { pool } from '../config/database';
import { redisUtils } from '../config/redis';

export interface AuditLogEntry {
  action: string;
  userId?: number;
  data?: any;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  service: string;
  success: boolean;
  errorMessage?: string;
}

export async function auditLog(
  action: string, 
  data: any = {}, 
  req?: Request, 
  success: boolean = true, 
  errorMessage?: string
) {
  try {
    const logEntry: AuditLogEntry = {
      action,
      userId: data.userId || (req as any)?.user?.id,
      data: sanitizeData(data),
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent'),
      timestamp: new Date(),
      service: 'auth-service',
      success,
      errorMessage
    };

    // Store in PostgreSQL for permanent record
    await pool.query(
      `INSERT INTO audit_logs (action, user_id, data, ip_address, user_agent, timestamp, service, success, error_message)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
      [
        logEntry.action,
        logEntry.userId,
        JSON.stringify(logEntry.data),
        logEntry.ipAddress,
        logEntry.userAgent,
        logEntry.timestamp,
        logEntry.service,
        logEntry.success,
        logEntry.errorMessage
      ]
    );

    // Store in Redis for real-time monitoring (keep for 24 hours)
    const redisKey = `audit:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
    await redisUtils.setJSON(redisKey, logEntry, 24 * 60 * 60);

    // Update metrics
    await updateAuditMetrics(action, success);

    console.log(`[AUDIT] ${action}:`, {
      userId: logEntry.userId,
      success: logEntry.success,
      ip: logEntry.ipAddress
    });
  } catch (error) {
    console.error('Failed to write audit log:', error);
  }
}

function sanitizeData(data: any): any {
  const sanitized = { ...data };
  
  // Remove sensitive information
  const sensitiveFields = ['password', 'passwordHash', 'token', 'secret', 'key'];
  
  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }

  return sanitized;
}

async function updateAuditMetrics(action: string, success: boolean) {
  try {
    const today = new Date().toISOString().split('T')[0];
    const hour = new Date().getHours();
    
    // Daily metrics
    const dailyKey = `metrics:daily:${today}`;
    await redisUtils.incr(`${dailyKey}:${action}:total`);
    await redisUtils.expire(`${dailyKey}:${action}:total`, 7 * 24 * 60 * 60); // Keep for 7 days
    
    if (success) {
      await redisUtils.incr(`${dailyKey}:${action}:success`);
      await redisUtils.expire(`${dailyKey}:${action}:success`, 7 * 24 * 60 * 60);
    } else {
      await redisUtils.incr(`${dailyKey}:${action}:failure`);
      await redisUtils.expire(`${dailyKey}:${action}:failure`, 7 * 24 * 60 * 60);
    }
    
    // Hourly metrics
    const hourlyKey = `metrics:hourly:${today}:${hour}`;
    await redisUtils.incr(`${hourlyKey}:${action}:total`);
    await redisUtils.expire(`${hourlyKey}:${action}:total`, 24 * 60 * 60); // Keep for 24 hours
    
    if (success) {
      await redisUtils.incr(`${hourlyKey}:${action}:success`);
      await redisUtils.expire(`${hourlyKey}:${action}:success`, 24 * 60 * 60);
    } else {
      await redisUtils.incr(`${hourlyKey}:${action}:failure`);
      await redisUtils.expire(`${hourlyKey}:${action}:failure`, 24 * 60 * 60);
    }
  } catch (error) {
    console.error('Failed to update audit metrics:', error);
  }
}

export async function getAuditMetrics(timeframe: 'hourly' | 'daily' = 'daily', date?: string) {
  try {
    const targetDate = date || new Date().toISOString().split('T')[0];
    const metrics: any = {};

    if (timeframe === 'daily') {
      const key = `metrics:daily:${targetDate}`;
      const actions = ['LOGIN_SUCCESS', 'LOGIN_FAILED', 'REGISTRATION', 'LOGOUT', 'PASSWORD_RESET'];
      
      for (const action of actions) {
        const total = await redisUtils.get(`${key}:${action}:total`) || '0';
        const success = await redisUtils.get(`${key}:${action}:success`) || '0';
        const failure = await redisUtils.get(`${key}:${action}:failure`) || '0';
        
        metrics[action] = {
          total: parseInt(total),
          success: parseInt(success),
          failure: parseInt(failure)
        };
      }
    } else {
      // Hourly metrics for the specified date
      for (let hour = 0; hour < 24; hour++) {
        const key = `metrics:hourly:${targetDate}:${hour}`;
        const hourMetrics: any = {};
        
        const actions = ['LOGIN_SUCCESS', 'LOGIN_FAILED', 'REGISTRATION', 'LOGOUT'];
        
        for (const action of actions) {
          const total = await redisUtils.get(`${key}:${action}:total`) || '0';
          const success = await redisUtils.get(`${key}:${action}:success`) || '0';
          const failure = await redisUtils.get(`${key}:${action}:failure`) || '0';
          
          hourMetrics[action] = {
            total: parseInt(total),
            success: parseInt(success),
            failure: parseInt(failure)
          };
        }
        
        metrics[hour] = hourMetrics;
      }
    }

    return metrics;
  } catch (error) {
    console.error('Failed to get audit metrics:', error);
    return {};
  }
}

export async function getRecentAuditLogs(limit: number = 100, userId?: number, action?: string) {
  try {
    let query = `
      SELECT action, user_id, data, ip_address, user_agent, timestamp, service, success, error_message
      FROM audit_logs
      WHERE 1=1
    `;
    const params: any[] = [];
    let paramCount = 0;

    if (userId) {
      paramCount++;
      query += ` AND user_id = $${paramCount}`;
      params.push(userId);
    }

    if (action) {
      paramCount++;
      query += ` AND action = $${paramCount}`;
      params.push(action);
    }

    query += ` ORDER BY timestamp DESC LIMIT $${paramCount + 1}`;
    params.push(limit);

    const result = await pool.query(query, params);
    return result.rows;
  } catch (error) {
    console.error('Failed to get recent audit logs:', error);
    return [];
  }
}

// Create audit_logs table if it doesn't exist
export async function createAuditTable() {
  try {
    await pool.query(`
      CREATE TABLE IF NOT EXISTS audit_logs (
        id SERIAL PRIMARY KEY,
        action VARCHAR(100) NOT NULL,
        user_id INTEGER,
        data JSONB,
        ip_address INET,
        user_agent TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        service VARCHAR(50) NOT NULL,
        success BOOLEAN DEFAULT TRUE,
        error_message TEXT
      )
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
      CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
      CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
      CREATE INDEX IF NOT EXISTS idx_audit_logs_service ON audit_logs(service);
    `);

    console.log('✅ Audit logs table created/verified');
  } catch (error) {
    console.error('❌ Failed to create audit logs table:', error);
    throw error;
  }
}
