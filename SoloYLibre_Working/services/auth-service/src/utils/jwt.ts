import jwt from 'jsonwebtoken';
import { config } from '../config/config';

export interface TokenPayload {
  userId: number;
  email: string;
  username: string;
  isAdmin: boolean;
  application: string;
  permissions: any;
}

export function generateTokens(userId: number, payload: Omit<TokenPayload, 'userId'>) {
  const accessTokenPayload = {
    userId,
    ...payload,
    type: 'access'
  };

  const refreshTokenPayload = {
    userId,
    type: 'refresh'
  };

  const accessToken = jwt.sign(
    accessTokenPayload,
    config.jwt.secret,
    { 
      expiresIn: config.jwt.expiresIn,
      issuer: 'unified-auth-service',
      audience: payload.application
    }
  );

  const refreshToken = jwt.sign(
    refreshTokenPayload,
    config.jwt.refreshSecret,
    { 
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: 'unified-auth-service',
      audience: payload.application
    }
  );

  return { accessToken, refreshToken };
}

export function verifyAccessToken(token: string): TokenPayload | null {
  try {
    const decoded = jwt.verify(token, config.jwt.secret) as any;
    
    if (decoded.type !== 'access') {
      return null;
    }

    return {
      userId: decoded.userId,
      email: decoded.email,
      username: decoded.username,
      isAdmin: decoded.isAdmin,
      application: decoded.application,
      permissions: decoded.permissions
    };
  } catch (error) {
    return null;
  }
}

export function verifyRefreshToken(token: string): { userId: number } | null {
  try {
    const decoded = jwt.verify(token, config.jwt.refreshSecret) as any;
    
    if (decoded.type !== 'refresh') {
      return null;
    }

    return { userId: decoded.userId };
  } catch (error) {
    return null;
  }
}

export function generatePasswordResetToken(userId: number): string {
  return jwt.sign(
    { userId, type: 'password_reset' },
    config.jwt.secret,
    { 
      expiresIn: '1h',
      issuer: 'unified-auth-service'
    }
  );
}

export function verifyPasswordResetToken(token: string): { userId: number } | null {
  try {
    const decoded = jwt.verify(token, config.jwt.secret) as any;
    
    if (decoded.type !== 'password_reset') {
      return null;
    }

    return { userId: decoded.userId };
  } catch (error) {
    return null;
  }
}

export function generateEmailVerificationToken(userId: number): string {
  return jwt.sign(
    { userId, type: 'email_verification' },
    config.jwt.secret,
    { 
      expiresIn: '24h',
      issuer: 'unified-auth-service'
    }
  );
}

export function verifyEmailVerificationToken(token: string): { userId: number } | null {
  try {
    const decoded = jwt.verify(token, config.jwt.secret) as any;
    
    if (decoded.type !== 'email_verification') {
      return null;
    }

    return { userId: decoded.userId };
  } catch (error) {
    return null;
  }
}
