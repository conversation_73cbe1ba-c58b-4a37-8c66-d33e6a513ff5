import express from 'express';
import { body, validationResult } from 'express-validator';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import { pool } from '../config/database';
import { redisUtils } from '../config/redis';
import { config } from '../config/config';
import { authMiddleware } from '../middleware/auth';
import { auditLog } from '../utils/audit';
import { sendEmail } from '../utils/email';
import { generateTokens, verifyRefreshToken } from '../utils/jwt';

const router = express.Router();

// Register endpoint
router.post('/register', [
  body('email').isEmail().normalizeEmail(),
  body('username').isLength({ min: 3, max: 30 }).matches(/^[a-zA-Z0-9_]+$/),
  body('password').isLength({ min: config.security.passwordMinLength }),
  body('firstName').optional().isLength({ min: 1, max: 50 }),
  body('lastName').optional().isLength({ min: 1, max: 50 }),
  body('application').isIn(['social', 'dating', 'learning', 'admin']),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { email, username, password, firstName, lastName, application } = req.body;

    // Check if user already exists
    const existingUser = await pool.query(
      'SELECT id FROM users WHERE email = $1 OR username = $2',
      [email, username]
    );

    if (existingUser.rows.length > 0) {
      await auditLog('REGISTRATION_FAILED', { email, username, reason: 'User already exists' }, req);
      return res.status(409).json({ error: 'User already exists' });
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, config.security.bcryptRounds);

    // Create user
    const userResult = await pool.query(
      `INSERT INTO users (email, username, password_hash, first_name, last_name) 
       VALUES ($1, $2, $3, $4, $5) RETURNING id, email, username, first_name, last_name, is_verified`,
      [email, username, passwordHash, firstName, lastName]
    );

    const user = userResult.rows[0];

    // Set default permissions for the application
    const defaultPermissions = getDefaultPermissions(application);
    await pool.query(
      'INSERT INTO user_permissions (user_id, application, permissions) VALUES ($1, $2, $3)',
      [user.id, application, JSON.stringify(defaultPermissions)]
    );

    // Generate email verification token
    const verificationToken = jwt.sign(
      { userId: user.id, type: 'email_verification' },
      config.jwt.secret,
      { expiresIn: '24h' }
    );

    await pool.query(
      'INSERT INTO email_verification_tokens (user_id, token, expires_at) VALUES ($1, $2, $3)',
      [user.id, verificationToken, new Date(Date.now() + 24 * 60 * 60 * 1000)]
    );

    // Send verification email
    await sendEmail({
      to: email,
      subject: 'Verify your email address',
      template: 'email-verification',
      data: {
        firstName: firstName || username,
        verificationUrl: `${config.urls.frontend[application]}/verify-email?token=${verificationToken}`
      }
    });

    await auditLog('USER_REGISTERED', { userId: user.id, email, username, application }, req);

    res.status(201).json({
      message: 'User registered successfully. Please check your email for verification.',
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.first_name,
        lastName: user.last_name,
        isVerified: user.is_verified
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Login endpoint
router.post('/login', [
  body('identifier').notEmpty(), // email or username
  body('password').notEmpty(),
  body('application').isIn(['social', 'dating', 'learning', 'admin']),
  body('twoFactorCode').optional().isLength({ min: 6, max: 6 }),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { identifier, password, application, twoFactorCode } = req.body;
    const clientIP = req.ip;

    // Check rate limiting
    const rateLimit = await redisUtils.checkRateLimit(`login:${clientIP}`, 900, 10);
    if (!rateLimit.allowed) {
      return res.status(429).json({ 
        error: 'Too many login attempts. Please try again later.',
        resetTime: rateLimit.resetTime
      });
    }

    // Check login attempts for this identifier
    const attempts = await redisUtils.getLoginAttempts(identifier);
    if (attempts >= config.security.maxLoginAttempts) {
      await auditLog('LOGIN_BLOCKED', { identifier, reason: 'Too many attempts' }, req);
      return res.status(423).json({ error: 'Account temporarily locked due to too many failed attempts' });
    }

    // Find user
    const userResult = await pool.query(
      `SELECT id, email, username, password_hash, first_name, last_name, avatar_url, 
              is_verified, is_active, is_admin, two_factor_enabled, two_factor_secret,
              locked_until
       FROM users 
       WHERE (email = $1 OR username = $1) AND is_active = true`,
      [identifier]
    );

    if (userResult.rows.length === 0) {
      await redisUtils.incrementLoginAttempts(identifier);
      await auditLog('LOGIN_FAILED', { identifier, reason: 'User not found' }, req);
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const user = userResult.rows[0];

    // Check if account is locked
    if (user.locked_until && new Date() < new Date(user.locked_until)) {
      await auditLog('LOGIN_BLOCKED', { userId: user.id, reason: 'Account locked' }, req);
      return res.status(423).json({ error: 'Account is temporarily locked' });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      await redisUtils.incrementLoginAttempts(identifier);
      await auditLog('LOGIN_FAILED', { userId: user.id, reason: 'Invalid password' }, req);
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check two-factor authentication
    if (user.two_factor_enabled) {
      if (!twoFactorCode) {
        return res.status(200).json({ 
          requiresTwoFactor: true,
          message: 'Two-factor authentication code required'
        });
      }

      const isValidTwoFactor = speakeasy.totp.verify({
        secret: user.two_factor_secret,
        encoding: 'base32',
        token: twoFactorCode,
        window: 2
      });

      if (!isValidTwoFactor) {
        await redisUtils.incrementLoginAttempts(identifier);
        await auditLog('LOGIN_FAILED', { userId: user.id, reason: 'Invalid 2FA code' }, req);
        return res.status(401).json({ error: 'Invalid two-factor authentication code' });
      }
    }

    // Check user permissions for the application
    const permissionsResult = await pool.query(
      'SELECT permissions FROM user_permissions WHERE user_id = $1 AND application = $2',
      [user.id, application]
    );

    if (permissionsResult.rows.length === 0) {
      await auditLog('LOGIN_FAILED', { userId: user.id, reason: 'No permissions for application' }, req);
      return res.status(403).json({ error: 'Access denied for this application' });
    }

    const permissions = permissionsResult.rows[0].permissions;

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id, {
      email: user.email,
      username: user.username,
      isAdmin: user.is_admin,
      application,
      permissions
    });

    // Store refresh token in database
    await pool.query(
      `INSERT INTO user_sessions (user_id, refresh_token, device_info, ip_address, user_agent, expires_at)
       VALUES ($1, $2, $3, $4, $5, $6)`,
      [
        user.id,
        refreshToken,
        JSON.stringify({ application }),
        clientIP,
        req.get('User-Agent'),
        new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
      ]
    );

    // Update last login
    await pool.query(
      'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
      [user.id]
    );

    // Clear login attempts
    await redisUtils.clearLoginAttempts(identifier);

    // Store session in Redis
    await redisUtils.setUserSession(user.id, {
      userId: user.id,
      email: user.email,
      username: user.username,
      application,
      permissions,
      loginTime: new Date().toISOString()
    }, 15 * 60); // 15 minutes

    await auditLog('LOGIN_SUCCESS', { userId: user.id, application }, req);

    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.first_name,
        lastName: user.last_name,
        avatarUrl: user.avatar_url,
        isVerified: user.is_verified,
        isAdmin: user.is_admin,
        permissions
      },
      tokens: {
        accessToken,
        refreshToken
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Refresh token endpoint
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({ error: 'Refresh token required' });
    }

    const decoded = verifyRefreshToken(refreshToken);
    if (!decoded) {
      return res.status(401).json({ error: 'Invalid refresh token' });
    }

    // Check if refresh token exists in database
    const sessionResult = await pool.query(
      'SELECT user_id, device_info FROM user_sessions WHERE refresh_token = $1 AND expires_at > CURRENT_TIMESTAMP',
      [refreshToken]
    );

    if (sessionResult.rows.length === 0) {
      return res.status(401).json({ error: 'Refresh token not found or expired' });
    }

    const session = sessionResult.rows[0];
    const application = session.device_info?.application || 'social';

    // Get user data
    const userResult = await pool.query(
      'SELECT id, email, username, is_admin FROM users WHERE id = $1 AND is_active = true',
      [session.user_id]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({ error: 'User not found' });
    }

    const user = userResult.rows[0];

    // Get permissions
    const permissionsResult = await pool.query(
      'SELECT permissions FROM user_permissions WHERE user_id = $1 AND application = $2',
      [user.id, application]
    );

    const permissions = permissionsResult.rows[0]?.permissions || {};

    // Generate new access token
    const { accessToken } = generateTokens(user.id, {
      email: user.email,
      username: user.username,
      isAdmin: user.is_admin,
      application,
      permissions
    });

    res.json({
      accessToken,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        isAdmin: user.is_admin,
        permissions
      }
    });
  } catch (error) {
    console.error('Refresh token error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Logout endpoint
router.post('/logout', authMiddleware, async (req, res) => {
  try {
    const { refreshToken } = req.body;
    const userId = req.user.id;

    // Remove refresh token from database
    if (refreshToken) {
      await pool.query(
        'DELETE FROM user_sessions WHERE refresh_token = $1',
        [refreshToken]
      );
    }

    // Remove session from Redis
    await redisUtils.deleteUserSession(userId);

    await auditLog('LOGOUT', { userId }, req);

    res.json({ message: 'Logout successful' });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Helper function to get default permissions
function getDefaultPermissions(application: string) {
  const permissions = {
    social: {
      posts: ['read', 'create', 'update_own', 'delete_own'],
      comments: ['read', 'create', 'update_own', 'delete_own'],
      likes: ['read', 'create', 'delete_own'],
      friends: ['read', 'create', 'delete_own'],
      messages: ['read', 'create', 'update_own', 'delete_own']
    },
    dating: {
      profiles: ['read', 'create', 'update_own'],
      matches: ['read', 'create'],
      messages: ['read', 'create', 'update_own', 'delete_own'],
      likes: ['read', 'create']
    },
    learning: {
      courses: ['read', 'enroll'],
      lessons: ['read'],
      progress: ['read', 'update_own'],
      certificates: ['read'],
      payments: ['create']
    },
    admin: {
      users: ['read'],
      analytics: ['read'],
      reports: ['read']
    }
  };

  return permissions[application] || {};
}

export { router as authRoutes };
