import dotenv from 'dotenv';

dotenv.config();

export const config = {
  port: parseInt(process.env.PORT || '4000'),
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '15m',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  },
  
  // Database Configuration
  database: {
    url: process.env.POSTGRES_URL || 'postgresql://postgres:password@localhost:5432/auth_db',
  },
  
  // Redis Configuration
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
  },
  
  // Email Configuration
  email: {
    host: process.env.SMTP_HOST || 'mailhog',
    port: parseInt(process.env.SMTP_PORT || '1025'),
    secure: process.env.SMTP_SECURE === 'true',
    user: process.env.SMTP_USER || '',
    pass: process.env.SMTP_PASS || '',
    from: process.env.EMAIL_FROM || '<EMAIL>',
  },
  
  // Security Configuration
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
    maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5'),
    lockoutDuration: parseInt(process.env.LOCKOUT_DURATION || '900000'), // 15 minutes
    passwordMinLength: parseInt(process.env.PASSWORD_MIN_LENGTH || '8'),
    sessionSecret: process.env.SESSION_SECRET || 'your-super-secret-session-key-change-in-production',
  },
  
  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '900000'), // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX || '100'),
  },
  
  // Two-Factor Authentication
  twoFactor: {
    serviceName: process.env.TWO_FACTOR_SERVICE_NAME || 'Unified Platform',
    issuer: process.env.TWO_FACTOR_ISSUER || 'YourCompany',
  },
  
  // Application URLs
  urls: {
    frontend: {
      social: process.env.SOCIAL_FRONTEND_URL || 'http://localhost:3000',
      dating: process.env.DATING_FRONTEND_URL || 'http://localhost:3001',
      learning: process.env.LEARNING_FRONTEND_URL || 'http://localhost:3002',
      admin: process.env.ADMIN_FRONTEND_URL || 'http://localhost:3003',
    },
  },
};
