import { createClient } from 'redis';
import { config } from './config';

export const redisClient = createClient({
  url: config.redis.url,
});

export async function connectRedis() {
  try {
    await redisClient.connect();
    console.log('✅ Connected to Redis');
    
    // Test the connection
    await redisClient.ping();
    console.log('✅ Redis connection verified');
  } catch (error) {
    console.error('❌ Failed to connect to Redis:', error);
    throw error;
  }
}

// Redis utility functions
export const redisUtils = {
  // Set with expiration
  async setex(key: string, seconds: number, value: string): Promise<void> {
    await redisClient.setEx(key, seconds, value);
  },

  // Get value
  async get(key: string): Promise<string | null> {
    return await redisClient.get(key);
  },

  // Delete key
  async del(key: string): Promise<number> {
    return await redisClient.del(key);
  },

  // Check if key exists
  async exists(key: string): Promise<number> {
    return await redisClient.exists(key);
  },

  // Increment counter
  async incr(key: string): Promise<number> {
    return await redisClient.incr(key);
  },

  // Set expiration
  async expire(key: string, seconds: number): Promise<boolean> {
    return await redisClient.expire(key, seconds);
  },

  // Get TTL
  async ttl(key: string): Promise<number> {
    return await redisClient.ttl(key);
  },

  // Store JSON data
  async setJSON(key: string, data: any, seconds?: number): Promise<void> {
    const value = JSON.stringify(data);
    if (seconds) {
      await redisClient.setEx(key, seconds, value);
    } else {
      await redisClient.set(key, value);
    }
  },

  // Get JSON data
  async getJSON(key: string): Promise<any | null> {
    const value = await redisClient.get(key);
    return value ? JSON.parse(value) : null;
  },

  // Store user session
  async setUserSession(userId: number, sessionData: any, expirationSeconds: number): Promise<void> {
    const key = `user_session:${userId}`;
    await this.setJSON(key, sessionData, expirationSeconds);
  },

  // Get user session
  async getUserSession(userId: number): Promise<any | null> {
    const key = `user_session:${userId}`;
    return await this.getJSON(key);
  },

  // Delete user session
  async deleteUserSession(userId: number): Promise<void> {
    const key = `user_session:${userId}`;
    await redisClient.del(key);
  },

  // Store login attempts
  async incrementLoginAttempts(identifier: string): Promise<number> {
    const key = `login_attempts:${identifier}`;
    const attempts = await redisClient.incr(key);
    
    // Set expiration on first attempt
    if (attempts === 1) {
      await redisClient.expire(key, 900); // 15 minutes
    }
    
    return attempts;
  },

  // Get login attempts
  async getLoginAttempts(identifier: string): Promise<number> {
    const key = `login_attempts:${identifier}`;
    const attempts = await redisClient.get(key);
    return attempts ? parseInt(attempts) : 0;
  },

  // Clear login attempts
  async clearLoginAttempts(identifier: string): Promise<void> {
    const key = `login_attempts:${identifier}`;
    await redisClient.del(key);
  },

  // Store rate limit data
  async checkRateLimit(identifier: string, windowSeconds: number, maxRequests: number): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const key = `rate_limit:${identifier}`;
    const current = await redisClient.incr(key);
    
    if (current === 1) {
      await redisClient.expire(key, windowSeconds);
    }
    
    const ttl = await redisClient.ttl(key);
    const resetTime = Date.now() + (ttl * 1000);
    
    return {
      allowed: current <= maxRequests,
      remaining: Math.max(0, maxRequests - current),
      resetTime
    };
  }
};
