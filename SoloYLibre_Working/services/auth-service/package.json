{"name": "unified-auth-service", "version": "1.0.0", "description": "Unified authentication service for all applications", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "lint": "eslint src/**/*.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "redis": "^4.6.12", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "nodemailer": "^6.9.7", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "prom-client": "^15.1.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.5", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.9", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/express-session": "^1.17.10", "@types/nodemailer": "^6.4.14", "@types/speakeasy": "^2.0.10", "@types/qrcode": "^1.5.5", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "@types/jest": "^29.5.11"}}