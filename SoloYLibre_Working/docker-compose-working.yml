version: '3.8'

services:
  # ===== DATABASES =====
  postgres-soloylibre:
    image: postgres:15-alpine
    container_name: soloylibre-postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=soloylibre_db
    ports:
      - "5434:5432"
    volumes:
      - soloylibre_postgres_data:/var/lib/postgresql/data
    networks:
      - soloylibre-network

  mongodb-soloylibre:
    image: mongo:6.0
    container_name: soloylibre-mongodb
    environment:
      - MONGO_INITDB_DATABASE=dating_db
    ports:
      - "27018:27017"
    volumes:
      - soloylibre_mongodb_data:/data/db
    networks:
      - soloylibre-network

  redis-soloylibre:
    image: redis:7-alpine
    container_name: soloylibre-redis
    ports:
      - "6381:6379"
    volumes:
      - soloylibre_redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - soloylibre-network

  # ===== STORAGE & AI =====
  minio-soloylibre:
    image: minio/minio:latest
    container_name: soloylibre-minio
    ports:
      - "9002:9000"
      - "9005:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - soloylibre_minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - soloylibre-network

  ollama-soloylibre:
    image: ollama/ollama:latest
    container_name: soloylibre-ollama
    ports:
      - "11435:11434"
    volumes:
      - soloylibre_ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    networks:
      - soloylibre-network

  # ===== MONITORING =====
  prometheus-soloylibre:
    image: prom/prometheus:latest
    container_name: soloylibre-prometheus
    ports:
      - "9092:9090"
    volumes:
      - soloylibre_prometheus_data:/prometheus
    networks:
      - soloylibre-network

  grafana-soloylibre:
    image: grafana/grafana:latest
    container_name: soloylibre-grafana
    ports:
      - "3005:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - soloylibre_grafana_data:/var/lib/grafana
    depends_on:
      - prometheus-soloylibre
    networks:
      - soloylibre-network

  # ===== EMAIL TESTING =====
  mailhog-soloylibre:
    image: mailhog/mailhog:latest
    container_name: soloylibre-mailhog
    ports:
      - "1026:1025"
      - "8026:8025"
    networks:
      - soloylibre-network

  # ===== ELASTICSEARCH =====
  elasticsearch-soloylibre:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: soloylibre-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9201:9200"
    volumes:
      - soloylibre_elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - soloylibre-network

  # ===== NGINX REVERSE PROXY =====
  nginx-soloylibre:
    image: nginx:alpine
    container_name: soloylibre-nginx
    ports:
      - "8080:80"
    volumes:
      - ./nginx-simple.conf:/etc/nginx/nginx.conf
    depends_on:
      - postgres-soloylibre
      - mongodb-soloylibre
      - redis-soloylibre
    networks:
      - soloylibre-network

volumes:
  soloylibre_postgres_data:
  soloylibre_mongodb_data:
  soloylibre_redis_data:
  soloylibre_minio_data:
  soloylibre_ollama_data:
  soloylibre_prometheus_data:
  soloylibre_grafana_data:
  soloylibre_elasticsearch_data:

networks:
  soloylibre-network:
    driver: bridge
