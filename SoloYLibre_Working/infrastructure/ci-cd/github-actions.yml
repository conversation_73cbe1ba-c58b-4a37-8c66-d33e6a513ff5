name: Unified Platform CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Security scanning
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  # Frontend tests
  frontend-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [social-frontend, dating-frontend, learning-frontend, admin-dashboard]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: apps/${{ matrix.app }}/package-lock.json
      
      - name: Install dependencies
        run: npm ci
        working-directory: apps/${{ matrix.app }}
      
      - name: Run linting
        run: npm run lint
        working-directory: apps/${{ matrix.app }}
      
      - name: Run tests
        run: npm test -- --coverage --watchAll=false
        working-directory: apps/${{ matrix.app }}
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: apps/${{ matrix.app }}/coverage/lcov.info
          flags: ${{ matrix.app }}

  # Backend tests
  backend-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [auth-service, social-api, dating-api, learning-api, audit-service, websocket-service, analytics-service, queue-service]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: services/${{ matrix.service }}/package-lock.json
      
      - name: Install dependencies
        run: npm ci
        working-directory: services/${{ matrix.service }}
      
      - name: Run linting
        run: npm run lint
        working-directory: services/${{ matrix.service }}
      
      - name: Run tests
        run: npm test -- --coverage
        working-directory: services/${{ matrix.service }}
        env:
          NODE_ENV: test
          POSTGRES_URL: postgresql://postgres:postgres@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: services/${{ matrix.service }}/coverage/lcov.info
          flags: ${{ matrix.service }}

  # Build and push Docker images
  build-and-push:
    needs: [security-scan, frontend-tests, backend-tests]
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    strategy:
      matrix:
        component:
          - { path: "apps/social-frontend", name: "social-frontend" }
          - { path: "apps/dating-frontend", name: "dating-frontend" }
          - { path: "apps/learning-frontend", name: "learning-frontend" }
          - { path: "apps/admin-dashboard", name: "admin-dashboard" }
          - { path: "services/auth-service", name: "auth-service" }
          - { path: "services/social-api", name: "social-api" }
          - { path: "services/dating-api", name: "dating-api" }
          - { path: "services/learning-api", name: "learning-api" }
          - { path: "services/audit-service", name: "audit-service" }
          - { path: "services/websocket-service", name: "websocket-service" }
          - { path: "services/analytics-service", name: "analytics-service" }
          - { path: "services/queue-service", name: "queue-service" }

    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.component.name }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ${{ matrix.component.path }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Deploy to staging
  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure kubectl
        uses: azure/k8s-set-context@v3
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}
      
      - name: Deploy to staging
        run: |
          kubectl apply -f infrastructure/kubernetes/ -n unified-platform-staging
          kubectl set image deployment/auth-service auth-service=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/auth-service:${{ github.sha }} -n unified-platform-staging
          kubectl set image deployment/social-api social-api=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/social-api:${{ github.sha }} -n unified-platform-staging
          # Add other services...
          kubectl rollout status deployment/auth-service -n unified-platform-staging

  # Deploy to production
  deploy-production:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure kubectl
        uses: azure/k8s-set-context@v3
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}
      
      - name: Deploy to production
        run: |
          kubectl apply -f infrastructure/kubernetes/ -n unified-platform
          kubectl set image deployment/auth-service auth-service=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/auth-service:${{ github.sha }} -n unified-platform
          kubectl set image deployment/social-api social-api=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/social-api:${{ github.sha }} -n unified-platform
          # Add other services...
          kubectl rollout status deployment/auth-service -n unified-platform

  # Performance tests
  performance-tests:
    needs: deploy-staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Run K6 performance tests
        uses: grafana/k6-action@v0.3.0
        with:
          filename: tests/performance/load-test.js
        env:
          K6_CLOUD_TOKEN: ${{ secrets.K6_CLOUD_TOKEN }}

  # Security tests
  security-tests:
    needs: deploy-staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Run OWASP ZAP security scan
        uses: zaproxy/action-full-scan@v0.7.0
        with:
          target: 'https://staging.yourplatform.com'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'
