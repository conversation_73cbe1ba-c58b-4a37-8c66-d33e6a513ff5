events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;
    
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
    
    # Upstream servers - Main App (Unified Entry Point)
    upstream main-app {
        server main-app:3000;
    }
    
    # Individual application upstreams
    upstream social-frontend {
        server social-frontend:3001;
    }
    
    upstream dating-frontend {
        server dating-frontend:3002;
    }
    
    upstream learning-frontend {
        server learning-frontend:3003;
    }
    
    upstream admin-dashboard {
        server admin-dashboard:3004;
    }
    
    # Backend service upstreams
    upstream auth-service {
        server auth-service:4000;
    }
    
    upstream social-api {
        server social-api:4001;
    }
    
    upstream dating-api {
        server dating-api:4002;
    }
    
    upstream learning-api {
        server learning-api:4003;
    }
    
    upstream admin-api {
        server admin-api:4004;
    }
    
    upstream websocket-service {
        server websocket-service:4005;
    }
    
    upstream analytics-service {
        server analytics-service:4006;
    }
    
    upstream audit-service {
        server audit-service:4007;
    }
    
    # Main server block with enhanced URL routing
    server {
        listen 80;
        server_name localhost;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
        
        # Main application - unified entry point with URL routing
        location / {
            proxy_pass http://main-app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Social Media Application URLs
        location ~ ^/(social|instagram|twitter|tiktok|discord|linkedin)(/.*)?$ {
            proxy_pass http://main-app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Dating Application URLs
        location ~ ^/(dating|tinder|bumble|hinge|match)(/.*)?$ {
            proxy_pass http://main-app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Learning Platform URLs
        location ~ ^/(learning|courses|education|academy)(/.*)?$ {
            proxy_pass http://main-app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Admin Dashboard URLs
        location ~ ^/(admin|dashboard|management|themes|showcase)(/.*)?$ {
            proxy_pass http://main-app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Common navigation shortcuts
        location ~ ^/(home|feed|profile|messages|notifications)(/.*)?$ {
            proxy_pass http://main-app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # API routing with rate limiting
        location /api/auth {
            limit_req zone=auth burst=20 nodelay;
            rewrite ^/api/auth(.*) $1 break;
            proxy_pass http://auth-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/social {
            limit_req zone=api burst=50 nodelay;
            rewrite ^/api/social(.*) $1 break;
            proxy_pass http://social-api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/dating {
            limit_req zone=api burst=50 nodelay;
            rewrite ^/api/dating(.*) $1 break;
            proxy_pass http://dating-api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/learning {
            limit_req zone=api burst=50 nodelay;
            rewrite ^/api/learning(.*) $1 break;
            proxy_pass http://learning-api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/admin {
            limit_req zone=api burst=20 nodelay;
            rewrite ^/api/admin(.*) $1 break;
            proxy_pass http://admin-api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/analytics {
            limit_req zone=api burst=10 nodelay;
            rewrite ^/api/analytics(.*) $1 break;
            proxy_pass http://analytics-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/audit {
            limit_req zone=api burst=10 nodelay;
            rewrite ^/api/audit(.*) $1 break;
            proxy_pass http://audit-service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # WebSocket connections
        location /socket.io/ {
            proxy_pass http://websocket-service;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Static files
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # File uploads
        location /uploads/ {
            alias /var/www/uploads/;
            expires 1y;
            add_header Cache-Control "public";
        }
        
        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Monitoring endpoints
        location /metrics {
            proxy_pass http://analytics-service/metrics;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
