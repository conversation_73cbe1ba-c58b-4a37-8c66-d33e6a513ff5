// MongoDB initialization script
db = db.getSiblingDB('dating_db');

// Create collections
db.createCollection('users');
db.createCollection('profiles');
db.createCollection('matches');
db.createCollection('conversations');
db.createCollection('messages');

// Create indexes for better performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });
db.profiles.createIndex({ "userId": 1 }, { unique: true });
db.profiles.createIndex({ "location": "2dsphere" });
db.profiles.createIndex({ "age": 1 });
db.profiles.createIndex({ "interests": 1 });
db.matches.createIndex({ "user1": 1, "user2": 1 }, { unique: true });
db.conversations.createIndex({ "participants": 1 });
db.messages.createIndex({ "conversationId": 1, "timestamp": -1 });

print('Dating database initialized successfully');
