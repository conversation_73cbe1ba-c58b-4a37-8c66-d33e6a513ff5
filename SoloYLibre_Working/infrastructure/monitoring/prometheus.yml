global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Auth Service
  - job_name: 'auth-service'
    static_configs:
      - targets: ['auth-service:4000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Social API
  - job_name: 'social-api'
    static_configs:
      - targets: ['social-api:4000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Dating API
  - job_name: 'dating-api'
    static_configs:
      - targets: ['dating-api:4000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Learning API
  - job_name: 'learning-api'
    static_configs:
      - targets: ['learning-api:4000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Audit Service
  - job_name: 'audit-service'
    static_configs:
      - targets: ['audit-service:4000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # WebSocket Service
  - job_name: 'websocket-service'
    static_configs:
      - targets: ['websocket-service:4000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  # MongoDB
  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongodb:27017']
    scrape_interval: 30s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # Elasticsearch
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']
    scrape_interval: 30s

  # MinIO
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    scrape_interval: 30s

  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 30s
