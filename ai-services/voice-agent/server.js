/**
 * SoloYlibre & JEYKO Voice React Agent
 * Head Developer: <PERSON>
 * Company: SoloYlibre | AI Division: JEYKO
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const axios = require('axios');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

// Company configuration
const COMPANY_CONFIG = {
    name: process.env.COMPANY_NAME || 'SoloYlibre',
    aiDivision: process.env.AI_DIVISION || 'JEYKO',
    headDeveloper: 'Jose L Encarnacion',
    email: '<EMAIL>'
};

// Middleware
app.use(express.json());
app.use(express.static('public'));

// CORS middleware
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    next();
});

// Routes
app.get('/', (req, res) => {
    res.json({
        message: `Welcome to ${COMPANY_CONFIG.name} & ${COMPANY_CONFIG.aiDivision} Voice Agent`,
        company: COMPANY_CONFIG.name,
        aiDivision: COMPANY_CONFIG.aiDivision,
        headDeveloper: COMPANY_CONFIG.headDeveloper,
        status: 'operational',
        services: {
            voiceRecognition: 'active',
            textToSpeech: 'active',
            aiProcessing: 'active'
        }
    });
});

// Voice processing endpoint
app.post('/api/voice/process', async (req, res) => {
    try {
        const { text, language = 'en', voice = 'default' } = req.body;
        
        if (!text) {
            return res.status(400).json({ error: 'Text is required' });
        }
        
        // Process with AI (placeholder for actual AI integration)
        const aiResponse = await processWithAI(text);
        
        // Generate TTS
        const audioUrl = await generateTTS(aiResponse, language, voice);
        
        res.json({
            originalText: text,
            aiResponse: aiResponse,
            audioUrl: audioUrl,
            language: language,
            voice: voice,
            company: COMPANY_CONFIG.name,
            aiDivision: COMPANY_CONFIG.aiDivision
        });
        
    } catch (error) {
        console.error('Voice processing error:', error);
        res.status(500).json({ error: 'Voice processing failed' });
    }
});

// AI processing function
async function processWithAI(text) {
    try {
        // Integrate with local Ollama or external AI service
        const response = await axios.post('http://ollama:11434/api/generate', {
            model: 'llama2',
            prompt: `You are an AI assistant for ${COMPANY_CONFIG.name} company and ${COMPANY_CONFIG.aiDivision} AI division. 
                     Head Developer: ${COMPANY_CONFIG.headDeveloper}. 
                     Respond professionally to: ${text}`,
            stream: false
        });
        
        return response.data.response || `Thank you for contacting ${COMPANY_CONFIG.name}. Our ${COMPANY_CONFIG.aiDivision} AI division is processing your request.`;
    } catch (error) {
        console.error('AI processing error:', error);
        return `Hello from ${COMPANY_CONFIG.name} and ${COMPANY_CONFIG.aiDivision} AI division. How can we assist you today?`;
    }
}

// TTS generation function
async function generateTTS(text, language, voice) {
    try {
        // Use ElevenLabs TTS service
        const response = await axios.post('http://soloylibre-elevenlabs-tts:3000/api/tts', {
            text: text,
            language: language,
            voice_id: voice,
            cms_source: 'voice-agent',
            user_id: 'SoloYlibre'
        });
        
        return response.data.audioUrl || null;
    } catch (error) {
        console.error('TTS generation error:', error);
        return null;
    }
}

// WebSocket handling
io.on('connection', (socket) => {
    console.log(`New client connected to ${COMPANY_CONFIG.name} Voice Agent`);
    
    socket.emit('welcome', {
        message: `Connected to ${COMPANY_CONFIG.name} & ${COMPANY_CONFIG.aiDivision} Voice Agent`,
        company: COMPANY_CONFIG.name,
        aiDivision: COMPANY_CONFIG.aiDivision
    });
    
    socket.on('voice-input', async (data) => {
        try {
            const { text, language = 'en' } = data;
            
            // Process voice input
            const aiResponse = await processWithAI(text);
            const audioUrl = await generateTTS(aiResponse, language);
            
            socket.emit('voice-response', {
                originalText: text,
                aiResponse: aiResponse,
                audioUrl: audioUrl,
                timestamp: new Date().toISOString(),
                company: COMPANY_CONFIG.name,
                aiDivision: COMPANY_CONFIG.aiDivision
            });
            
        } catch (error) {
            console.error('Voice input processing error:', error);
            socket.emit('error', { message: 'Voice processing failed' });
        }
    });
    
    socket.on('disconnect', () => {
        console.log('Client disconnected from Voice Agent');
    });
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        company: COMPANY_CONFIG.name,
        aiDivision: COMPANY_CONFIG.aiDivision,
        service: 'Voice React Agent',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Company info endpoint
app.get('/api/company', (req, res) => {
    res.json(COMPANY_CONFIG);
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, '0.0.0.0', () => {
    console.log(`${COMPANY_CONFIG.name} & ${COMPANY_CONFIG.aiDivision} Voice Agent running on port ${PORT}`);
    console.log(`Head Developer: ${COMPANY_CONFIG.headDeveloper}`);
    console.log(`Email: ${COMPANY_CONFIG.email}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Process terminated');
    });
});

module.exports = app;
