# Portainer Advanced Configuration
# Professional 2000% IQ Configuration for Ultimate Dev Environment

# General Settings
general:
  logo_url: ""
  enable_edge_compute: true
  enable_host_management: true
  snapshot_interval: "5m"
  
# Security Settings
security:
  internal_auth: true
  ldap_auth: false
  oauth_auth: false
  hide_labels: []
  allowed_bind_addresses: ["0.0.0.0"]
  
# Features
features:
  enable_stack_management: true
  enable_app_templates: true
  enable_registry_management: true
  enable_webhook: true
  enable_edge_jobs: true
  
# Templates Configuration
templates:
  - url: "https://raw.githubusercontent.com/portainer/templates/master/templates.json"
    title: "Portainer Official Templates"
  - url: "https://raw.githubusercontent.com/SelfhostedPro/selfhosted_templates/master/Template/portainer-v2.json"
    title: "Selfhosted Templates"
  - url: "https://raw.githubusercontent.com/technorabilia/portainer-templates/main/lsio/templates.json"
    title: "LinuxServer.io Templates"

# Registry Configuration
registries:
  - name: "Docker Hub"
    url: "docker.io"
    type: "dockerhub"
    authentication: false
  - name: "GitHub Container Registry"
    url: "ghcr.io"
    type: "github"
    authentication: false

# Notification Settings
notifications:
  webhook_url: ""
  slack_webhook: ""
  teams_webhook: ""
  
# Edge Configuration
edge:
  checkin_interval: 5
  command_interval: 5
  ping_interval: 60
  snapshot_interval: 300
  
# Backup Settings
backup:
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention_days: 30
  include_passwords: false
  
# Monitoring
monitoring:
  enable_container_monitoring: true
  enable_host_monitoring: true
  metrics_retention: "7d"
  
# Custom CSS (Optional)
custom_css: |
  /* Custom Portainer Styling */
  .navbar-brand {
    font-weight: bold;
  }
  
  .card-header {
    background: linear-gradient(45deg, #2196F3, #21CBF3);
    color: white;
  }
  
  .btn-primary {
    background: linear-gradient(45deg, #2196F3, #21CBF3);
    border: none;
  }

# Environment Variables for Docker Compose
environment_variables:
  PORTAINER_ADMIN_PASSWORD_FILE: "/run/secrets/portainer_admin_password"
  PORTAINER_LOGO: ""
  PORTAINER_TEMPLATES: "https://raw.githubusercontent.com/portainer/templates/master/templates.json"
  PORTAINER_HIDE_LABELS: ""
  PORTAINER_ENABLE_EDGE_COMPUTE: "true"
  PORTAINER_EDGE_CHECKIN_INTERVAL: "5"
  PORTAINER_SNAPSHOT_INTERVAL: "5m"
