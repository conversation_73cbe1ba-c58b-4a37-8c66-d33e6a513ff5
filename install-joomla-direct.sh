#!/bin/bash

# Direct Joomla Installation for SoloYlibre & JEYKO
# Head Developer: <PERSON>nacion

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[JOOMLA DIRECT]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Installing Jo<PERSON><PERSON> directly in container..."

# Install Joomla directly via database
docker exec soloylibre-mysql-joomla mysql -u joomla_user -pSoloYlibre_Joomla_2024! joomla_db << 'EOF'
-- Create Joomla configuration table
CREATE TABLE IF NOT EXISTS `sol_extensions` (
  `extension_id` int(11) NOT NULL AUTO_INCREMENT,
  `package_id` int(11) NOT NULL DEFAULT '0',
  `name` varchar(100) NOT NULL,
  `type` varchar(20) NOT NULL,
  `element` varchar(100) NOT NULL,
  `folder` varchar(100) NOT NULL,
  `client_id` tinyint(3) NOT NULL,
  `enabled` tinyint(3) NOT NULL DEFAULT '1',
  `access` int(10) unsigned NOT NULL DEFAULT '1',
  `protected` tinyint(3) NOT NULL DEFAULT '0',
  `manifest_cache` text NOT NULL,
  `params` text NOT NULL,
  `custom_data` text NOT NULL,
  `system_data` text NOT NULL,
  `checked_out` int(10) unsigned NOT NULL DEFAULT '0',
  `checked_out_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `ordering` int(11) DEFAULT '0',
  `state` int(11) DEFAULT '0',
  PRIMARY KEY (`extension_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create users table
CREATE TABLE IF NOT EXISTS `sol_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(400) NOT NULL DEFAULT '',
  `username` varchar(150) NOT NULL DEFAULT '',
  `email` varchar(100) NOT NULL DEFAULT '',
  `password` varchar(100) NOT NULL DEFAULT '',
  `block` tinyint(4) NOT NULL DEFAULT '0',
  `sendEmail` tinyint(4) DEFAULT '0',
  `registerDate` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `lastvisitDate` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `activation` varchar(100) NOT NULL DEFAULT '',
  `params` text NOT NULL,
  `lastResetTime` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `resetCount` int(11) NOT NULL DEFAULT '0',
  `otpKey` varchar(1000) NOT NULL DEFAULT '',
  `otep` varchar(1000) NOT NULL DEFAULT '',
  `requireReset` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert SoloYlibre admin user
INSERT INTO `sol_users` (`name`, `username`, `email`, `password`, `block`, `sendEmail`, `registerDate`, `lastvisitDate`, `params`) 
VALUES ('Jose L Encarnacion - SoloYlibre', 'SoloYlibre', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 0, 1, NOW(), NOW(), '{}');

-- Create user groups table
CREATE TABLE IF NOT EXISTS `sol_usergroups` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` int(10) unsigned NOT NULL DEFAULT '0',
  `lft` int(11) NOT NULL DEFAULT '0',
  `rgt` int(11) NOT NULL DEFAULT '0',
  `level` int(10) unsigned NOT NULL DEFAULT '0',
  `path` varchar(400) NOT NULL DEFAULT '',
  `title` varchar(100) NOT NULL DEFAULT '',
  `alias` varchar(400) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default user groups
INSERT INTO `sol_usergroups` (`id`, `parent_id`, `lft`, `rgt`, `level`, `path`, `title`, `alias`) VALUES
(1, 0, 1, 22, 0, '', 'Public', 'public'),
(2, 1, 8, 21, 1, 'public', 'Registered', 'registered'),
(3, 2, 9, 16, 2, 'public/registered', 'Author', 'author'),
(4, 3, 10, 13, 3, 'public/registered/author', 'Editor', 'editor'),
(5, 4, 11, 12, 4, 'public/registered/author/editor', 'Publisher', 'publisher'),
(6, 1, 4, 7, 1, 'public', 'Manager', 'manager'),
(7, 6, 5, 6, 2, 'public/manager', 'Administrator', 'administrator'),
(8, 1, 2, 3, 1, 'public', 'Super Users', 'super-users');

-- Create user group mapping
CREATE TABLE IF NOT EXISTS `sol_user_usergroup_map` (
  `user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `group_id` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`user_id`,`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Map SoloYlibre user to Super Users group
INSERT INTO `sol_user_usergroup_map` (`user_id`, `group_id`) VALUES (1, 8);

-- Create configuration table
CREATE TABLE IF NOT EXISTS `sol_configuration` (
  `config_id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(255) NOT NULL,
  `config_value` text,
  PRIMARY KEY (`config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert basic configuration
INSERT INTO `sol_configuration` (`config_key`, `config_value`) VALUES
('sitename', 'SoloYlibre Joomla Platform'),
('MetaDesc', 'SoloYlibre business platform powered by JEYKO AI division'),
('mailfrom', '<EMAIL>'),
('fromname', 'SoloYlibre'),
('offline', '0'),
('offline_message', 'SoloYlibre site is temporarily offline for maintenance.');

EOF

print_success "Joomla database structure created"

# Create Joomla configuration file
print_status "Creating Joomla configuration file..."

docker exec josetusabe-joomla bash -c "
cd /var/www/html
cat > configuration.php << 'EOF'
<?php
class JConfig {
    public \$offline = '0';
    public \$offline_message = 'SoloYlibre site is temporarily offline for maintenance.';
    public \$display_offline_message = '1';
    public \$offline_image = '';
    public \$sitename = 'SoloYlibre Joomla Platform';
    public \$editor = 'tinymce';
    public \$captcha = '0';
    public \$list_limit = '20';
    public \$access = '1';
    public \$debug = '0';
    public \$debug_lang = '0';
    public \$debug_lang_const = '1';
    public \$dbtype = 'mysqli';
    public \$host = 'soloylibre-mysql-joomla';
    public \$user = 'joomla_user';
    public \$password = 'SoloYlibre_Joomla_2024!';
    public \$db = 'joomla_db';
    public \$dbprefix = 'sol_';
    public \$live_site = '';
    public \$secret = 'SoloYlibre_Secret_Key_2024_JEYKO';
    public \$gzip = '0';
    public \$error_reporting = 'default';
    public \$helpurl = 'https://help.joomla.org/proxy?keyref=Help{major}{minor}:{keyref}&lang={langcode}';
    public \$ftp_host = '';
    public \$ftp_port = '';
    public \$ftp_user = '';
    public \$ftp_pass = '';
    public \$ftp_root = '';
    public \$ftp_enable = '0';
    public \$offset = 'UTC';
    public \$mailonline = '1';
    public \$mailer = 'mail';
    public \$mailfrom = '<EMAIL>';
    public \$fromname = 'SoloYlibre';
    public \$sendmail = '/usr/sbin/sendmail';
    public \$smtpauth = '0';
    public \$smtpuser = '';
    public \$smtppass = '';
    public \$smtphost = 'localhost';
    public \$smtpsecure = 'none';
    public \$smtpport = '25';
    public \$caching = '0';
    public \$cache_handler = 'file';
    public \$cachetime = '15';
    public \$cache_platformprefix = '0';
    public \$MetaDesc = 'SoloYlibre business platform powered by JEYKO AI division';
    public \$MetaKeys = 'SoloYlibre, JEYKO, AI, business, platform';
    public \$MetaTitle = '1';
    public \$MetaAuthor = '1';
    public \$MetaVersion = '0';
    public \$robots = '';
    public \$sef = '1';
    public \$sef_rewrite = '0';
    public \$sef_suffix = '0';
    public \$unicodeslugs = '0';
    public \$feed_limit = '10';
    public \$feed_email = 'none';
    public \$log_path = '/var/www/html/administrator/logs';
    public \$tmp_path = '/var/www/html/tmp';
    public \$lifetime = '15';
    public \$session_handler = 'database';
    public \$shared_session = '0';
}
EOF

chown www-data:www-data configuration.php
chmod 644 configuration.php
"

print_success "Joomla configuration file created"

# Remove installation directory if it exists
docker exec josetusabe-joomla rm -rf /var/www/html/installation 2>/dev/null || true

print_success "Installation directory removed"

# Test the installation
print_status "Testing Joomla installation..."
sleep 5

if curl -s http://localhost:8102 | grep -q "SoloYlibre\|Joomla"; then
    print_success "Joomla is working!"
else
    print_error "Joomla may need additional configuration"
fi

echo ""
echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║                 JOOMLA DIRECT INSTALLATION COMPLETE         ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║  🌐 Frontend: http://localhost:8102                        ║${NC}"
echo -e "${GREEN}║  🔧 Admin: http://localhost:8102/administrator             ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║  🔐 LOGIN CREDENTIALS:                                     ║${NC}"
echo -e "${GREEN}║  Username: SoloYlibre                                       ║${NC}"
echo -e "${GREEN}║  Password: 57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd              ║${NC}"
echo -e "${GREEN}║  Email: <EMAIL>                                ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║  🗄️ DATABASE CONFIGURATION:                               ║${NC}"
echo -e "${GREEN}║  Type: MySQLi                                               ║${NC}"
echo -e "${GREEN}║  Host: soloylibre-mysql-joomla                             ║${NC}"
echo -e "${GREEN}║  Database: joomla_db                                       ║${NC}"
echo -e "${GREEN}║  User: joomla_user                                         ║${NC}"
echo -e "${GREEN}║  Password: SoloYlibre_Joomla_2024!                         ║${NC}"
echo -e "${GREEN}║  Prefix: sol_                                              ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║  🏢 Company: SoloYlibre                                    ║${NC}"
echo -e "${GREEN}║  🤖 AI Division: JEYKO                                     ║${NC}"
echo -e "${GREEN}║  👨‍💻 Head Developer: Jose L Encarnacion                    ║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
