version: '3.8'

services:
  # Ollama - Local LLM Server
  ollama:
    image: ollama/ollama:latest
    container_name: josetusabe-ollama
    ports:
      - "11434:11434"
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_ORIGINS=*
    volumes:
      - ollama_data:/root/.ollama
      - ./ollama/models:/models
    networks:
      - ai-network
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ollama.rule=Host(`ollama.localhost`) || Host(`ollama.soloylibre.com`)"
      - "traefik.http.routers.ollama.entrypoints=web,websecure"
      - "traefik.http.routers.ollama.tls=true"
      - "traefik.http.services.ollama.loadbalancer.server.port=11434"

  # LlamaGPT - Web Interface for Ollama
  llamagpt:
    image: ghcr.io/getumbrel/llama-gpt:latest
    container_name: josetusabe-llamagpt
    ports:
      - "3001:3000"
    environment:
      - OLLAMA_HOST=http://ollama:11434
      - MODEL=llama2:7b
      - NEXT_PUBLIC_DEFAULT_MODEL=llama2:7b
    volumes:
      - llamagpt_data:/app/chat
    networks:
      - ai-network
    depends_on:
      - ollama
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.llamagpt.rule=Host(`ai.localhost`) || Host(`ai.soloylibre.com`)"
      - "traefik.http.routers.llamagpt.entrypoints=web,websecure"
      - "traefik.http.routers.llamagpt.tls=true"
      - "traefik.http.services.llamagpt.loadbalancer.server.port=3000"

  # Reduced - AI Model Optimization
  reduced:
    build:
      context: ./reduced
      dockerfile: Dockerfile
    container_name: josetusabe-reduced
    ports:
      - "8950:8000"
    environment:
      - OLLAMA_URL=http://ollama:11434
      - REDIS_URL=redis://redis-ai:6379
      - MODEL_CACHE_DIR=/app/cache
    volumes:
      - reduced_data:/app/cache
      - ./reduced/models:/app/models
    networks:
      - ai-network
    depends_on:
      - ollama
      - redis-ai
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.reduced.rule=Host(`reduced.localhost`) || Host(`reduced.soloylibre.com`)"
      - "traefik.http.routers.reduced.entrypoints=web,websecure"
      - "traefik.http.routers.reduced.tls=true"

  # n8n - Workflow Automation
  n8n:
    image: n8nio/n8n:latest
    container_name: josetusabe-n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=josetusabe
      - N8N_BASIC_AUTH_PASSWORD=josetusabe123
      - N8N_HOST=n8n.soloylibre.com
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - WEBHOOK_URL=https://n8n.soloylibre.com
      - GENERIC_TIMEZONE=America/New_York
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres-ai
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8n123
      # Email Configuration for Dynu
      - N8N_EMAIL_MODE=smtp
      - N8N_SMTP_HOST=smtp.dynu.com
      - N8N_SMTP_PORT=587
      - N8N_SMTP_USER=${DYNU_EMAIL_USER}
      - N8N_SMTP_PASS=${DYNU_EMAIL_PASS}
      - N8N_SMTP_SENDER=${DYNU_EMAIL_FROM}
      - N8N_SMTP_SSL=false
      - N8N_SMTP_TLS=true
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n/workflows:/home/<USER>/.n8n/workflows
      - ./n8n/credentials:/home/<USER>/.n8n/credentials
    networks:
      - ai-network
    depends_on:
      - postgres-ai
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.n8n.rule=Host(`n8n.localhost`) || Host(`n8n.soloylibre.com`)"
      - "traefik.http.routers.n8n.entrypoints=web,websecure"
      - "traefik.http.routers.n8n.tls=true"
      - "traefik.http.services.n8n.loadbalancer.server.port=5678"

  # PostgreSQL for AI Services
  postgres-ai:
    image: postgres:15-alpine
    container_name: josetusabe-postgres-ai
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=ai_services
      - POSTGRES_USER=ai_user
      - POSTGRES_PASSWORD=ai_pass_123
      - POSTGRES_MULTIPLE_DATABASES=n8n,ollama,llamagpt
    volumes:
      - postgres_ai_data:/var/lib/postgresql/data
      - ./ai-services/database/init:/docker-entrypoint-initdb.d
    networks:
      - ai-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_user"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for AI Services
  redis-ai:
    image: redis:7-alpine
    container_name: josetusabe-redis-ai
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_ai_data:/data
    networks:
      - ai-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Email Relay Service for Dynu Integration
  email-relay:
    image: boky/postfix:latest
    container_name: josetusabe-email-relay
    ports:
      - "1587:587"
    environment:
      # Dynu SMTP Configuration
      - RELAYHOST=smtp.dynu.com:587
      - RELAYHOST_USERNAME=${DYNU_EMAIL_USER}
      - RELAYHOST_PASSWORD=${DYNU_EMAIL_PASS}
      - ALLOWED_SENDER_DOMAINS=soloylibre.com,josetusabe.com,1and1photo.com,joselencarnacion.com
      - HOSTNAME=mail.soloylibre.com
      - MESSAGE_SIZE_LIMIT=52428800
      - POSTFIX_myhostname=mail.soloylibre.com
      - POSTFIX_mydomain=soloylibre.com
      - POSTFIX_myorigin=soloylibre.com
      - POSTFIX_smtp_use_tls=yes
      - POSTFIX_smtp_sasl_auth_enable=yes
      - POSTFIX_smtp_sasl_security_options=noanonymous
      - POSTFIX_smtp_tls_security_level=encrypt
    volumes:
      - email_relay_data:/var/spool/postfix
      - ./email/logs:/var/log/postfix
    networks:
      - ai-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.tcp.routers.smtp.rule=HostSNI(`*`)"
      - "traefik.tcp.routers.smtp.entrypoints=smtp"
      - "traefik.tcp.services.smtp.loadbalancer.server.port=587"

  # AI Model Manager
  ai-model-manager:
    build:
      context: ./ai-model-manager
      dockerfile: Dockerfile
    container_name: josetusabe-ai-manager
    ports:
      - "8951:8000"
    environment:
      - OLLAMA_URL=http://ollama:11434
      - DATABASE_URL=*************************************************/ai_services
      - REDIS_URL=redis://redis-ai:6379
      - MODEL_STORAGE_PATH=/app/models
      - HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}
    volumes:
      - ai_models_data:/app/models
      - ./ai-model-manager/config:/app/config
    networks:
      - ai-network
    depends_on:
      - ollama
      - postgres-ai
      - redis-ai
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ai-manager.rule=Host(`ai-manager.localhost`) || Host(`ai-manager.soloylibre.com`)"
      - "traefik.http.routers.ai-manager.entrypoints=web,websecure"
      - "traefik.http.routers.ai-manager.tls=true"

  # AI Monitoring Dashboard
  ai-monitoring:
    image: grafana/grafana:latest
    container_name: josetusabe-ai-monitoring
    ports:
      - "3002:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=josetusabe123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=https://ai-monitor.soloylibre.com
      - GF_DATABASE_TYPE=postgres
      - GF_DATABASE_HOST=postgres-ai:5432
      - GF_DATABASE_NAME=grafana_ai
      - GF_DATABASE_USER=ai_user
      - GF_DATABASE_PASSWORD=ai_pass_123
    volumes:
      - ai_grafana_data:/var/lib/grafana
      - ./ai-services/monitoring/dashboards:/etc/grafana/provisioning/dashboards
      - ./ai-services/monitoring/datasources:/etc/grafana/provisioning/datasources
    networks:
      - ai-network
    depends_on:
      - postgres-ai
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ai-monitoring.rule=Host(`ai-monitor.localhost`) || Host(`ai-monitor.soloylibre.com`)"
      - "traefik.http.routers.ai-monitoring.entrypoints=web,websecure"
      - "traefik.http.routers.ai-monitoring.tls=true"

volumes:
  ollama_data:
  llamagpt_data:
  reduced_data:
  n8n_data:
  postgres_ai_data:
  redis_ai_data:
  email_relay_data:
  ai_models_data:
  ai_grafana_data:

networks:
  ai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
