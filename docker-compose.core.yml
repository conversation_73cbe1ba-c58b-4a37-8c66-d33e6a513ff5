version: '3.8'

services:
  # Traefik Reverse Proxy
  traefik:
    image: traefik:v3.0
    container_name: josetusabe-traefik
    ports:
      - "8081:80"
      - "8443:443"
      - "8082:8080"
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - core-network
    restart: unless-stopped

  # PostgreSQL Master
  postgres-master:
    image: postgres:15-alpine
    container_name: josetusabe-postgres-master
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=master_db
      - POSTGRES_USER=master_user
      - POSTGRES_PASSWORD=JoseTusabe_Master_2024!
    volumes:
      - postgres_master_data:/var/lib/postgresql/data
    networks:
      - core-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U master_user"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Master
  redis-master:
    image: redis:7-alpine
    container_name: josetusabe-redis-master
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --requirepass Redis_Master_2024!
    volumes:
      - redis_master_data:/data
    networks:
      - core-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "Redis_Master_2024!", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO Storage
  minio:
    image: minio/minio:latest
    container_name: josetusabe-minio
    ports:
      - "9002:9000"
      - "9003:9001"
    environment:
      - MINIO_ROOT_USER=josetusabe
      - MINIO_ROOT_PASSWORD=MinIO_JoseTusabe_2024!
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - core-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.minio.rule=Host(`files.localhost`)"
      - "traefik.http.routers.minio.entrypoints=web"
      - "traefik.http.services.minio.loadbalancer.server.port=9001"

  # Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: josetusabe-prometheus
    ports:
      - "9091:9090"
    volumes:
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.time=90d'
    networks:
      - core-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prometheus.localhost`)"
      - "traefik.http.routers.prometheus.entrypoints=web"

  # Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: josetusabe-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=Grafana_JoseTusabe_2024!
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=http://grafana.localhost
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - core-network
    depends_on:
      - prometheus
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.localhost`)"
      - "traefik.http.routers.grafana.entrypoints=web"

  # Jaeger
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: josetusabe-jaeger
    ports:
      - "16687:16686"
      - "14269:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - core-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.jaeger.rule=Host(`jaeger.localhost`)"
      - "traefik.http.routers.jaeger.entrypoints=web"
      - "traefik.http.services.jaeger.loadbalancer.server.port=16686"

volumes:
  postgres_master_data:
  redis_master_data:
  minio_data:
  prometheus_data:
  grafana_data:

networks:
  core-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
