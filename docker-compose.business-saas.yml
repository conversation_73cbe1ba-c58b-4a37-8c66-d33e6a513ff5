version: '3.8'

services:
  # Zonos - Tax & Duty Calculation
  zonos:
    image: node:18-alpine
    container_name: josetusabe-zonos
    ports:
      - "8960:3000"
    environment:
      - ZONOS_API_KEY=${ZONOS_API_KEY}
      - ZONOS_WEBHOOK_URL=https://zonos.soloylibre.com/webhook
      - DATABASE_URL=*******************************************************************/zonos
      - REDIS_URL=redis://redis-business:6379/1
    volumes:
      - ./zonos:/app
      - zonos_data:/app/data
    working_dir: /app
    command: >
      sh -c "
        npm install express axios pg redis &&
        node server.js
      "
    networks:
      - business-network
    depends_on:
      - postgres-business
      - redis-business
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.zonos.rule=Host(`zonos.soloylibre.com`)"
      - "traefik.http.routers.zonos.entrypoints=websecure"
      - "traefik.http.routers.zonos.tls=true"

  # Voice React Agent - AI Voice Assistant
  voice-react-agent:
    build:
      context: ./voice-react-agent
      dockerfile: Dockerfile
    container_name: josetusabe-voice-agent
    ports:
      - "8961:3000"
      - "8962:8000"  # WebSocket for real-time voice
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - OLLAMA_URL=http://josetusabe-ollama:11434
      - VOICE_MODEL=eleven_multilingual_v2
      - STT_MODEL=whisper-1
      - TTS_MODEL=tts-1
      - REACT_APP_WS_URL=ws://localhost:8962
    volumes:
      - ./voice-react-agent:/app
      - voice_agent_data:/app/recordings
    networks:
      - business-network
      - ai-network
    depends_on:
      - ollama
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.voice-agent.rule=Host(`voice.soloylibre.com`)"
      - "traefik.http.routers.voice-agent.entrypoints=websecure"
      - "traefik.http.routers.voice-agent.tls=true"

  # Themer - Dynamic Theme Management
  themer:
    image: node:18-alpine
    container_name: josetusabe-themer
    ports:
      - "8963:3000"
    environment:
      - DATABASE_URL=*******************************************************************/themer
      - REDIS_URL=redis://redis-business:6379/2
      - THEME_STORAGE_PATH=/app/themes
      - CDN_URL=https://files.soloylibre.com/themes
    volumes:
      - ./themer:/app
      - themer_data:/app/themes
      - ./for-migration:/app/migration-themes:ro
    working_dir: /app
    command: >
      sh -c "
        npm install express multer sharp pg redis sass &&
        node server.js
      "
    networks:
      - business-network
    depends_on:
      - postgres-business
      - redis-business
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.themer.rule=Host(`themes.soloylibre.com`)"
      - "traefik.http.routers.themer.entrypoints=websecure"
      - "traefik.http.routers.themer.tls=true"

  # GoSub - Browser Engine
  gosub:
    build:
      context: ./gosub
      dockerfile: Dockerfile
    container_name: josetusabe-gosub
    ports:
      - "8964:8080"
    environment:
      - GOSUB_CONFIG_PATH=/app/config
      - GOSUB_CACHE_DIR=/app/cache
      - GOSUB_LOG_LEVEL=info
    volumes:
      - ./gosub:/app
      - gosub_data:/app/cache
    networks:
      - business-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.gosub.rule=Host(`browser.soloylibre.com`)"
      - "traefik.http.routers.gosub.entrypoints=websecure"
      - "traefik.http.routers.gosub.tls=true"

  # NCP (Nextcloud Plus) - File Management & Collaboration
  nextcloud:
    image: nextcloud:28-apache
    container_name: josetusabe-nextcloud
    ports:
      - "8965:80"
    environment:
      - POSTGRES_DB=nextcloud
      - POSTGRES_USER=business_user
      - POSTGRES_PASSWORD=business_pass_123
      - POSTGRES_HOST=postgres-business
      - NEXTCLOUD_ADMIN_USER=josetusabe
      - NEXTCLOUD_ADMIN_PASSWORD=josetusabe123
      - NEXTCLOUD_TRUSTED_DOMAINS=ncp.soloylibre.com localhost
      - REDIS_HOST=redis-business
      - REDIS_HOST_PORT=6379
      - REDIS_HOST_PASSWORD=business_redis_123
    volumes:
      - nextcloud_data:/var/www/html
      - nextcloud_apps:/var/www/html/custom_apps
      - nextcloud_config:/var/www/html/config
      - ./nextcloud/data:/var/www/html/data
    networks:
      - business-network
    depends_on:
      - postgres-business
      - redis-business
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.nextcloud.rule=Host(`ncp.soloylibre.com`)"
      - "traefik.http.routers.nextcloud.entrypoints=websecure"
      - "traefik.http.routers.nextcloud.tls=true"

  # Docmost - Documentation Platform
  docmost:
    image: docmost/docmost:latest
    container_name: josetusabe-docmost
    ports:
      - "8966:3000"
    environment:
      - APP_URL=https://docs.soloylibre.com
      - DATABASE_URL=*******************************************************************/docmost
      - REDIS_URL=redis://redis-business:6379/3
      - APP_SECRET=${DOCMOST_APP_SECRET}
      - MAIL_DRIVER=smtp
      - MAIL_HOST=smtp.dynu.com
      - MAIL_PORT=587
      - MAIL_USERNAME=${DYNU_EMAIL_USER}
      - MAIL_PASSWORD=${DYNU_EMAIL_PASS}
      - MAIL_FROM_ADDRESS=${DYNU_EMAIL_FROM}
    volumes:
      - docmost_data:/app/data
      - ./docmost/uploads:/app/uploads
    networks:
      - business-network
    depends_on:
      - postgres-business
      - redis-business
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.docmost.rule=Host(`docs.soloylibre.com`)"
      - "traefik.http.routers.docmost.entrypoints=websecure"
      - "traefik.http.routers.docmost.tls=true"

  # Strapi - Headless CMS
  strapi:
    image: strapi/strapi:latest
    container_name: josetusabe-strapi
    ports:
      - "8967:1337"
    environment:
      - DATABASE_CLIENT=postgres
      - DATABASE_HOST=postgres-business
      - DATABASE_PORT=5432
      - DATABASE_NAME=strapi
      - DATABASE_USERNAME=business_user
      - DATABASE_PASSWORD=business_pass_123
      - JWT_SECRET=${STRAPI_JWT_SECRET}
      - ADMIN_JWT_SECRET=${STRAPI_ADMIN_JWT_SECRET}
      - APP_KEYS=${STRAPI_APP_KEYS}
      - API_TOKEN_SALT=${STRAPI_API_TOKEN_SALT}
      - TRANSFER_TOKEN_SALT=${STRAPI_TRANSFER_TOKEN_SALT}
      - SMTP_HOST=smtp.dynu.com
      - SMTP_PORT=587
      - SMTP_USERNAME=${DYNU_EMAIL_USER}
      - SMTP_PASSWORD=${DYNU_EMAIL_PASS}
    volumes:
      - strapi_data:/opt/app
      - ./strapi/config:/opt/app/config
      - ./strapi/src:/opt/app/src
      - ./strapi/public:/opt/app/public
    networks:
      - business-network
    depends_on:
      - postgres-business
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.strapi.rule=Host(`cms.soloylibre.com`)"
      - "traefik.http.routers.strapi.entrypoints=websecure"
      - "traefik.http.routers.strapi.tls=true"

  # NocoDB - No-Code Database
  nocodb:
    image: nocodb/nocodb:latest
    container_name: josetusabe-nocodb
    ports:
      - "8968:8080"
    environment:
      - NC_DB=pg://postgres-business:5432?u=business_user&p=business_pass_123&d=nocodb
      - NC_REDIS_URL=redis://redis-business:6379/4
      - NC_PUBLIC_URL=https://db.soloylibre.com
      - NC_ADMIN_EMAIL=${DYNU_EMAIL_USER}
      - NC_ADMIN_PASSWORD=josetusabe123
      - NC_JWT_EXPIRES_IN=10h
      - NC_SMTP_HOST=smtp.dynu.com
      - NC_SMTP_PORT=587
      - NC_SMTP_USERNAME=${DYNU_EMAIL_USER}
      - NC_SMTP_PASSWORD=${DYNU_EMAIL_PASS}
      - NC_SMTP_FROM=${DYNU_EMAIL_FROM}
      - NC_SMTP_SECURE=false
    volumes:
      - nocodb_data:/usr/app/data
    networks:
      - business-network
    depends_on:
      - postgres-business
      - redis-business
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.nocodb.rule=Host(`db.soloylibre.com`)"
      - "traefik.http.routers.nocodb.entrypoints=websecure"
      - "traefik.http.routers.nocodb.tls=true"

  # FeedHive - Social Media Management
  feedhive:
    build:
      context: ./feedhive
      dockerfile: Dockerfile
    container_name: josetusabe-feedhive
    ports:
      - "8969:3000"
    environment:
      - DATABASE_URL=*******************************************************************/feedhive
      - REDIS_URL=redis://redis-business:6379/5
      - TWITTER_API_KEY=${TWITTER_API_KEY}
      - TWITTER_API_SECRET=${TWITTER_API_SECRET}
      - FACEBOOK_APP_ID=${FACEBOOK_APP_ID}
      - FACEBOOK_APP_SECRET=${FACEBOOK_APP_SECRET}
      - INSTAGRAM_ACCESS_TOKEN=${INSTAGRAM_ACCESS_TOKEN}
      - LINKEDIN_CLIENT_ID=${LINKEDIN_CLIENT_ID}
      - LINKEDIN_CLIENT_SECRET=${LINKEDIN_CLIENT_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SMTP_HOST=smtp.dynu.com
      - SMTP_PORT=587
      - SMTP_USER=${DYNU_EMAIL_USER}
      - SMTP_PASS=${DYNU_EMAIL_PASS}
    volumes:
      - feedhive_data:/app/data
      - ./feedhive/uploads:/app/uploads
    networks:
      - business-network
    depends_on:
      - postgres-business
      - redis-business
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.feedhive.rule=Host(`social.soloylibre.com`)"
      - "traefik.http.routers.feedhive.entrypoints=websecure"
      - "traefik.http.routers.feedhive.tls=true"

  # Make.com Alternative - Automation Platform
  make-alternative:
    image: n8nio/n8n:latest
    container_name: josetusabe-make-alt
    ports:
      - "8970:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=josetusabe
      - N8N_BASIC_AUTH_PASSWORD=josetusabe123
      - N8N_HOST=make.soloylibre.com
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - WEBHOOK_URL=https://make.soloylibre.com
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres-business
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=make_alt
      - DB_POSTGRESDB_USER=business_user
      - DB_POSTGRESDB_PASSWORD=business_pass_123
      - N8N_SMTP_HOST=smtp.dynu.com
      - N8N_SMTP_PORT=587
      - N8N_SMTP_USER=${DYNU_EMAIL_USER}
      - N8N_SMTP_PASS=${DYNU_EMAIL_PASS}
      - N8N_SMTP_SENDER=${DYNU_EMAIL_FROM}
    volumes:
      - make_alt_data:/home/<USER>/.n8n
      - ./make-alternative/workflows:/home/<USER>/.n8n/workflows
    networks:
      - business-network
    depends_on:
      - postgres-business
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.make-alt.rule=Host(`make.soloylibre.com`)"
      - "traefik.http.routers.make-alt.entrypoints=websecure"
      - "traefik.http.routers.make-alt.tls=true"

  # PostgreSQL for Business Services
  postgres-business:
    image: postgres:15-alpine
    container_name: josetusabe-postgres-business
    ports:
      - "5434:5432"
    environment:
      - POSTGRES_DB=business_services
      - POSTGRES_USER=business_user
      - POSTGRES_PASSWORD=business_pass_123
      - POSTGRES_MULTIPLE_DATABASES=zonos,themer,nextcloud,docmost,strapi,nocodb,feedhive,make_alt
    volumes:
      - postgres_business_data:/var/lib/postgresql/data
      - ./business-services/database/init:/docker-entrypoint-initdb.d
    networks:
      - business-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U business_user"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Business Services
  redis-business:
    image: redis:7-alpine
    container_name: josetusabe-redis-business
    ports:
      - "6381:6379"
    command: redis-server --appendonly yes --requirepass business_redis_123
    volumes:
      - redis_business_data:/data
    networks:
      - business-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "business_redis_123", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  zonos_data:
  voice_agent_data:
  themer_data:
  gosub_data:
  nextcloud_data:
  nextcloud_apps:
  nextcloud_config:
  docmost_data:
  strapi_data:
  nocodb_data:
  feedhive_data:
  make_alt_data:
  postgres_business_data:
  redis_business_data:

networks:
  business-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
