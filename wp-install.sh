#!/bin/bash
# Wait for database
sleep 5

# Download WP-C<PERSON><PERSON>
curl -O https://raw.githubusercontent.com/wp-cli/wp-cli/v2.8.1/utils/wp-completion.bash
curl -O https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar
chmod +x wp-cli.phar
mv wp-cli.phar /usr/local/bin/wp

# Install WordPress
cd /var/www/html
wp core install \
    --url="http://localhost:8100" \
    --title="SoloYlibre - Ultimate Business Platform" \
    --admin_user="SoloYlibre" \
    --admin_password="57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd" \
    --admin_email="<EMAIL>" \
    --allow-root || echo "WordPress may already be installed"

# Enable multisite
wp core multisite-convert \
    --title="SoloYlibre Network" \
    --allow-root || echo "Multisite may already be enabled"

# Create JEYKO subsite
wp site create \
    --slug="jeyko" \
    --title="JEYKO - AI Division" \
    --email="<EMAIL>" \
    --allow-root || echo "JEYKO site may already exist"

echo "WordPress setup completed"
