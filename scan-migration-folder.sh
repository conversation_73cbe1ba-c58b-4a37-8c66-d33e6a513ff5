#!/bin/bash

# Migration Folder Scanner
# Professional 2000% IQ Discovery Tool for Jose L Encarnacion (JoseTusabe)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCAN_REPORT="migration_scan_report.md"
PROJECTS_LIST="projects_inventory.json"

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                  MIGRATION FOLDER SCANNER                   ║"
    echo "║              Professional 2000% IQ Discovery                ║"
    echo "║                Jose L Encarnacion (JoseTusabe)               ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${CYAN}[SCAN]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[FOUND]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Find migration folder
find_migration_folder() {
    print_status "Searching for 'for-migration' folder..."
    
    # Common locations to search
    SEARCH_PATHS=(
        "$HOME/Desktop"
        "$HOME/Documents"
        "$HOME/Downloads"
        "$HOME/Projects"
        "$HOME/Development"
        "$HOME"
        "/Users/<USER>/Desktop"
    )
    
    MIGRATION_FOLDER=""
    
    for path in "${SEARCH_PATHS[@]}"; do
        if [ -d "$path" ]; then
            print_status "Searching in: $path"
            FOUND=$(find "$path" -name "for-migration" -type d 2>/dev/null | head -1)
            if [ ! -z "$FOUND" ]; then
                MIGRATION_FOLDER="$FOUND"
                print_success "Found migration folder: $MIGRATION_FOLDER"
                break
            fi
        fi
    done
    
    if [ -z "$MIGRATION_FOLDER" ]; then
        print_error "Migration folder not found in common locations"
        echo "Please provide the full path to your 'for-migration' folder:"
        read -r MIGRATION_FOLDER
        
        if [ ! -d "$MIGRATION_FOLDER" ]; then
            print_error "Provided path does not exist: $MIGRATION_FOLDER"
            exit 1
        fi
    fi
    
    echo "$MIGRATION_FOLDER"
}

# Scan folder contents
scan_folder_contents() {
    local folder="$1"
    print_status "Scanning folder contents: $folder"
    
    # Initialize report
    cat > "$SCAN_REPORT" << EOF
# 📊 Migration Folder Scan Report
## Jose L Encarnacion (JoseTusabe) - $(date)

**Scanned Folder**: \`$folder\`

---

## 📁 Folder Structure
\`\`\`
EOF
    
    # Add folder tree
    if command -v tree &> /dev/null; then
        tree -L 3 "$folder" >> "$SCAN_REPORT"
    else
        find "$folder" -type d | head -20 >> "$SCAN_REPORT"
    fi
    
    cat >> "$SCAN_REPORT" << EOF
\`\`\`

---

## 📊 Statistics

EOF
    
    # Count files by type
    local total_files=$(find "$folder" -type f | wc -l)
    local css_files=$(find "$folder" -name "*.css" | wc -l)
    local js_files=$(find "$folder" -name "*.js" | wc -l)
    local php_files=$(find "$folder" -name "*.php" | wc -l)
    local html_files=$(find "$folder" -name "*.html" | wc -l)
    local json_files=$(find "$folder" -name "*.json" | wc -l)
    
    cat >> "$SCAN_REPORT" << EOF
| File Type | Count |
|-----------|-------|
| **Total Files** | $total_files |
| **CSS Files** | $css_files |
| **JavaScript Files** | $js_files |
| **PHP Files** | $php_files |
| **HTML Files** | $html_files |
| **JSON Files** | $json_files |

---

## 🎨 CSS Themes/Styles Found

EOF
    
    print_success "Found $css_files CSS files"
    
    # List CSS files
    find "$folder" -name "*.css" | while read -r css_file; do
        local rel_path=$(echo "$css_file" | sed "s|$folder/||")
        echo "- \`$rel_path\`" >> "$SCAN_REPORT"
    done
    
    cat >> "$SCAN_REPORT" << EOF

---

## 📋 Project Configuration Files

EOF
    
    # Find project configuration files
    find "$folder" -name "package.json" -o -name "composer.json" -o -name "requirements.txt" -o -name "Dockerfile" -o -name "docker-compose.yml" | while read -r config_file; do
        local rel_path=$(echo "$config_file" | sed "s|$folder/||")
        echo "- \`$rel_path\`" >> "$SCAN_REPORT"
    done
    
    cat >> "$SCAN_REPORT" << EOF

---

## 🔍 Audit Files Found

EOF
    
    # Find audit files
    find "$folder" -name "*audit*" -type f | while read -r audit_file; do
        local rel_path=$(echo "$audit_file" | sed "s|$folder/||")
        echo "- \`$rel_path\`" >> "$SCAN_REPORT"
    done
    
    cat >> "$SCAN_REPORT" << EOF

---

## 📁 Potential Project Folders

EOF
    
    # Find potential project folders (containing multiple file types)
    find "$folder" -type d -mindepth 1 -maxdepth 3 | while read -r dir; do
        local file_count=$(find "$dir" -maxdepth 1 -type f | wc -l)
        if [ "$file_count" -gt 5 ]; then
            local rel_path=$(echo "$dir" | sed "s|$folder/||")
            local has_config=$(find "$dir" -maxdepth 1 -name "package.json" -o -name "composer.json" -o -name "index.php" -o -name "index.html" | wc -l)
            if [ "$has_config" -gt 0 ]; then
                echo "- 🎯 **\`$rel_path\`** ($file_count files) - *Likely project*" >> "$SCAN_REPORT"
            else
                echo "- 📁 \`$rel_path\` ($file_count files)" >> "$SCAN_REPORT"
            fi
        fi
    done
}

# Create projects inventory
create_projects_inventory() {
    local folder="$1"
    print_status "Creating projects inventory..."
    
    cat > "$PROJECTS_LIST" << EOF
{
  "scan_date": "$(date -Iseconds)",
  "migration_folder": "$folder",
  "domains": [
    "josetusabe.com",
    "soloylibre.com", 
    "1and1photo.com",
    "joselencarnacion.com"
  ],
  "projects": [
EOF
    
    local first=true
    find "$folder" -type d -mindepth 1 -maxdepth 3 | while read -r dir; do
        local config_files=$(find "$dir" -maxdepth 1 -name "package.json" -o -name "composer.json" -o -name "index.php" -o -name "index.html")
        if [ ! -z "$config_files" ]; then
            local rel_path=$(echo "$dir" | sed "s|$folder/||")
            local file_count=$(find "$dir" -type f | wc -l)
            
            if [ "$first" = true ]; then
                first=false
            else
                echo "," >> "$PROJECTS_LIST"
            fi
            
            cat >> "$PROJECTS_LIST" << EOF
    {
      "name": "$(basename "$dir")",
      "path": "$rel_path",
      "full_path": "$dir",
      "file_count": $file_count,
      "config_files": [$(echo "$config_files" | sed 's|.*/||' | sed 's/^/"/;s/$/"/' | tr '\n' ',' | sed 's/,$//')],
      "estimated_domain": "unknown",
      "priority": 0,
      "technology": "unknown"
    }EOF
        fi
    done
    
    cat >> "$PROJECTS_LIST" << EOF

  ]
}
EOF
    
    print_success "Projects inventory created: $PROJECTS_LIST"
}

# Generate recommendations
generate_recommendations() {
    print_status "Generating migration recommendations..."
    
    cat >> "$SCAN_REPORT" << EOF

---

## 🚀 Migration Recommendations

### 📋 Next Steps Required:
1. **Review project folders** marked as "Likely project"
2. **Identify domain mapping** for each project
3. **Determine technology stack** for each project
4. **Set migration priority** (1-10) for each project
5. **Check for database dependencies**

### 🔧 Technical Analysis Needed:
- [ ] **WordPress projects** - Check for themes/plugins
- [ ] **Custom applications** - Identify framework used
- [ ] **Static sites** - HTML/CSS/JS analysis
- [ ] **Database requirements** - MySQL, PostgreSQL, etc.
- [ ] **File dependencies** - Images, uploads, assets

### 🐳 Containerization Strategy:
- [ ] **Group by domain** - One stack per domain
- [ ] **Shared services** - Database, Redis, monitoring
- [ ] **Port allocation** - Systematic port numbering
- [ ] **Volume mapping** - Persistent data storage

### ⚠️ Potential Issues:
- [ ] **Large file sizes** - Optimize before migration
- [ ] **Hardcoded paths** - Update for containers
- [ ] **Database connections** - Update connection strings
- [ ] **File permissions** - Docker user mapping

---

## 📞 Contact Information
**Developer**: Jose L Encarnacion (JoseTusabe)
**Domains**: josetusabe.com, soloylibre.com, 1and1photo.com, joselencarnacion.com
**Infrastructure**: Mac Pro M1 + Synology RS3618xs

---

*Report generated by Professional 2000% IQ Migration Scanner*
EOF
}

# Main function
main() {
    print_header
    
    # Find migration folder
    MIGRATION_FOLDER=$(find_migration_folder)
    
    if [ -z "$MIGRATION_FOLDER" ]; then
        print_error "Cannot proceed without migration folder"
        exit 1
    fi
    
    # Scan contents
    scan_folder_contents "$MIGRATION_FOLDER"
    
    # Create inventory
    create_projects_inventory "$MIGRATION_FOLDER"
    
    # Generate recommendations
    generate_recommendations
    
    print_success "Scan complete! Reports generated:"
    echo "  📊 Scan Report: $SCAN_REPORT"
    echo "  📋 Projects Inventory: $PROJECTS_LIST"
    echo ""
    print_status "Please review the reports and provide:"
    echo "  1. Domain mapping for each project"
    echo "  2. Technology identification"
    echo "  3. Migration priority (1-10)"
    echo "  4. Special requirements"
}

# Run main function
main "$@"
