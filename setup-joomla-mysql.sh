#!/bin/bash

# Setup Joomla with MySQL for SoloYlibre & JEYKO
# Head Developer: <PERSON> Encarnacion

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[JOOMLA SETUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Setting up MySQL for Joomla..."

# Create MySQL container for <PERSON><PERSON><PERSON>
docker run -d \
    --name soloylibre-mysql-joomla \
    --network ultimate_dev_env_cms-network \
    -e MYSQL_ROOT_PASSWORD=SoloYlibre_MySQL_Root_2024! \
    -e MYSQL_DATABASE=joomla_db \
    -e MYSQL_USER=joomla_user \
    -e MYSQL_PASSWORD=SoloYlibre_Joomla_2024! \
    -p 3307:3306 \
    mysql:8.0 \
    --default-authentication-plugin=mysql_native_password

print_success "MySQL container created for Joomla"

# Wait for MySQL to be ready
print_status "Waiting for MySQL to be ready..."
sleep 30

# Test MySQL connection
docker exec soloylibre-mysql-joomla mysql -u joomla_user -pSoloYlibre_Joomla_2024! -e "SELECT 1;" || print_error "MySQL connection test failed"

print_success "MySQL is ready for Joomla"

# Restart Joomla container to ensure network connectivity
print_status "Restarting Joomla container..."
docker restart josetusabe-joomla

print_success "Joomla container restarted"

# Wait for Joomla to be ready
sleep 10

print_status "Testing Joomla connectivity..."
curl -s http://localhost:8102 > /dev/null && print_success "Joomla is responding" || print_error "Joomla is not responding"

echo ""
echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║                 JOOMLA MYSQL SETUP COMPLETE                 ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║  🌐 Joomla URL: http://localhost:8102                      ║${NC}"
echo -e "${GREEN}║  🗄️ Database: MySQL (ready for installation)              ║${NC}"
echo -e "${GREEN}║                                                              ║${NC}"
echo -e "${GREEN}║  Use the database configuration provided next...            ║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
