#!/bin/bash

# COMPLETE INSTALLATION & SETUP - SoloYlibre & JEYKO Ultimate Environment
# Installs all dependencies, sets up all services, and verifies everything is working

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║           COMPLETE INSTALLATION & SETUP                     ║"
    echo "║              SOLOYLIBRE & JEYKO                             ║"
    echo "║                                                              ║"
    echo "║  🚀 Installing all dependencies and setting up services     ║"
    echo "║  🌐 Complete environment migration and configuration        ║"
    echo "║  👨‍💻 Head Developer: Jose L Encarnacion                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${CYAN}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${GRE<PERSON>}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check system requirements
check_system_requirements() {
    print_status "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check available disk space (need at least 10GB)
    available_space=$(df . | tail -1 | awk '{print $4}')
    if [ "$available_space" -lt 10485760 ]; then
        print_warning "Low disk space detected. Recommend at least 10GB free space."
    fi
    
    print_success "System requirements check passed"
}

# Install Node.js dependencies
install_nodejs_dependencies() {
    print_status "Installing Node.js dependencies..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        print_status "Installing Node.js..."
        # Install Node.js using package manager (macOS)
        if [[ "$OSTYPE" == "darwin"* ]]; then
            if command -v brew &> /dev/null; then
                brew install node
            else
                print_error "Please install Node.js manually from https://nodejs.org/"
                exit 1
            fi
        fi
    fi
    
    # Install global packages
    print_status "Installing global npm packages..."
    npm install -g pm2 nodemon typescript ts-node
    
    print_success "Node.js dependencies installed"
}

# Setup Docker networks
setup_docker_networks() {
    print_status "Setting up Docker networks..."
    
    # Create networks if they don't exist
    docker network create soloylibre-core 2>/dev/null || true
    docker network create soloylibre-cms 2>/dev/null || true
    docker network create soloylibre-ai 2>/dev/null || true
    docker network create soloylibre-app 2>/dev/null || true
    
    print_success "Docker networks configured"
}

# Start core services
start_core_services() {
    print_status "Starting core infrastructure services..."
    
    # Start core services (databases, monitoring)
    if [ -f "docker-compose.core.yml" ]; then
        docker-compose -f docker-compose.core.yml up -d
        print_success "Core services started"
    else
        print_warning "docker-compose.core.yml not found, skipping core services"
    fi
    
    # Wait for databases to be ready
    print_status "Waiting for databases to initialize..."
    sleep 30
}

# Start CMS suite
start_cms_suite() {
    print_status "Starting CMS suite services..."
    
    if [ -f "docker-compose.cms-suite.yml" ]; then
        docker-compose -f docker-compose.cms-suite.yml up -d
        print_success "CMS suite started"
    else
        print_warning "docker-compose.cms-suite.yml not found, skipping CMS suite"
    fi
    
    # Wait for CMS services to initialize
    print_status "Waiting for CMS services to initialize..."
    sleep 20
}

# Start AI services
start_ai_services() {
    print_status "Starting AI services..."
    
    if [ -f "docker-compose.ai-services.yml" ]; then
        docker-compose -f docker-compose.ai-services.yml up -d
        print_success "AI services started"
    elif [ -f "docker-compose.ai-simple.yml" ]; then
        docker-compose -f docker-compose.ai-simple.yml up -d
        print_success "AI simple services started"
    else
        print_warning "AI services compose file not found, skipping AI services"
    fi
}

# Start business SaaS services
start_business_services() {
    print_status "Starting business SaaS services..."
    
    if [ -f "docker-compose.business-saas.yml" ]; then
        docker-compose -f docker-compose.business-saas.yml up -d
        print_success "Business SaaS services started"
    else
        print_warning "docker-compose.business-saas.yml not found, skipping business services"
    fi
}

# Start premium SaaS services
start_premium_services() {
    print_status "Starting premium SaaS services..."
    
    if [ -f "docker-compose.premium-saas.yml" ]; then
        docker-compose -f docker-compose.premium-saas.yml up -d
        print_success "Premium SaaS services started"
    else
        print_warning "docker-compose.premium-saas.yml not found, skipping premium services"
    fi
}

# Install Portainer
install_portainer() {
    print_status "Installing Portainer..."
    
    if [ -f "docker-compose.portainer.yml" ]; then
        docker-compose -f docker-compose.portainer.yml up -d
        print_success "Portainer installed and started"
    elif [ -f "install-portainer.sh" ]; then
        chmod +x install-portainer.sh
        ./install-portainer.sh
        print_success "Portainer installed via script"
    else
        print_warning "Portainer installation files not found, skipping Portainer"
    fi
}

# Setup PostgreSQL and pgAdmin
setup_postgresql() {
    print_status "Setting up PostgreSQL and pgAdmin..."
    
    if [ -f "install-postgresql-and-audit.sh" ]; then
        chmod +x install-postgresql-and-audit.sh
        ./install-postgresql-and-audit.sh
        print_success "PostgreSQL setup completed"
    elif [ -f "auto-connect-postgresql.sh" ]; then
        chmod +x auto-connect-postgresql.sh
        ./auto-connect-postgresql.sh
        print_success "PostgreSQL auto-connection completed"
    else
        print_warning "PostgreSQL setup scripts not found"
    fi
}

# Complete Joomla installation
complete_joomla_setup() {
    print_status "Completing Joomla installation..."
    
    if [ -f "auto-complete-joomla-install.sh" ]; then
        chmod +x auto-complete-joomla-install.sh
        ./auto-complete-joomla-install.sh
        print_success "Joomla installation completed"
    elif [ -f "complete-joomla-fix.sh" ]; then
        chmod +x complete-joomla-fix.sh
        ./complete-joomla-fix.sh
        print_success "Joomla fix completed"
    else
        print_warning "Joomla installation scripts not found"
    fi
}

# Fix any service issues
fix_service_issues() {
    print_status "Fixing any service issues..."
    
    if [ -f "fix-all-services.sh" ]; then
        chmod +x fix-all-services.sh
        ./fix-all-services.sh
        print_success "Service fixes applied"
    fi
    
    if [ -f "fix-database-connections.sh" ]; then
        chmod +x fix-database-connections.sh
        ./fix-database-connections.sh
        print_success "Database connection fixes applied"
    fi
}

# Test all services
test_all_services() {
    print_status "Testing all services..."
    
    # Define services to test
    declare -A services=(
        ["pgAdmin"]="http://localhost:5050"
        ["WordPress"]="http://localhost:8100"
        ["Drupal"]="http://localhost:8101"
        ["Joomla"]="http://localhost:8102"
        ["AI Chat"]="http://localhost:3002"
        ["Themer"]="http://localhost:3004"
        ["Docmost"]="http://localhost:3003"
        ["NocoDB"]="http://localhost:8080"
        ["Grafana"]="http://localhost:3001"
        ["Prometheus"]="http://localhost:9091"
        ["MinIO"]="http://localhost:9003"
        ["Portainer"]="http://localhost:9000"
    )
    
    echo ""
    echo "🔍 Service Health Check Results:"
    echo "================================"
    
    for service in "${!services[@]}"; do
        url="${services[$service]}"
        if curl -s --max-time 5 "$url" > /dev/null 2>&1; then
            echo -e "✅ $service: ${GREEN}WORKING${NC} ($url)"
        else
            echo -e "❌ $service: ${RED}NOT RESPONDING${NC} ($url)"
        fi
    done
}

# Generate final documentation
generate_documentation() {
    print_status "Generating final documentation..."
    
    if [ -f "generate-documentation-summary.sh" ]; then
        chmod +x generate-documentation-summary.sh
        ./generate-documentation-summary.sh
        print_success "Documentation generated"
    fi
}

# Show final status
show_final_status() {
    echo ""
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║              🎉 INSTALLATION COMPLETE! 🎉                  ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🚀 SoloYlibre & JEYKO Ultimate Environment Ready!         ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  📊 Container Status:                                       ║${NC}"
    
    # Count running containers
    total_containers=$(docker ps --format "table {{.Names}}" | tail -n +2 | wc -l)
    echo -e "${GREEN}║  📦 Total Containers Running: $total_containers                        ║${NC}"
    
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🌐 Quick Access URLs:                                      ║${NC}"
    echo -e "${GREEN}║  • pgAdmin: http://localhost:5050                          ║${NC}"
    echo -e "${GREEN}║  • WordPress: http://localhost:8100                        ║${NC}"
    echo -e "${GREEN}║  • AI Chat: http://localhost:3002                          ║${NC}"
    echo -e "${GREEN}║  • Grafana: http://localhost:3001                          ║${NC}"
    echo -e "${GREEN}║  • Portainer: http://localhost:9000                        ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  🔐 Default Credentials:                                    ║${NC}"
    echo -e "${GREEN}║  • PostgreSQL: <EMAIL> / Encarnacion12@amd12  ║${NC}"
    echo -e "${GREEN}║  • SoloYlibre: SoloYlibre / 57w:tqf_UMd2kmogiVx!he+U}rXwhNHpd ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║  📋 Head Developer: Jose L Encarnacion                     ║${NC}"
    echo -e "${GREEN}║  🏢 Company: SoloYlibre & JEYKO                            ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    
    echo ""
    print_success "Complete installation and setup finished!"
    print_status "Check the generated documentation for detailed service information"
}

# Main execution
main() {
    print_header
    
    print_status "Starting complete installation and setup process..."
    
    check_system_requirements
    install_nodejs_dependencies
    setup_docker_networks
    
    print_status "Starting services in order..."
    start_core_services
    start_cms_suite
    start_ai_services
    start_business_services
    start_premium_services
    
    print_status "Installing additional components..."
    install_portainer
    setup_postgresql
    complete_joomla_setup
    
    print_status "Applying fixes and optimizations..."
    fix_service_issues
    
    print_status "Testing all services..."
    test_all_services
    
    print_status "Finalizing setup..."
    generate_documentation
    show_final_status
}

# Run the complete installation
main "$@"
