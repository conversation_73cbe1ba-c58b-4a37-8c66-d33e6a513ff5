#!/bin/bash

# Synology RS3618xs Kubernetes Setup
# Professional 2000% IQ Production Environment
# Jose L Encarnacion (JoseTusabe)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                SYNOLOGY K3S SETUP SCRIPT                    ║"
    echo "║              Professional 2000% IQ Production               ║"
    echo "║                Jose L Encarnacion (JoseTusabe)               ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_status() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SYNOLOGY_IP="*************"  # Update with your Synology IP
DOMAIN="soloylibre.com"
EMAIL="<EMAIL>"

# Check if running on Synology
check_synology() {
    print_status "Checking if running on Synology NAS..."
    
    if [ ! -f /etc/synoinfo.conf ]; then
        print_error "This script must be run on a Synology NAS"
        exit 1
    fi
    
    print_success "Synology NAS detected"
}

# Install k3s
install_k3s() {
    print_status "Installing k3s on Synology..."
    
    # Download and install k3s
    curl -sfL https://get.k3s.io | INSTALL_K3S_EXEC="--disable traefik --disable servicelb" sh -
    
    # Wait for k3s to be ready
    print_status "Waiting for k3s to be ready..."
    sleep 30
    
    # Check k3s status
    if systemctl is-active --quiet k3s; then
        print_success "k3s installed and running"
    else
        print_error "k3s installation failed"
        exit 1
    fi
}

# Install Helm
install_helm() {
    print_status "Installing Helm..."
    
    curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
    
    # Add common Helm repositories
    helm repo add stable https://charts.helm.sh/stable
    helm repo add bitnami https://charts.bitnami.com/bitnami
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo add grafana https://grafana.github.io/helm-charts
    helm repo add jaegertracing https://jaegertracing.github.io/helm-charts
    helm repo add traefik https://helm.traefik.io/traefik
    helm repo update
    
    print_success "Helm installed and repositories added"
}

# Create namespaces
create_namespaces() {
    print_status "Creating Kubernetes namespaces..."
    
    kubectl create namespace project1 --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace project2 --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace soloylibre --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace wordpress --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace monitoring --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace storage --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace ingress --dry-run=client -o yaml | kubectl apply -f -
    
    print_success "Namespaces created"
}

# Install Traefik Ingress Controller
install_traefik() {
    print_status "Installing Traefik Ingress Controller..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Namespace
metadata:
  name: traefik
---
apiVersion: helm.cattle.io/v1
kind: HelmChart
metadata:
  name: traefik
  namespace: kube-system
spec:
  chart: traefik
  repo: https://helm.traefik.io/traefik
  targetNamespace: traefik
  valuesContent: |-
    service:
      type: LoadBalancer
    ports:
      web:
        port: 80
      websecure:
        port: 443
    certificatesResolvers:
      letsencrypt:
        acme:
          email: ${EMAIL}
          storage: /data/acme.json
          httpChallenge:
            entryPoint: web
EOF
    
    print_success "Traefik installed"
}

# Install Prometheus Stack
install_prometheus() {
    print_status "Installing Prometheus monitoring stack..."
    
    helm install prometheus prometheus-community/kube-prometheus-stack \
        --namespace monitoring \
        --create-namespace \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=50Gi \
        --set grafana.adminPassword=josetusabe123 \
        --set grafana.ingress.enabled=true \
        --set grafana.ingress.hosts[0]=grafana.${DOMAIN} \
        --set prometheus.ingress.enabled=true \
        --set prometheus.ingress.hosts[0]=prometheus.${DOMAIN}
    
    print_success "Prometheus stack installed"
}

# Install PostgreSQL
install_postgresql() {
    print_status "Installing PostgreSQL cluster..."
    
    helm install postgresql bitnami/postgresql \
        --namespace storage \
        --create-namespace \
        --set auth.postgresPassword=josetusabe123 \
        --set auth.database=master_db \
        --set primary.persistence.size=100Gi \
        --set readReplicas.replicaCount=1 \
        --set readReplicas.persistence.size=100Gi
    
    print_success "PostgreSQL installed"
}

# Install Redis
install_redis() {
    print_status "Installing Redis cluster..."
    
    helm install redis bitnami/redis \
        --namespace storage \
        --set auth.password=josetusabe123 \
        --set cluster.enabled=true \
        --set cluster.slaveCount=2 \
        --set persistence.size=20Gi
    
    print_success "Redis cluster installed"
}

# Install MinIO
install_minio() {
    print_status "Installing MinIO object storage..."
    
    helm install minio bitnami/minio \
        --namespace storage \
        --set auth.rootUser=josetusabe \
        --set auth.rootPassword=josetusabe123 \
        --set persistence.size=500Gi \
        --set ingress.enabled=true \
        --set ingress.hostname=files.${DOMAIN}
    
    print_success "MinIO installed"
}

# Install Jaeger
install_jaeger() {
    print_status "Installing Jaeger tracing..."
    
    helm install jaeger jaegertracing/jaeger \
        --namespace monitoring \
        --set query.ingress.enabled=true \
        --set query.ingress.hosts[0]=jaeger.${DOMAIN}
    
    print_success "Jaeger installed"
}

# Create SSL certificates
setup_ssl() {
    print_status "Setting up SSL certificates..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: ${EMAIL}
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: traefik
EOF
    
    print_success "SSL certificates configured"
}

# Create ingress routes
create_ingress() {
    print_status "Creating ingress routes..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: main-ingress
  namespace: default
  annotations:
    kubernetes.io/ingress.class: traefik
    cert-manager.io/cluster-issuer: letsencrypt-prod
    traefik.ingress.kubernetes.io/redirect-entry-point: https
spec:
  tls:
  - hosts:
    - ${DOMAIN}
    - www.${DOMAIN}
    - api.${DOMAIN}
    - dashboard.${DOMAIN}
    secretName: main-tls
  rules:
  - host: ${DOMAIN}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: soloylibre-frontend
            port:
              number: 80
  - host: api.${DOMAIN}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 80
  - host: dashboard.${DOMAIN}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: admin-dashboard
            port:
              number: 80
EOF
    
    print_success "Ingress routes created"
}

# Setup monitoring
setup_monitoring() {
    print_status "Configuring monitoring and alerting..."
    
    # Create custom Grafana dashboards
    kubectl create configmap custom-dashboards \
        --from-file=./monitoring/dashboards/ \
        --namespace monitoring
    
    # Create Prometheus rules
    kubectl create configmap prometheus-rules \
        --from-file=./monitoring/rules/ \
        --namespace monitoring
    
    print_success "Monitoring configured"
}

# Main installation function
main() {
    print_header
    
    print_status "Starting Synology RS3618xs Kubernetes setup..."
    print_status "Domain: ${DOMAIN}"
    print_status "Email: ${EMAIL}"
    print_status "Synology IP: ${SYNOLOGY_IP}"
    
    check_synology
    install_k3s
    install_helm
    create_namespaces
    install_traefik
    install_prometheus
    install_postgresql
    install_redis
    install_minio
    install_jaeger
    setup_ssl
    create_ingress
    setup_monitoring
    
    print_success "Kubernetes setup completed!"
    echo ""
    echo -e "${GREEN}🎉 Your production environment is ready!${NC}"
    echo ""
    echo -e "${BLUE}📱 Access URLs:${NC}"
    echo "   • Main Site: https://${DOMAIN}"
    echo "   • API: https://api.${DOMAIN}"
    echo "   • Dashboard: https://dashboard.${DOMAIN}"
    echo "   • Grafana: https://grafana.${DOMAIN}"
    echo "   • Prometheus: https://prometheus.${DOMAIN}"
    echo "   • Jaeger: https://jaeger.${DOMAIN}"
    echo "   • Files: https://files.${DOMAIN}"
    echo ""
    echo -e "${YELLOW}⚠️  Next Steps:${NC}"
    echo "   1. Update DNS records to point to ${SYNOLOGY_IP}"
    echo "   2. Deploy your applications using kubectl or Helm"
    echo "   3. Configure backup and monitoring alerts"
    echo "   4. Set up CI/CD pipelines"
    echo ""
}

# Run main function
main "$@"
